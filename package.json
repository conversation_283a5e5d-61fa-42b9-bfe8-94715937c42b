{"name": "cury_cliente", "version": "0.1.1", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "SENTRY_SUPPRESS_TURBOPACK_WARNING=1 NODE_OPTIONS='--inspect=0.0.0.0:9230' next dev --turbopack", "dev-server": "NODE_OPTIONS='--inspect=0.0.0.0:9230' next dev", "dev-server-turbo": "SENTRY_SUPPRESS_TURBOPACK_WARNING=1 NODE_OPTIONS='--inspect=0.0.0.0:9230' next dev --turbopack", "dev-server-turbo-scan": "SENTRY_SUPPRESS_TURBOPACK_WARNING=1 NODE_OPTIONS='--inspect=0.0.0.0:9230' next dev --turbopack --scan", "dev-turbo": "SENTRY_SUPPRESS_TURBOPACK_WARNING=1 next dev --turbopack", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "lint": "eslint .", "lintfix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lintquiet": "eslint --quiet .", "start": "next start", "client:rsdoctor": "rsdoctor analyze --profile .next/.rsdoctor/manifest.json", "server:rsdoctor": "rsdoctor analyze --profile .next/server/chunks/.rsdoctor/manifest.json", "knip": "knip", "scan": "NODE_OPTIONS='--inspect=0.0.0.0:9230' next dev --turbopack & npx react-scan@latest localhost:3000"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "dependencies": {"@mona-health/react-input-mask": "3.0.3", "@next/bundle-analyzer": "15.3.0", "@next/third-parties": "15.3.0", "@sentry/nextjs": "9.5.0", "@tailwindcss/postcss": "4.0.17", "@use-gesture/react": "10.3.1", "autoprefixer": "10.4.21", "cep-promise": "^4.4.1", "framer-motion": "12.6.3", "html-react-parser": "5.2.6", "import-in-the-middle": "1.13.1", "jspdf": "3.0.1", "jspdf-autotable": "5.0.2", "lodash": "4.17.21", "million": "3.1.11", "motion": "12.23.12", "next": "15.3.0", "nookies": "^2.5.2", "postcss": "8.5.6", "react": "19.1.0", "react-calendar": "5.1.0", "react-dom": "19.1.0", "react-hook-form": "7.54.2", "react-scan": "0.3.3", "react-swipeable": "^7.0.1", "require-in-the-middle": "7.5.2", "swiper": "11.1.14", "tailwindcss": "4.1.3", "ua-parser-js": "2.0.3", "zod": "3.24.2"}, "devDependencies": {"@eslint/compat": "1.2.7", "@eslint/eslintrc": "3.3.0", "@eslint/js": "9.23.0", "@rsdoctor/cli": "1.0.1", "@types/node": "24.2.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@typescript-eslint/eslint-plugin": "8.39.0", "@typescript-eslint/parser": "8.39.0", "critters": "0.0.23", "eslint": "9.23.0", "eslint-config-next": "15.3.0", "eslint-plugin-react": "7.37.4", "husky": "9.1.7", "jiti": "2.4.2", "knip": "5.46.2", "prettier": "3.5.3", "typescript": "5.8.3"}, "overrides": {"@types/react": "19.1.0", "@types/react-dom": "19.1.2"}}