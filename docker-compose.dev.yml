version: '3.8'

services:
  cury-cliente-dev:
    container_name: cury-cliente-dev
    build:
      context: .
      dockerfile: Dockerfile
      target: development
      args:
        - NODE_ENV=development
    ports:
      - '3000:3000'
      - '9230:9230' # Debug port
    volumes:
      - ./src:/app/src:cached
      - ./public:/app/public:cached
      - ./package.json:/app/package.json
      - ./next.config.js:/app/next.config.js:ro
      - ./tailwind.config.ts:/app/tailwind.config.ts:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
    command: npm run dev-server
    restart: unless-stopped
    networks:
      - backend_cury-cliente

networks:
  backend_cury-cliente:
    external: true
