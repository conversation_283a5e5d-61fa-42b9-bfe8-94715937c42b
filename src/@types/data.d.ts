/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { TokenType } from './user';

export interface GetDataType<T> {
  id: string;
  index?: number | string | null;
}
export interface CreateDataType<T> {
  id: string;
  value: T;
  isSetCookie?: boolean;
  index?: number | string | null;
}

export interface CreateDataLoginType {
  token: TokenType;
  user: User;
  contents: Content[];
  contentSelected?: number;
  refreshContent?: boolean;
}
export interface CreateDataContentsType {
  AccountId: string;
  contents: Content[];
  contentSelected?: number;
}
export interface CreateDataContentType {
  content: Content;
  contentSelected?: number;
}
