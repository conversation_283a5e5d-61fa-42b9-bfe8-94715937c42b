/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Content } from './content';
import { Proposal } from './proposals';

export interface CaseResponse {
  data: {
    newcase: Case;
  };
}
export interface CaseExistsCaseToday {
  data: { existsCaseToday: boolean };
}
export interface Case {
  id: number;
  CaseId: string;
  SuppliedName?: string;
  SuppliedEmail?: string;
  SuppliedPhone?: string;
  AccountId: string;
  CaseNumber: string;
  ContactId: string;
  AssetId: string;
  Status: string;
  TipoAtendimento__c: string;
  Classificacao__c: string;
  Assunto__c: string;
  Description: string;
  InformacoesEnvioEmail__c: string;
  CreatedDate: string;
  LastModifiedDate: string;
  Empreendimento__c: string;
  PrevisaoConclusaoSla__c: string;
  NomeEmpreendimento__c: string;
  BlocoFormula__c: string;
  AtivoUnidade__c: string;
  StatusNegociacao__c: string;
  StatusAprovacao__c: string;
  DescricaoAtendimento__c: string;
  ClienteEntregouDocumentoParaCartorio__c: boolean;
  TipoReembolso__c: string;
  DataVencOriginalBoleto__c: string;
  DataVencPagarBoleto__c: string;
  ValorCheque__c: string;
  DataCheque__c: string;
  MesReferencia__c: string;
  files: string[];
  read: boolean;
  work_order: WorkOrder[];
}

export interface WorkOrder {
  id: number;
  WorkOrderId: string;
  CaseId: string;
  OwnerId: string;
  WorkOrderNumber: string;
  ServiceTerritoryId: string;
  CreatedDate: string;
  CreatedById: string;
  LastModifiedDate: string;
  LastModifiedById: string;
  AccountId: string;
  AssetId: string;
  Description: string;
  StartDate: string;
  EndDate: string;
  Subject: string;
  Status: string;
  created_at: string;
  updated_at: string;
}

export interface CaseCreate {
  data: {
    newcase: {
      data: {
        id: string;
        success: boolean;
      };
    };
  };
}
export interface CaseCreateError extends Error {
  data: null;
  errors: {
    message: string;
  }[];
}

export interface CaseDataType {
  type?: string;
  user?: User;
  SuppliedName?: string | number | boolean | object | null;
  SuppliedEmail?: string | number | boolean | object | null;
  SuppliedPhone?: string | number | boolean | object | null;
  content?: Content;
  description?: string | number | boolean | object | null;
  informacoesEnvioEmail?: string | number | boolean | object | null;
  tipoAtendimento: string | number | boolean | object | null;
  classificacao: string | number | boolean | object | null;
  assunto: string | number | boolean | object | null;
  OwnerName?: string | number | boolean | object | null;
  recordName?: string | number | boolean | object | null;
  OwnerId?: string | number | boolean | object | null;
  EmpreendimentoId?: string | number | boolean | object | null;
  MotivoCancelamento?: string | number | boolean | object | null;
  OrigemSolicitacaoAreaPrivativa__c?: string | number | boolean | object | null;
  DataAgendamento__c?: string | number | boolean | object | null;
  DataAgendamentoFormatada__c?: string | number | boolean | object | null;
  ClienteEntregouDocumentoParaCartorio__c?: string | number | boolean | object | null;
  TipoReembolso__c?: string | number | boolean | object | null;
  DataVencOriginalBoleto__c?: string | number | boolean | object | null;
  DataVencPagarBoleto__c?: string | number | boolean | object | null;
  ValorCheque__c?: string | number | boolean | object | null;
  DataCheque__c?: string | number | boolean | object | null;
  MesReferencia__c?: string | number | boolean | object | null;
  file?: File | null;
  textLoadingCase?: string | number | boolean | object | null;
}

export interface SendCaseProps {
  user?: User; // opcional
  content?: Content | Proposal | null | undefined; // opcional
  SuppliedName?: string;
  SuppliedEmail?: string;
  SuppliedPhone?: string;
  tipoAtendimento: string;
  classificacao: string;
  assunto: string;
  description?: string;
  recordName?: string;
  OwnerId?: string;
  OwnerName?: string;
  EmpreendimentoId?: string;
  MotivoCancelamento?: string;
  OrigemSolicitacaoAreaPrivativa__c?: string;
  DataAgendamento__c?: string;
  DataAgendamentoFormatada__c?: string;
  ClienteEntregouDocumentoParaCartorio__c?: boolean;
  TipoReembolso__c?: string;
  DataVencOriginalBoleto__c?: string;
  DataVencPagarBoleto__c?: string;
  ValorCheque__c?: string;
  DataCheque__c?: string;
  MesReferencia__c?: string;
  file?: File;
  type?: string; // rota da API, default = 'case'
  textLoadingCase?: string;
}

export interface CaseData {
  user: any;
  content: Content;
  tipoAtendimento: string;
  classificacao: string;
  assunto: string;
  MotivoCancelamento: string;
  description: string;
  type: string;
  textLoadingCase?: string;
}
