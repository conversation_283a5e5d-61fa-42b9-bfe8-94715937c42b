// import { Content } from './contents';
// import { User } from './user';

// /*
//  * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
//  * Copyright (C) 2024 StudioWox
//  *
//  * Desenvolvido exclusivamente para Cury por StudioWox.
//  *
//  * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
//  * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
//  *
//  * Para mais informações, entre em contato com:
//  * Thalles Freitas - <EMAIL>
//  * StudioWox: https://studiowox.com.br
//  * Cury: https://cury.net
//  *
//  * Todos os direitos reservados.
//  */
// export interface LayoutContextProviderProps {
//   children: ReactNode;
// }

// export interface LayoutContextType {
//   user: User;
//   content: Content;
//   subtitleHeaderMobile: string;
//   setSubtitleHeaderMobile: Dispatch<SetStateAction<string>>;
//   showSidebar: boolean;
//   setShowSidebar: Dispatch<SetStateAction<boolean>>;
//   isModalVisible: boolean | null;
//   setIsModalVisible: Dispatch<SetStateAction<boolean | null>>;
//   isModalCookieVisible: boolean | null;
//   setIsModalCookieVisible: Dispatch<SetStateAction<boolean | null>>;
//   imagesToSlider: string[] | null;
//   setImagesToSlider: Dispatch<SetStateAction<string[] | null>>;
//   showFoto: number | null;
//   setShowFoto: Dispatch<SetStateAction<number | null>>;
//   videosToSlider: string[] | null;
//   setVideosToSlider: Dispatch<SetStateAction<string[] | null>>;
//   showVideos: number | null;
//   setShowVideos: Dispatch<SetStateAction<number | null>>;
// }
