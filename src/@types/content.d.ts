/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Asset } from './asset';
import { Contract } from './contract';
import { DocumentType } from './document';
import { Empreendimento } from './Empreendimento';

export interface Content extends Array {
  ProposalId: string | null;
  ContractId: string | null;
  Contract: Contract;
  AccountId: string;
  name: string;
  UnidadeAtivoName: string;
  DataCompra__c: string;
  AssetId: string;
  PlantaId: string;
  planta: DocumentType[];
  StatusFinanciamento__c: string;
  NomeCCA__c: string;
  TelefoneCelularCCA__c: string;
  documents: DocumentType[];
  DataAntecipacaoDesconto__c: string;
  Empreendimento: Empreendimento;
  Etapa__c: string;
  type: string;
  statusBoletoAto: string | null;
  SituacaoEntrega__c: string | null;
  StatusCarteira__c: string | null;
  assets: Asset;
}
