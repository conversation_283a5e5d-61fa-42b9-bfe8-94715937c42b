/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Contract } from './contract';
import { DocumentType } from './document';
import { Empreendimento } from './Empreendimento';

export interface ProposalResponse {
  data: {
    proposal: Proposal;
  };
}

export interface Proposal {
  id?: number;
  ProposalId: string;
  UnidadeAtivoName: string;
  AssetId: string;
  statusBoletoAto: string;
  Empreendimento: Empreendimento;
  type: string;
  //
  DataAntecipacaoDesconto__c: string | null;
  PlantaId: string | null;
  planta: DocumentType[];
  StatusFinanciamento__c: string | null;
  NomeCCA__c: string | null;
  TelefoneCelularCCA__c: string | null;
  name: string | null;
  documents: DocumentType[];
  Contract: Contract | null;
}
