/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
export interface AuthContextProviderProps {
  children: ReactNode;
}

export interface AuthContextType {
  verifyAuth: (isProtected: boolean) => Promise<boolean>;
  // verificationCode: (code: string) => Promise<void>;
}

// Formulário de esqueci minha senha
export interface ForgotPasswordFormData {
  userLogin: string; // CPF ou CNPJ do usuário
}

// Resposta da API ao solicitar reset de senha
export interface SendResetPasswordResponse {
  success: boolean;
  message: string;
  data: {
    user: {
      Email__c: string;
      CPF__c?: string;
      CNPJ__c?: string;
    };
  };
}

// Dados para verificação de código
export interface VerificationCodeData {
  userLogin: string;
  email: string;
  code: string;
  deviceinfo: string;
}

// Resposta da API de verificação de código
export interface VerificationCodeResponse {
  success: boolean;
  message: string;
  data: {
    user: {
      AccountId: string;
      Email__c: string;
      CPF__c?: string;
      CNPJ__c?: string;
      email_verified_at: string | null;
    };
    token: {
      access_token: string;
      expires_at: string;
    };
  };
}

// Resposta para comunicar ao componente
export interface CodeVerificationResult {
  success: boolean;
  error?: string;
  redirectTo?: string;
}

export interface LoginType {
  user: {
    AccountId: string;
    CPF__c: string;
    CNPJ__c: string;
    Email__c: string;
    email_verified_at: string | null;
  };
  token: {
    access_token: string;
    expires_at: string;
  };
}

export type PreLoginType = number | string;
