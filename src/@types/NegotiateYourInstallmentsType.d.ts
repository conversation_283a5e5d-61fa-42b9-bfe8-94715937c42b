/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import {
  InstallmentItem,
  NegotiatedConditionsType,
  NegotiateType,
  SelectedInstallments
} from '@/@types/installments';

export interface StateType {
  returnTitle: string;
  returnText: string;
  returnType: string;
  caseNumber: string;
  negotiateData: NegotiateType | undefined;
  descriptionToCancel: string;
  cancelMotivation: string;
  showParcelas: boolean;
  showCondicoes: boolean;
  showReturn: boolean;
  showCancelamento: boolean;
  paid: boolean;
  negotiatedConditions: NegotiatedConditionsType[] | null;
  filteredInstallments: InstallmentItem[][] | null;
  billReceivableId: number | null;
  selectedInstallments: SelectedInstallments | null;
}
