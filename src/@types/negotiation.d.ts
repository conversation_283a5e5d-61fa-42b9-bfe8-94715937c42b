/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import {
  InstallmentItem,
  NegotiatedConditionsType,
  NegotiateType,
  SelectedInstallments
} from '@/@types/installments';

export interface StateType {
  returnTitle: string;
  returnText: string;
  returnType: string;
  caseNumber: string;
  negotiateData: NegotiateType | undefined;
  descriptionToCancel: string;
  cancelMotivation: string;
  showParcelas: boolean;
  showCondicoes: boolean;
  showReturn: boolean;
  showCancelamento: boolean;
  paid: boolean;
  negotiatedConditions: NegotiatedConditionsType[] | null;
  filteredInstallments: InstallmentItem[][] | null;
  billReceivableId: number | null;
  selectedInstallments: SelectedInstallments | null;
}

import { ErrorNegotiateType } from './apiTypes';
import { InstallmentsReturnType, ReturnType, SelectedInstallment } from './installments.d';

export interface NegotiationUIState {
  showParcelas: boolean;
  showCondicoes: boolean;
  showReturn: boolean;
  showCancelamento: boolean;
  returnTitle: string;
  returnText: string;
  caseNumber: string;
}

export interface NegotiationDataState {
  paid: boolean;
  negotiateData: NegotiateType | null;
  filteredInstallments: InstallmentItem[] | null;
  selectedInstallments: { selectedInstallment: SelectedInstallment[] } | null;
  billReceivableId: number | null;
  negotiatedConditions: NegotiationConditionsType | null;
  cancelMotivation: string;
  descriptionToCancel: string;
  DataAntecipacaoDesconto__c: string;
}

export type NegotiationConditionsType =
  | null
  | InstallmentsReturnType
  | ReturnType
  | ErrorNegotiateType
  | (NegotiatedConditionsType[] | InstallmentsReturnType | ReturnType | ErrorNegotiateType)[];

export interface NegotiationProcessActions {
  confirmAction: () => Promise<boolean>;
  simulationAction: () => void;
  cancelAction: () => Promise<void>;
  sendCancel: () => Promise<void | null>;
  selectCondictionBase: (condictionSelected: string | NegotiatedConditionsType | null) => void;
  cancelMotivationSelect: (value: string | number) => void;
  selectInstallments: (newInstallment: SelectedInstallment) => void;
  setShowCancelamento: (value: boolean) => void;
}

export type UseNegotiateYourInstallmentsReturn = NegotiationUIState &
  NegotiationDataState &
  NegotiationProcessActions;

// export const CASO_REGISTRADO = 'Caso registrado';
// export const ENVIANDO_DADOS = 'Enviando condição selecionada... Aguarde!';

//REFACTORING
export interface UseNegotiationProcessReturn {
  selectCondictionBase: (condictionSelected: string | NegotiatedConditionsType | null) => void;
  confirmAction: () => Promise<boolean>;
  simulationAction: (newNegotiateData?: any) => void;
  cancelAction: () => Promise<void>;
  sendCancel: () => Promise<void | null>;
  cancelMotivationSelect: (value: string | number) => void;
}
