/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export interface CategoriaVideo {
  id: number;
  CategoriaId: string;
  Name: string;
  videos: Video[];
}

export interface Video {
  id: number;
  CategoriaId: string;
  VideoId: string;
  Name: string;
  UrlEmbutida__c: string;
  created_at: string;
  updated_at: string;
}
export interface CategoriasVideosResponse {
  data: {
    categorias: CategoriaVideo[];
  };
}

export interface OptionsCategoriasTempProps {
  id: number;
  value: string;
  name: string;
}

export interface BoxVideosEnterpriseProps {
  videos: string[];
  setShowVideos: Dispatch<SetStateAction<number | null>>;
}
