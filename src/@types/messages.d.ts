/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export interface MessageResponse {
  data: {
    message: Message;
  };
}
export interface MessagesResponse {
  data: {
    messages: Message[];
  };
}
export interface Message {
  id: number;
  AccountId: string;
  MessageId: string;
  RelatedToId: string;
  FromAddress: string;
  FromName: string;
  ToAddress: string;
  HtmlBody: string;
  Subject: string;
  TextBody: string;
  MessageDate: string;
  read: boolean;
}
export interface MessageCount {
  data: {
    total: number;
  };
}
