export interface ModalContextProviderProps {
  children: ReactNode;
}
export type CallbackFunction = () => void;

export interface Params {
  [key: string]:
    | string
    | number
    | boolean
    | Params
    | null
    | CallbackFunction
    | {
        style?: {
          fontSize?: number;
          fontColor?: string;
        };
        progress?: number;
      };
}
export interface toggleModalProps {
  text?: string | undefined;
  params?: Params;
}
export interface ModalContextType {
  openModal: boolean;
  textInfoModal: string;
  params: Params;
  toggleModal: ({ text, params }: toggleModalProps) => void;
}
