/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

interface PostOptions {
  [key: string | object]: string | number | boolean | object | null | undefined;
}
interface Company {
  id: number;
  name: string;
}
interface CostCenter {
  id: number;
  name: string;
}
interface Customer {
  document: string;
  id: number;
  name: string;
}
interface PaymentTerms {
  descrition: string;
  description: string;
  id: string;
}

interface Receipt {
  date: string;
  days: number;
  discount: number;
  extra: number;
  netReceipt: number;
  type: string;
  value: number;
}

export interface InstallmentItem {
  billReceivableId?: number;
  annualCorrection: boolean;
  baseDate: string;
  document?: string;
  calculationDate: string;
  currentBalance: number;
  currentBalanceWithAddition: number;
  isoDueDate?: string;
  dueDate?: string;
  generatedBillet: boolean;
  id: number;
  indexerId?: number;
  indexerName?: stringi;
  installmentNumber: string;
  installmentSituation: string;
  originalValue: number;
  paymentTerms: PaymentTerms;
  receipts: Receipt[];
  sentToScripturalCharge: boolean;
  parentIndex?: number;
}
export type InstallmentDetails = {
  billReceivableId: number;
  statusBoleto: string | null;
  duedateFormated: string;
  receiptDate: string;
  installmentNumber: string | number;
  titleName: string;
  contractType: string;
  typeCondiction: string;
  correctionValue: string;
  originalValue: string;
  currentBalanceWithAddition: string;
  currentJurosMulta: string;
  paidValue: string;
  paidTotalValue: string;
  indexerName: string;
  rowsData: { value: string | number | null; customClass: string }[];
  generatedBillet: boolean;
} | null;

interface Installment {
  billReceivableId: number;
  company: Company;
  correctionDate: string;
  costCenter: CostCenter;
  customer: Customer;
  document: string;
  emissionDate: string;
  installments: InstallmentItem[];
  lastRenegotiationDate: string;
  oldestInstallmentDate: string;
  privateArea: number;
  revokedBillReceivableDate: string;
  ParentId?: string;
  Parent?: Parent;
  parentIndex?: number;
}

// export interface Installments {
//   data: {
//     installments: {
//       data: Installment[];
//     };
//   };
// }
export interface Installments {
  data: Installment[];
}

type AdimplenteType = 'adimplente';
type InadimplenteType = 'inadimplente';

type AdimplenteAction = 'simulacao' | 'antecipar';
type InadimplenteAction =
  | 'simulacao'
  | 'ParcelamentoSaldoDevedor'
  | 'ParcelamentoSaldoVencido'
  | 'QuitacaoSaldoDevedor'
  | 'QuitacaoSaldoVencido';

interface ParcelamentoAction extends PostOptions {
  installmentQuantity?: number;
}

interface BaseNegotiateType extends PostOptions {
  codigo_sienge_user?: string;
  assetId: string;
  billReceivableId?: number | null;
  installments?: SelectedInstallment[] | null;
  installmentsIds?: number[] | null;
}

// interface CommonNegotiateType extends BaseNegotiateType {
//   type: AdimplenteType | InadimplenteType;
//   action: string | 'simulacao';
// }

interface AdimplenteNegotiateType extends BaseNegotiateType {
  type: AdimplenteType;
  action: 'antecipar';
}

interface InadimplenteNegotiateTypeWithoutQuantity extends BaseNegotiateType {
  type: InadimplenteType;
  action: Exclude<
    InadimplenteAction,
    'ParcelamentoSaldoDevedor' | 'ParcelamentoSaldoVencido' | 'simulacao'
  >;
}

interface InadimplenteNegotiateTypeWithQuantity extends BaseNegotiateType, ParcelamentoAction {
  type: InadimplenteType;
  action: 'ParcelamentoSaldoDevedor' | 'ParcelamentoSaldoVencido';
}

export type NegotiateType = {
  type: string;
  action: string;
  codigo_sienge_user?: string;
  assetId?: string;
  billReceivableId?: number;
  installments?: SelectedInstallment[];
  installmentsIds?: number[];
  selectedInstallment?: SelectedInstallment[];
};

type NegotiatedConditionsData = {
  value: string;
  title: string;
};
export interface NegotiatedConditionsType {
  title: string;
  id: string;
  data: NegotiatedConditionsData[];
}

export interface SelectedInstallments {
  selectedInstallment: SelectedInstallment[];
}

export interface SelectedInstallment {
  billReceivableId: number;
  id: string;
  indexerId: number;
  isoDueDate?: string;
  dueDate?: string;
  value: number;
  paymentTerms: PaymentTerms;
}

export interface InstallmentsReturnType {
  id: string;
}
// export interface InstallmentsErrorReturnType {
//   id: string;
// }
export interface ReturnType {
  success: boolean;
  message: null;
  caso: Caso;
  CaseNumber: null;
}
// export interface NegotiatedConditionsPayload {
//   type: AdimplenteType | InadimplenteType;
//   'action': 'ParcelamentoSaldoDevedor';
//   'installmentQuantity': 3;
//   'assetId': '02i4Q00000HfHvfQAF';
// }

export type Indexer = {
  id: number;
  IndexerId: string;
  name: string;
  revenueRetroactivity: string;
  revenueRetroactivityExpenses: string;
  lastValue: {
    date: string;
    value: number;
    percentage: number;
  };
};
// RETURN API TYPES

export interface Caso {
  attributes: Attributes;
  Id: string;
  AccountId: string;
  OwnerId: string;
  CaseNumber: string;
  RecordTypeId: string;
  ContactId: string;
  AssetId: string;
  Status: string;
  TipoAtendimento__c: string;
  Classificacao__c: string;
  Assunto__c: string;
  Description: string;
  CreatedDate: string;
  LastModifiedDate: string;
  Empreendimento__c: string;
  PrevisaoConclusaoSla__c: string;
  NomeEmpreendimento__c: string;
  BlocoFormula__c: string;
  AtivoUnidade__c: string;
  Owner: Owner;
  ParentId: string;
  Parent: Parent;
}

interface Parent {
  attributes: Attributes;
  Id: string;
  Campanha__c: string;
  Campanha__r: Campanha;
}

interface Campanha {
  attributes: Attributes;
  Id: string;
  EmpresaDeGestao__c: string;
  EmpresaDeGestao__r: EmpresaDeGestao;
}

interface EmpresaDeGestao {
  attributes: Attributes;
  Id: string;
  Name: string;
  TelefoneComercial__c: string;
}

interface Owner {
  attributes: Attributes;
  Id: string;
  Name: string;
}

interface Attributes {
  type: string;
  url: string;
}

export interface UseFilterInstallmentsReturn {
  processInstallments: (installments: Installments) => {
    isPaid: boolean;
    filteredInstallments: InstallmentItem[];
    hasReparcelamentoToday: boolean;
    allInstallmentData: InstallmentItem[];
  };
}

export interface NegotiatedConditionsProps {
  negotiatedConditions: NegotiatedConditionsType[] | null;
  paid: boolean;
  selectCondictionBase: (condictionSelected: null | string | NegotiatedConditionsType) => void;
  confirmAction: () => void;
  cancelAction: () => void;
  show: boolean;
}
