/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export interface Contract {
  //   id: number;
  //   AccountId: string;
  //   ProposalId: string;
  //   ContractId: string;
  //   ContractNumber: string;
  //   StatusCarteira__c: string;
  //   ContratoComProgramaFidelidade__c: string;
  //   DataAdesaoProgramaFidelidade__c: string;
  //   DataValidacaoProgramaFidelidade__c: string;
  //   CampanhaId: string;
  //     SituacaoEntrega__c: string;

  ContractId: string;
  AccountId: string;
  ProposalId: string | null;
  EmpreendimentoId: string;
  AssetId: string;
  UnidadeAtivoName: string;
  CampanhaId: string;
  ContractNumber: string;
  StatusCarteira__c: string;
  SituacaoEntrega__c: string;
  Regional__c: string;
  Status: string;
  ContratoComProgramaFidelidade__c: string;
  DataAdesaoProgramaFidelidade__c: string;
  DataValidacaoProgramaFidelidade__c: string;
  DataChaves__c: string;
  DataCompra__c: string;
  CreatedDate: string;
  LastModifiedDate: string;
  created_at: string;
  updated_at: string;
  type: string;
  DataAntecipacaoDesconto__c: string;
  PlantaId: string;
  planta: DocumentType[];
  StatusFinanciamento__c: string;
  NomeCCA__c: string;
  TelefoneCelularCCA__c: string;
  imgsCarrossel: DocumentType[];
  name: string;
  documents: DocumentType[];
  Empreendimento: Empreendimento;
}
