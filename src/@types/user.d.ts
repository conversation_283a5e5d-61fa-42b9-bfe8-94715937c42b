/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Case } from './cases';
import { DocumentType } from './document';
import { Message } from './messages';

export type TokenCookie = {
  token: TokenType;
};
export type TokenType = {
  access_token: string;
  expires_at: string;
};

export interface User {
  // Sindico: UserType[];
  id: number;
  UserId: string;
  AccountId: string;
  PersonContactId: string;
  CodigoSienge__c: string;
  Name: string;
  FirstName: string;
  LastName: string;
  Email__c: string;
  email_verified_at: string;
  avatar: string;
  // password: string;
  TelefoneCelular__c: string;
  TelefoneCelular2__c: string;
  TelefoneComercial__c: string;
  TelefoneFixo__c: string;
  CPF__c: string;
  CNPJ__c: string;
  EmailAlternativo__c: string;

  alternative_name: string;
  ShippingPostalCode__c: string;
  ShippingNeighborhood__c: string;
  ShippingStreet__c: string;
  ShippingCity__c: string;
  ShippingNumber__c: string;
  ShippingState__c: string;
  ShippingComplement__c: string;
  ShippingCountry__c: string;
  MembroPatrimonioAfetacao__c: boolean;
  contents: Content[];
  // proposals: Proposals[];
  // contracts: Contract[];
  messages: Message[];
  cases: Case[];
  token: TokenType;
  CreatedDate: string;
  LastModifiedDate: string;
  created_at: string;
  updated_at: string;
  isImpersonating?: boolean;
  documents: DocumentType[];
}

export interface UserResponse {
  data: User;
}
export interface AvatarResponse {
  data: {
    avatar: string;
  };
}
