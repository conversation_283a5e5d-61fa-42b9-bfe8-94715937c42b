// /*
//  * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
//  * Copyright (C) 2024 StudioWox
//  *
//  * Desenvolvido exclusivamente para Cury por StudioWox.
//  *
//  * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
//  * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
//  *
//  * Para mais informações, entre em contato com:
//  * Thalles Freitas - <EMAIL>
//  * StudioWox: https://studiowox.com.br
//  * Cury: https://cury.net
//  *
//  * Todos os direitos reservados.
//  */

// import { Content } from './content';
// import { User } from './user';

// export interface LayoutContextType {
//   user: User;
//   content: Content | null;
//   subtitleHeaderMobile: string;
//   setSubtitleHeaderMobile: (subtitle: string) => void;
//   showSidebar: boolean;
//   setShowSidebar: Dispatch<SetStateAction<boolean>>;
//   isModalVisible: boolean | null;
//   setIsModalVisible: (visible: boolean | null) => void;
//   isModalCookieVisible: boolean | null;
//   setIsModalCookieVisible: (visible: boolean | null) => void;
//   imagesToSlider: string[] | null;
//   setImagesToSlider: (images: string[] | null) => void;
//   showFoto: number | null;
//   setShowFoto: (index: number | null) => void;
//   videosToSlider: string[] | null;
//   setVideosToSlider: (videos: string[] | null) => void;
//   showVideos: number | null;
//   setShowVideos: (index: number | null) => void;
// }
