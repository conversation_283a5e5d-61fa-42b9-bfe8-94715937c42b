/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
export interface EmpreendimentoResponse {
  data: {
    empreendimento: Empreendimento;
  };
}

export interface Empreendimento {
  id: number;
  EmpreendimentoId: string;
  CodigoSienge__c: string;
  CodigoSiengeSPE: string;
  Sigma__c: boolean;
  Manual__c: boolean;
  Empreendimento_novo__c: boolean;
  CodigoSienge__c: string;
  name: string;
  Regional__c: string;
  LogoEmpreendimento__c: string;
  StatusMacro__c: string;
  DataRealizadaHabitese__c: string;
  GOResponsavel__c: string;
  DataUltimaAtualizacao__c: string;
  Fundacao__c: string;
  Estrutura__c: string;
  Alvenaria__c: string;
  InstalacoesEletricas__c: string;
  InstalacoesHidraulicas__c: string;
  AcabamentoInterno__c: string;
  AcabamentoExterno__c: string;
  ServicosComplementares__c: string;
  Pintura__c: string;
  MobilizacaoCanteiro__c: string;
  PorcentagemFisicoAcumulado__c: string;
  EmpreendimentoPlanta__c: string[];
  imgsCarrossel: string | string[];
  OpcaoPlanta__c: string[];
  ImplantacaoAreaLazer__c: string[];
  InformadoEngenhariaEm__c: string[];
  ImplantacaoCondominio__c: string[];
  ImplantacaoEmpreendimento__c: string[];
  Perspectiva__c: string[];
  Fotos__c: string | string[];
  Videos__c: string | string[];
  Sindico__c: string;
  DataAGIRealizada__c: string;
  UltimaAtualizacaoVideoDrone__c: string;
  DataUltimaAtualizacaoMidia__c: string;
  DataRealMatriculaIndividualizada__c: string;
  OperatingHoursId: string;
  TerritorioServico: string;
  DataEntregaContratualCury__c: string;
  Video_Tour__c: string | string[];
  Last_Data_Tour__c: string;
  TerritorioServicoIsActive: boolean;
  documents: DocumentType[];
}
export interface real_estate_project {
  id: number;
  EmpreendimentoId: string;
  CodigoSienge__c: string;
  CodigoSiengeSPE: string;
  Sigma__c: boolean;
  Manual__c: boolean;
  Empreendimento_novo__c: boolean;
  CodigoSienge__c: string;
  name: string;
  Regional__c: string;
  LogoEmpreendimento__c: string;
  StatusMacro__c: string;
  DataRealizadaHabitese__c: string;
  GOResponsavel__c: string;
  DataUltimaAtualizacao__c: string;
  Fundacao__c: string;
  Estrutura__c: string;
  Alvenaria__c: string;
  InstalacoesEletricas__c: string;
  InstalacoesHidraulicas__c: string;
  AcabamentoInterno__c: string;
  AcabamentoExterno__c: string;
  ServicosComplementares__c: string;
  Pintura__c: string;
  MobilizacaoCanteiro__c: string;
  PorcentagemFisicoAcumulado__c: string;
  EmpreendimentoPlanta__c: string[];
  imgsCarrossel: string | string[];
  OpcaoPlanta__c: string[];
  ImplantacaoAreaLazer__c: string[];
  InformadoEngenhariaEm__c: string[];
  ImplantacaoCondominio__c: string[];
  ImplantacaoEmpreendimento__c: string[];
  Perspectiva__c: string[];
  Fotos__c: string[];
  Videos__c: string[];
  Sindico__c: string;
  DataAGIRealizada__c: string;
  UltimaAtualizacaoVideoDrone__c: string;
  DataUltimaAtualizacaoMidia__c: string;
  DataRealMatriculaIndividualizada__c: string;
  OperatingHoursId: string;
  TerritorioServico: string;
  DataEntregaContratualCury__c: string;
  Video_Tour__c: string[];
  Last_Data_Tour__c: string;
  TerritorioServicoIsActive: boolean;
  documents: DocumentType[];
}
export interface BoxCardsEnterpriseProps {
  imagesCards: string[];
  setShowFoto: (index: number) => void;
}
