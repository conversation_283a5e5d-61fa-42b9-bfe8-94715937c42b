import { DocumentType } from './document';

export interface InputProps {
  label?: string;
  type: string;
  name: string;
  defaultValue?: string;
  maxLength?: number;
  rows?: number;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  value?: string;
  errors?: string | null;
  showSavePass?: boolean;
  onChange?: ChangeEventHandler<HTMLInputElement> | ChangeEventHandler<HTMLTextAreaElement>;
  mask?: string | (string | RegExp)[];
}

export interface TextAreaFieldProps {
  name: string;
  rows?: number;
  ref: Ref<HTMLTextAreaElement>;
  onChange?: ChangeEventHandler<HTMLTextAreaElement>;
  required?: boolean;
  disabled?: boolean;
  disabledColor: string;
  enabledColor: string;
  [key: string]: any;
}

export interface ButtonDocumentsProps {
  docs: DocumentType;
  ProposalId?: string;
  EmpreendimentoId?: string;
  index: number;
}
