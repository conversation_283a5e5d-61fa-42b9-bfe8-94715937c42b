/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

declare global {
  interface Window {
    ReactNativeWebView: {
      postMessage(message: string): void;
    };
    opera: string;
    embeddedservice_bootstrap: {
      init: (
        orgId: string,
        eswConfigDevName: string,
        baseLiveAgentURL: string,
        options: { scrt2URL: string }
      ) => void;

      addEventHandler: (type: string, handler: (event: MessageEvent) => void) => void;
      // init(
      //   arg0: string,
      //   arg1: string,
      //   arg2: string,
      //   arg3: {
      //     scrt2URL: string;
      //     targetElement?: {
      //       className: string;
      //     };
      //   }
      // ): unknown;
      brandingData: {
        n: string;
        v: string;
      }[];
      settings: {
        displayHelpButton: boolean;
        language: string;
        widgetHeight: string;
        hideChatButtonOnLoad: boolean;
        devMode: boolean;
        omitSandbox: boolean;
        branding: {
          v: string;
        }[];
        chatButtonPosition: string;
        externalStyles: string[];
      };
      utilAPI: {
        showChatButton(): () => void;
        launchChat(): () => void;
      };
    };
  }
}

export {};


