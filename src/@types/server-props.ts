/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

// @/types/server-props.ts
import { Content } from '@/@types/content';
import { User } from '@/@types/user';

/**
 * Propriedades passadas do servidor para o componente cliente HomeScreen
 */
// export interface ServerProps {
//   /** Dados do usuário obtidos do servidor */
//   userData: User | null;

//   /** Lista de conteúdos disponíveis para o usuário */
//   contents: Content[] | null;

//   /** Conteúdo atualmente selecionado */
//   contentData: Content | null; //: Proposal | null;

//   /** Índice do conteúdo selecionado */
//   contentSelected: number;

//   /** Status do boleto/ato para o usuário atual */
//   boletoAtoStatus: string;

//   /** Lista de URLs de imagens para o carrossel */
//   imagesCarrossel: string[];

//   /** Flag indicando se deve mostrar o cartão de boleto/ato */
//   showBoletoAto: boolean;

//   /** Flag indicando se deve mostrar a barra lateral */
//   showSidebar: boolean;
// }

export interface InitialServerData {
  initialUser: User | null;
  initialContentData: Content | null;
  initialContentsData: Content[] | null;
  initialContentSelected: number;
  initialShowSidebar: boolean | null;
  environment: string;
  hasImpersonateToken: boolean;
}
