/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Content } from './content';
import { Installments } from './installments';
import { Proposal } from './proposals';

export interface InstallmentData {
  installments: Installment[];
}

export interface Financials {
  totalPaid: string;
  totalInstallmentsPaid: string;
  totalPayable: string;
  totalInstallmentsPayable: string;
  totalOutstandingInstallments: string;
  totalOutstanding: string;
  total: string;
}

export interface UserContext {
  user: {
    CodigoSienge__c?: string;
  };
}

export interface ContentContext {
  content: Content | Proposal | null | undefined;
  installments: Installments | null;
  getInstallments: (codigo: string, unidade: string) => void;
  // checkBoletoAto: (codigo: string, unidade: string) => void;
}

export interface InformeContextProviderProps {
  children: ReactNode;
}

export interface InformeContextType {
  showInforme: boolean;
  setShowInforme: (show: boolean) => void;
}
