import { Options } from '@/templates/TalkToCury/mock/dataRequestOptions';
import { Content } from './content';
import { Indexer } from './installments';

/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
export interface VerifyAuthResponse {
  status: boolean;
}
export interface CasesResponse {
  data: {
    cases: Case[];
  };
}

export interface UserDataResponse {
  data: {
    user: User;
    contents: Content[];
  };
}

export interface ExistsCaseTodayResponse {
  data: { existsCaseToday: boolean };
}
export interface ContentResponse {
  data: { content: Content };
}
export interface ContentsResponse {
  data: { contents: Content[] };
}

export interface IrResponse {
  irPDF?: string;
  errors?: { default: string };
}

export interface EmpreendimentosVizinhoResponse {
  data: {
    empreendimentos: Options[];
  };
}

export interface IndexerResponse {
  data: {
    indexers: Indexer[];
  };
}
