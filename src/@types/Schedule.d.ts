/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export interface Schedules {
  schedule: ScheduleCompiled[];
}
export interface ScheduleCompiled {
  day: string;
  name: string;
  // description: string;
}
export interface Holidays {
  Name: string;
  NextOccurrenceDate: string;
}

export interface WorkOrders {
  StartDate: string;
}

export interface Schedule {
  id: number;
  ScheduleId: string;
  EmpreendimentoId: string;
  Subject: string;
  Type: string;
  // Description: string;
  StartDateTime: string;
  EndDateTime: string;
  Location: string;
  created_at: string;
  updated_at: string;
}
export interface ScheduleServiceOptions {
  ScheduleServicesOptionsId: string;
  EmpreendimentoId: string;
  TimeSlotNumber: string;
  DayOfWeek: string;
  StartTime: string;
  EndTime: string;
}

export interface ServiceTerritoryMember {
  serviceTerritoryMemberId: string;
  EmpreendimentoId: string;
  ServiceTerritoryId: string;
  TerritoryType: string;
}

export interface ScheduleOptionsResponse {
  data: {
    schedule: ScheduleServiceOptions[];
    holidays: Holidays[];
    workOrders: WorkOrders[];
    members: ServiceTerritoryMember[];
  };
}

type ValuePiece = Date | null;
export type Value = ValuePiece | [ValuePiece, ValuePiece];

export interface CalendarRequestProps {
  title: string;
  field: keyof IFormInputRequestService;
  onChange: (value: Value, field: keyof IFormInputRequestService) => void;
}
