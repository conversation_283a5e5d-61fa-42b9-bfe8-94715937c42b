/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { InformeContextProviderProps, InformeContextType } from '@/@types/financial';
import { createContext, useContext, useMemo, useState } from 'react';

const InformeIrContext = createContext<InformeContextType>({
  showInforme: false,
  setShowInforme: () => {}
});

export const useInforme = () => {
  const context = useContext(InformeIrContext);
  if (!context) {
    // Em vez de lançar um erro, retornamos um valor padrão
    // Isso evita erros durante a hidratação do componente
    console.warn('useInforme está sendo usado fora de um InformeProvider');
    return {
      showInforme: false,
      setShowInforme: () => {}
    };
  }
  return context;
};

export const InformeProvider = ({ children }: InformeContextProviderProps) => {
  const [showInforme, setShowInforme] = useState(false);

  const valueInforme = useMemo(
    () => ({
      showInforme,
      setShowInforme: (show: boolean) => setShowInforme(show)
    }),
    [showInforme]
  );

  return <InformeIrContext.Provider value={valueInforme}>{children}</InformeIrContext.Provider>;
};
