/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { createContext, ReactNode, useCallback, useContext, useMemo, useReducer } from 'react';
import { useRouter } from 'next/navigation';

import { Content } from '@/@types/content';
import { User } from '@/@types/user';
import { FILES_URL } from '@/constants';
import { useMediaQuery } from '@/hooks';
import { ApiError } from '@/server/utils/ApiError';

// ... (definições de interface UserState, ContentState, UIState e Action)

interface AppState {
  user: UserState;
  content: ContentState;
  ui: UIState;
}

interface UserState {
  user: User | null;
  avatar: string;
}

interface ContentState {
  contents: Content[] | null;
  content: Content | null;
  contentSelected: number;
  imagesToSlider: string[] | null;
  videosToSlider: string[] | null;
  showFoto: number | null;
  showVideos: number | null;
  currentPage: number;
  hasMoreContent: boolean;
  isLoadingMore: boolean;
}

interface UIState {
  isLoading: boolean;
  showSidebar: boolean;
  modals: {
    isVisible: boolean;
    isCookieVisible: boolean;
  };
  mobile: {
    subtitleHeader: string;
  };
}

// Actions
type Action =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_CONTENT'; payload: Content | null }
  | { type: 'SET_CONTENTS'; payload: Content[] | null }
  | { type: 'SET_CONTENT_SELECTED'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_UI_STATE'; payload: Partial<UIState> }
  | { type: 'SET_MODAL_STATE'; payload: { type: 'default' | 'cookie'; value: boolean } }
  | { type: 'SET_SHOW_FOTO'; payload: number | null }
  | { type: 'SET_SHOW_VIDEOS'; payload: number | null }
  | { type: 'SET_IMAGES_TO_SLIDER'; payload: string[] | null }
  | { type: 'SET_VIDEOS_TO_SLIDER'; payload: string[] | null }
  | { type: 'SET_SUBTITLE_HEADER_MOBILE'; payload: string }
  | { type: 'SET_CURRENT_PAGE'; payload: number }
  | { type: 'SET_HAS_MORE_CONTENT'; payload: boolean }
  | { type: 'SET_LOADING_MORE'; payload: boolean }
  | { type: 'APPEND_CONTENTS'; payload: Content[] };

// Root Reducer
const appReducer = (state: AppState, action: Action): AppState => {
  switch (action.type) {
    case 'SET_USER': {
      const avatarsrc = action.payload?.avatar
        ? action.payload?.avatar?.startsWith('http')
          ? action.payload?.avatar
          : `${FILES_URL}${action.payload?.avatar}`
        : '/images/perfil-default.svg';

      return {
        ...state,
        user: {
          ...state.user,
          user: action.payload,
          avatar: avatarsrc
        }
      };
    }
    case 'SET_CONTENT':
      return { ...state, content: { ...state.content, content: action.payload } };
    case 'SET_CONTENTS':
      return { ...state, content: { ...state.content, contents: action.payload } };
    case 'SET_CONTENT_SELECTED':
      return { ...state, content: { ...state.content, contentSelected: action.payload } };
    case 'SET_SHOW_FOTO':
      return { ...state, content: { ...state.content, showFoto: action.payload } };
    case 'SET_SHOW_VIDEOS':
      return { ...state, content: { ...state.content, showVideos: action.payload } };
    case 'SET_IMAGES_TO_SLIDER':
      return { ...state, content: { ...state.content, imagesToSlider: action.payload } };
    case 'SET_VIDEOS_TO_SLIDER':
      return { ...state, content: { ...state.content, videosToSlider: action.payload } };
    case 'SET_CURRENT_PAGE':
      return { ...state, content: { ...state.content, currentPage: action.payload } };
    case 'SET_HAS_MORE_CONTENT':
      return { ...state, content: { ...state.content, hasMoreContent: action.payload } };
    case 'SET_LOADING_MORE':
      return { ...state, content: { ...state.content, isLoadingMore: action.payload } };
    case 'APPEND_CONTENTS': {
      if (!state.content.contents) {
        return { ...state, content: { ...state.content, contents: action.payload } };
      }
      const existingIds = new Set(state.content.contents.map((item) => item.ProposalId || item.ContractId));
      const newItems = action.payload.filter(
        (item) => !existingIds.has(item.ProposalId || item.ContractId)
      );
      if (newItems.length === 0) {
        return state;
      }
      return {
        ...state,
        content: {
          ...state.content,
          contents: [...state.content.contents, ...newItems]
        }
      };
    }
    case 'SET_LOADING':
      return { ...state, ui: { ...state.ui, isLoading: action.payload } };
    case 'SET_UI_STATE':
      return { ...state, ui: { ...state.ui, ...action.payload } };
    case 'SET_MODAL_STATE':
      return {
        ...state,
        ui: {
          ...state.ui,
          modals: {
            ...state.ui.modals,
            [action.payload.type === 'cookie' ? 'isCookieVisible' : 'isVisible']: action.payload.value
          }
        }
      };
    case 'SET_SUBTITLE_HEADER_MOBILE':
      return {
        ...state,
        ui: {
          ...state.ui,
          mobile: {
            ...state.ui.mobile,
            subtitleHeader: action.payload
          }
        }
      };
    default:
      return state;
  }
};

interface AppProviderProps {
  children: ReactNode;
  initialUser: User | null;
  initialContents: Content[] | null;
  initialContentSelected: number;
  initialContent: Content | null;
}

const AppContext = createContext<any>({} as any);
export const useAppContext = () => useContext(AppContext);

export function AppProvider({
  children,
  initialUser,
  initialContents,
  initialContentSelected,
  initialContent,
}: AppProviderProps) {
  const router = useRouter();
  const { isMobile } = useMediaQuery();
  const [appState, dispatch] = useReducer(appReducer, {
    user: {
      user: initialUser,
      avatar: initialUser?.avatar
        ? initialUser.avatar.startsWith('http')
          ? initialUser.avatar
          : `${FILES_URL}${initialUser.avatar}`
        : '/images/perfil-default.svg'
    },
    content: {
      contents: initialContents,
      content: initialContent,
      contentSelected: initialContentSelected,
      imagesToSlider: null,
      videosToSlider: null,
      showFoto: null,
      showVideos: null,
      currentPage: 1,
      hasMoreContent: true,
      isLoadingMore: false
    },
    ui: {
      isLoading: false,
      showSidebar: true,
      modals: {
        isVisible: false,
        isCookieVisible: false
      },
      mobile: {
        subtitleHeader: ''
      }
    }
  });

  const showBoletoAto = useMemo(async () => {
    if(appState.content.content?.type === 'sindico') return true;

    if (appState.content.content?.statusBoletoAto !== 'Recebimento'){
        const { checkStatusBoletoAto } = await import('@/server/actions/content');

        if(appState.user.user?.CodigoSienge__c && appState.content.content?.UnidadeAtivoName){
          const statusBoletoAto = await checkStatusBoletoAto(
            appState.user.user?.CodigoSienge__c as string,
            appState.content.content?.UnidadeAtivoName as string
          );
          return statusBoletoAto !== 'Recebimento';
        }
      return false;
    }
    return true;
  }, [appState.content.content]);

  const nameFinal = useMemo(() => {
    const user = appState.user.user;
    if (!user) return '';
    const limitWords = (name: string): string => {
      const words = name.trim().split(/\s+/);
      if (words.length <= 3) return name;
      return `${words.slice(0, 3).join(' ')}...`;
    };
    if (user.alternative_name) {
      return limitWords(user.alternative_name);
    }
    if (user.FirstName && user.LastName) {
      return limitWords(`${user.FirstName} ${user.LastName}`);
    }
    return limitWords(user.Name || '');
  }, [appState.user.user]);

  const selectContent = useCallback(
    async (index: number) => {
      if (index === appState.content.contentSelected) return;
      dispatch({ type: 'SET_LOADING', payload: true });
      try {
        const currentContent = appState.content.contents?.[index];
        if (!currentContent) {
          throw new Error('Conteúdo selecionado não encontrado.');
        }
        const { updateContentAndGetServerSide } = await import('@/server/actions/content');
        const newContent = await updateContentAndGetServerSide(appState.user.user, currentContent, index);
        dispatch({ type: 'SET_CONTENT_SELECTED', payload: index });
        dispatch({ type: 'SET_CONTENT', payload: newContent });
        router.refresh();
      } catch (error) {
        console.error('Erro ao selecionar conteúdo:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },
    [appState.content.contentSelected, appState.content.contents, router]
  );

  // ... (outros callbacks)
  const setShowFotoCallback = useCallback(
    (value: number | null) => {
      dispatch({ type: 'SET_SHOW_FOTO', payload: value });
    },
    [dispatch]
  );

  const setShowVideosCallback = useCallback(
    (value: number | null) => {
      dispatch({ type: 'SET_SHOW_VIDEOS', payload: value });
    },
    [dispatch]
  );

  const setImagesToSliderCallback = useCallback(
    (value: string[] | null) => {
      dispatch({ type: 'SET_IMAGES_TO_SLIDER', payload: value });
    },
    [dispatch]
  );

  const setVideosToSliderCallback = useCallback(
    (value: string[] | null) => {
      dispatch({ type: 'SET_VIDEOS_TO_SLIDER', payload: value });
    },
    [dispatch]
  );

  const setUserCallback = useCallback(
    (user: User | null) => dispatch({ type: 'SET_USER', payload: user }),
    [dispatch]
  );

  const setContentCallback = useCallback(
    (content: Content | null) => dispatch({ type: 'SET_CONTENT', payload: content }),
    [dispatch]
  );

  const setContentsCallback = useCallback(
    (contents: Content[] | null) => dispatch({ type: 'SET_CONTENTS', payload: contents }),
    [dispatch]
  );

  const setLoadingCallback = useCallback(
    (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
    [dispatch]
  );

  const setShowSidebarCallback = useCallback(
    (show: boolean) => dispatch({ type: 'SET_UI_STATE', payload: { showSidebar: show } }),
    [dispatch]
  );

  const setModalVisibleCallback = useCallback(
    (visible: boolean) =>
      dispatch({ type: 'SET_MODAL_STATE', payload: { type: 'default', value: visible } }),
    [dispatch]
  );

  const setModalCookieVisibleCallback = useCallback(
    (visible: boolean) =>
      dispatch({ type: 'SET_MODAL_STATE', payload: { type: 'cookie', value: visible } }),
    [dispatch]
  );

  const setSubtitleHeaderMobileCallback = useCallback(
    (subtitle: string) => dispatch({ type: 'SET_SUBTITLE_HEADER_MOBILE', payload: subtitle }),
    [dispatch]
  );

  const loadMoreContents = useCallback(async () => {
    if (appState.content.isLoadingMore || !appState.content.hasMoreContent) return;
    dispatch({ type: 'SET_LOADING_MORE', payload: true });
    try {
      const nextPage = appState.content.currentPage + 1;
      const { getUserAssets } = await import('../server/actions/user');
      const response = await getUserAssets(nextPage, 20);

      if (response.contents && response.contents.length > 0) {
        const hasMore = response.pagination.current_page < response.pagination.last_page;
        setTimeout(() => {
          dispatch({ type: 'APPEND_CONTENTS', payload: response.contents });
          dispatch({ type: 'SET_CURRENT_PAGE', payload: response.pagination.current_page });
          dispatch({ type: 'SET_HAS_MORE_CONTENT', payload: hasMore });
          dispatch({ type: 'SET_LOADING_MORE', payload: false });
        }, 0);
      } else {
        dispatch({ type: 'SET_HAS_MORE_CONTENT', payload: false });
        dispatch({ type: 'SET_LOADING_MORE', payload: false });
      }
    } catch (error) {
      if (error instanceof ApiError && error.status === 401) {
        console.warn('Tentativa de carregar mais conteúdo sem autenticação.');
      } else {
        console.error('Error loading more contents:', error);
      }
      dispatch({ type: 'SET_LOADING_MORE', payload: false });
    }
  }, [appState.content.currentPage, appState.content.isLoadingMore, appState.content.hasMoreContent, dispatch]);

  const contextValue = useMemo(
    () => ({
      user: appState.user.user,
      avatar: appState.user.avatar,
      contents: appState.content.contents,
      content: appState.content.content,
      contentSelected: appState.content.contentSelected,
      imagesToSlider: appState.content.imagesToSlider,
      videosToSlider: appState.content.videosToSlider,
      showFoto: appState.content.showFoto,
      showVideos: appState.content.showVideos,
      isLoading: appState.ui.isLoading,
      showSidebar: appState.ui.showSidebar,
      modals: appState.ui.modals,
      mobile: appState.ui.mobile,
      hasMoreContent: appState.content.hasMoreContent,
      isLoadingMore: appState.content.isLoadingMore,
      currentPage: appState.content.currentPage,
      nameFinal,
      isMobile,
      isModalVisible: appState.ui.modals.isVisible,
      isModalCookieVisible: appState.ui.modals.isCookieVisible,
      selectContent,
      setUser: setUserCallback,
      setContent: setContentCallback,
      setContents: setContentsCallback,
      setShowFoto: setShowFotoCallback,
      setShowVideos: setShowVideosCallback,
      setImagesToSlider: setImagesToSliderCallback,
      setVideosToSlider: setVideosToSliderCallback,
      setLoading: setLoadingCallback,
      setShowSidebar: setShowSidebarCallback,
      setModalVisible: setModalVisibleCallback,
      setIsModalVisible: setModalVisibleCallback,
      setModalCookieVisible: setModalCookieVisibleCallback,
      setSubtitleHeaderMobile: setSubtitleHeaderMobileCallback,
      loadMoreContents
    }),
    [
      appState,
      isMobile,
      nameFinal,
      selectContent,
      setUserCallback,
      setContentCallback,
      setContentsCallback,
      setShowFotoCallback,
      setShowVideosCallback,
      setImagesToSliderCallback,
      setVideosToSliderCallback,
      setLoadingCallback,
      setShowSidebarCallback,
      setModalVisibleCallback,
      setModalCookieVisibleCallback,
      setSubtitleHeaderMobileCallback,
      loadMoreContents,
      showBoletoAto
    ]
  );

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
}
