/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import {
  ModalContextProviderProps,
  ModalContextType,
  Params,
  toggleModalProps
} from '@/@types/Modal';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';

const ModalContext = createContext<ModalContextType>({
  openModal: false,
  textInfoModal: '',
  params: {},

  toggleModal: ({ text, params }: toggleModalProps) => {}
});

export const useModal = () => useContext(ModalContext);

export const ModalProvider = ({ children }: ModalContextProviderProps) => {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [textInfoModal, setTextInfoModal] = useState<string>('');
  const [params, setParams] = useState<Params>({});

  const toggleModal = useCallback(({ text, params = {} }: toggleModalProps) => {
    setOpenModal(text !== undefined);
    setTextInfoModal(text ?? '');
    setParams(params);
    BrowserUtils.handleBodyScroll(text === undefined);
  }, []);

  const value = useMemo(
    () => ({
      openModal,
      textInfoModal,
      toggleModal,
      params
    }),
    [openModal, textInfoModal, toggleModal, params]
  );
  return <ModalContext.Provider value={value}>{children}</ModalContext.Provider>;
};
