/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

// Easings compatíveis com motion/react
export const EASINGS = {
  easeInOut: [0.4, 0, 0.2, 1] as const,
  easeOut: [0, 0, 0.2, 1] as const,
  easeIn: [0.4, 0, 1, 1] as const,
  linear: [0, 0, 1, 1] as const
} as const;

// Função para criar transições com easing correto
const createTransition = (
  duration: number = 0.5,
  delay: number = 0,
  easing: readonly [number, number, number, number] = EASINGS.easeInOut
) => ({
  duration,
  delay,
  ease: easing
});

export const AnimateFadeIn = {
  initial: { opacity: 0 },
  open: { opacity: 1 },
  openWithDelay1: { opacity: 1 },
  openWithDelay2: { opacity: 1 },
  openWithDelay3: { opacity: 1 },
  openWithBigDelay: { opacity: 1 },
  collapsed: { opacity: 0 },
  exit: { opacity: 0 }
};

// Transições para usar com os estados de AnimateFadeIn
export const AnimateFadeInTransitions = {
  default: createTransition(0.5, 0),
  openWithDelay1: createTransition(0.5, 0.1),
  openWithDelay2: createTransition(0.5, 0.15),
  openWithDelay3: createTransition(0.5, 0.2),
  openWithBigDelay: createTransition(0.5, 0.8),
  exit: createTransition(0.5, 0)
};

// Transições comuns para uso direto
export const TRANSITIONS = {
  fadeIn: createTransition(0.5, 0, EASINGS.easeInOut),
  fadeOut: createTransition(0.5, 0, EASINGS.easeInOut),
  quick: createTransition(0.3, 0, EASINGS.easeInOut),
  slow: createTransition(0.7, 0, EASINGS.easeOut),
  linear: createTransition(0.3, 0, EASINGS.linear)
} as const;

export const AnimateFadeInOpenHeight = {
  open: { opacity: 1, height: 'auto' },
  collapsed: { opacity: 0, height: 0 },
  exit: { opacity: 0, height: 0 }
};

export const AnimateSubmenu = {
  open: { opacity: 1, x: 10 },
  collapsed: { opacity: 0, x: 0 },
  exit: { opacity: 0, x: -10 }
};
export const AnimateSelectContent = (index: number) => ({
  collapsed: { opacity: 0, y: 0, height: 0 },
  open: { opacity: 1, y: 0, height: 'auto' },
  exit: { opacity: 0, y: -52 - 50 * index, height: 0 }
});
