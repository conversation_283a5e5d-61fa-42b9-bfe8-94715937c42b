/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
export const AnimateFadeIn = {
  initial: { opacity: 0 },
  open: { opacity: 1 },
  openWithDelay1: { opacity: 1 },
  openWithDelay2: { opacity: 1 },
  openWithDelay3: { opacity: 1 },
  openWithBigDelay: { opacity: 1 },
  collapsed: { opacity: 0 },
  exit: { opacity: 0 }
};

export const AnimateFadeInOpenHeight = {
  open: { opacity: 1, height: 'auto' },
  collapsed: { opacity: 0, height: 0 },
  exit: { opacity: 0, height: 0 }
};

export const AnimateSubmenu = {
  open: { opacity: 1, x: 10 },
  collapsed: { opacity: 0, x: 0 },
  exit: { opacity: 0, x: -10 }
};
export const AnimateSelectContent = (index: number) => ({
  collapsed: { opacity: 0, y: 0, height: 0 },
  open: { opacity: 1, y: 0, height: 'auto' },
  exit: { opacity: 0, y: -52 - 50 * index, height: 0 }
});
