/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

enum _Breakpoints {
  tablet,
  laptop,
  laptopLarge,
  desktop
}
type BreakpointsType = {
  [key in keyof typeof _Breakpoints]: number;
};

export const BREAKPOINTS: BreakpointsType = {
  tablet: 768,
  laptop: 1024,
  laptopLarge: 1280,
  desktop: 1480
};
