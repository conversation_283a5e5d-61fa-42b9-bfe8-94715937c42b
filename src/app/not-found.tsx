/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { TokenType } from '@/@types/user';
import { useAppContext } from '@/contexts/AppContext';
import { useData } from '@/hooks/useData';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';

const NotFound = () => {
  const { getData } = useData();
  const token = getData<TokenType>({ id: 'token' }) as TokenType;
  const { user } = useAppContext();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (token === null || token === undefined) {
      router.replace('/login');
    } else if (user) {
      const userId = user.AccountId;
      const path = pathname;
      const targetPath = `/${user.AccountId}${path}`;
      fetch(targetPath, {
        method: 'HEAD'
      })
        .then((res) => {
          if (res.ok) {
            router.replace(targetPath);
          } else {
            router.replace(`/${userId}/home`);
          }
        })
        .catch(() => {
          router.replace(`/${userId}/home`);
        });
    }
  }, [user, pathname]);

  return <div>...</div>;
};

export default NotFound;
