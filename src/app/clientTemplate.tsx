'use client';

//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import { usePathname } from 'next/navigation';
import { ReactNode, useCallback } from 'react';

import { BUILD_ID } from '@/BuildId';
import { useAppContext } from '@/contexts/AppContext';
import Footer from '@/templates/components/Commons/footers/Footer';
import Header from '@/templates/components/Commons/headers/Header';
import Sidebar from '@/templates/Home/components/Sidebar';
import { Show } from '@/utils/components/Show';
import './globals.css';

/**
 * Este componente é renderizado no cliente e gerencia a interface do usuário.
 */
export default function ClientTemplate({ children }: { children: ReactNode }) {
  const { user, content, showSidebar, isLoading, environment, isMobile } = useAppContext();
  const pathname = usePathname();

  const showBuildId = useCallback((e: React.MouseEvent) => {
    const existingDiv = document.querySelector('.div-build-id');
    if (existingDiv) {
      existingDiv.remove();
    }
    const div = document.createElement('div');
    div.className = 'fixed z-[12313121314] top-0 right-0 p-2 text-xs div-build-id opacity-50';
    div.textContent = BUILD_ID;
    document.body.appendChild(div);
    setTimeout(() => div.remove(), 2000);
  }, []);
  console.log('teste 123')
  return (
    <>
      <main
        id='body-content'
        className={`flex w-full min-h-[100%] flex-col items-center font-ubuntu ${!user ? 'justify-between' : 'justify-start'}`}>
        <Header />
        <article className='w-full md:w-[744px] lg:w-[1000px] xl:w-[1260px] 2xl:w-[1524px] h-[auto] min-h-[100%] md:py-6 grow'>
          <Show when={!isLoading}>
            <div className='flex flex-row md:h-full max-md:flex-col-reverse justify-center'>
              <Show when={!isMobile}>
                <Sidebar />
              </Show>

              <div className={`${showSidebar && !isMobile ? 'md:w-9/12' : 'w-full'} h-full`}>
                <motion.div
                  key={`page-${pathname}-${content?.ProposalId || ''}`}
                  className='flex flex-col w-full p-4 md:p-0 pb-10'
                  initial={{ y: -30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: 50, opacity: 0 }}
                  transition={{ ease: 'easeOut', duration: 0.35 }}>
                  {children}
                </motion.div>
              </div>
            </div>
          </Show>
        </article>
        <Footer />
        <div className='fixed top-0 right-0 w-[36px] h-[36px] button-build-id z-[12313121312]'>
          <button className='w-[36px] h-[36px]' onClick={showBuildId} />
        </div>
      </main>
    </>
  );
}
