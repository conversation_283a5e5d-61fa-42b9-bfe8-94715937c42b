/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Metadata } from 'next';

import FaqScreen from '@/templates/Faq/Faq';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = await {
    title: 'Dúvidas Frequentes'
  };
  return metadata;
}
export const dynamic = 'force-static';

export default function DuvidasFrequentes() {
  return <FaqScreen />;
}
