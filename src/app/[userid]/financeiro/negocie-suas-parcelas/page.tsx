/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Metadata } from 'next';
import React, { memo } from 'react';

import NegotiateYourInstallmentsScreen from '@/templates/Financial/NegotiateYourInstallmentsScreen';

export const metadata: Metadata = {
  title: 'Negocie suas parcelas'
};

const NegocieSuasParcelas = React.memo(() => {
  return <NegotiateYourInstallmentsScreen />;
});
NegocieSuasParcelas.displayName = 'NegocieSuasParcelas';
export default memo(NegocieSuasParcelas);
