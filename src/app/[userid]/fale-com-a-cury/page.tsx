/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Metadata } from 'next';
import React from 'react';

import TalkToCuryMenuScreen from '@/templates/TalkToCury/TalkToCuryMenuScreen';

export const metadata: Metadata = {
  title: 'Solicitar atendimento'
};

const FaleComACury = React.memo(() => {
  return <TalkToCuryMenuScreen />;
});
FaleComACury.displayName = 'SolicitarAtendimento';
export default FaleComACury;
