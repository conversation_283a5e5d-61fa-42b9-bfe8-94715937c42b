/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { BUILD_ID } from '@/BuildId';
import { GoogleTagManager } from '@next/third-parties/google';
import { Metadata } from 'next';
import Head from 'next/head';

export const metadata: Metadata = {
  metadataBase: new URL('https://cliente.cury.net'),
  title: {
    default: 'Cury',
    template: '%s | Cury'
  },
  authors: {
    name: 'StudioWox',
    url: 'https://studiowox.com.br'
  },
  description:
    'Explore o portal da Cury Construtora e acompanhe todos os detalhes do seu futuro lar em um só lugar. Aqui você tem acesso exclusivo ao progresso da obra com atualizações visuais, detalhes financeiros das parcelas, opções para negociação e antecipação de pagamentos, além de informações completas sobre seu financiamento. Facilite sua vida com agendamento de serviços e suporte direto para esclarecer suas dúvidas mais comuns. A Cury está comprometida em fornecer transparência e comodidade para seus clientes.'
};
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang='pt-br' className='h-full min-h-full'>
      <Head>
        {/* <Script
          strategy='afterInteractive'
          src={`https://www.googletagmanager.com/gtag/js?id=G-JRH5ET2ZKJ`}
        />
        <Script
          id='google-analytics'
          strategy='afterInteractive'
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-JRH5ET2ZKJ');
            `
          }}
        /> */}

        {/* <meta name='viewport' content='width=device-width, initial-scale=1, user-scalable=no' /> */}
        <meta name='viewport' content='width=device-width' />
        <meta name='build-id' content={BUILD_ID} />
      </Head>
      <body className={'h-full min-h-full overflow-y-scroll'}>
        {children}
        <GoogleTagManager gtmId='G-JRH5ET2ZKJ' />
      </body>
    </html>
  );
}
