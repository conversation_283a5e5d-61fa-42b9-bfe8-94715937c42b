/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { MiscUtils } from '@/utils/MiscUtils';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useRef } from 'react';

function RedirectComp() {
  const searchParams = useSearchParams();
  const type = searchParams.get('type');
  const codEmpreendimento = searchParams.get('codEmpreendimento') ?? '';
  const inputValueCod = MiscUtils.calculateCode(codEmpreendimento);
  const url =
    type === 'pasta'
      ? 'https://manual1.com.br/manuais/api/cury/access_man.php'
      : 'https://www.sigmacivil.com.br/us/input_cury.asp';
  const formRef = useRef(null);

  useEffect(() => {
    if (formRef.current) {
      (formRef.current as HTMLFormElement).submit();
    }
  }, []);
  return (
    <div>
      Redirect...
      <form
        ref={formRef}
        name='form1'
        method='post'
        action={url}
        className='flex cursor-pointer h-12  bg-white w-11/12 flex-row items-center rounded max-md:flex-col max-md:h-[132px] max-md:w-full  max-md:bg-transparent md:mb-3 focus:outline-none text-center md:text-left'>
        <input type='hidden' name='num_emp' value={codEmpreendimento} />
        <input type='hidden' name='idemp' value={codEmpreendimento} />
        <input type='hidden' name='tipo' value='S' />
        <input type='hidden' name='cod' value={inputValueCod} />
      </form>
    </div>
  );
}

const Redirect = React.memo(() => {
  return <RedirectComp />;
});

Redirect.displayName = 'Redirect';
export default Redirect;
