import { validateImpersonationToken } from '@/server/services/ImpersonationService';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token = searchParams.get('impersonate_token');

  if (!token) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
  try {
    const cookieStore = await cookies();

    const validationResponse = await validateImpersonationToken(token);
    if (!validationResponse.success) {
      cookieStore.delete('impersonate_token');
      return NextResponse.redirect(new URL('/login', request.url));
    }
    cookieStore.set('token', validationResponse.temp_token || 'temp-token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 7200, // 2 horas
      path: '/'
    });

    cookieStore.set('AccountId', validationResponse.account_id || '0', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 7200,
      path: '/'
    });
    cookieStore.set('is_impersonating', 'true', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      //   sameSite: 'lax',
      maxAge: 7200
    });

    // Redirecionar para a página inicial ou dashboard
    return NextResponse.redirect(new URL('/home', request.url));
  } catch (error) {
    console.error('Erro ao validar token de impersonificação:', error);
    return NextResponse.redirect(new URL('/login', request.url));
  }
}
