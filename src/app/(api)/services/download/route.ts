/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
import { NextRequest, NextResponse } from 'next/server';

/**
 * Lista de domínios permitidos para download de arquivos
 * Apenas URLs desses domínios são aceitas por motivos de segurança
 */
//# SIENGE_BASE_URL="https://api.sienge.com.br/curyhomolog/public/api/"
const ALLOWED_DOMAINS = [
  '*.sienge.com.br',
  'api.sienge.com.br',
  'curyempreendimentos.curyhomolog.sienge.com.br',
  'curyhomolog.curyempreendimentos.sienge.com.br',
  'curyempreendimentos.sienge.com.br',
  's3clienteappcury.s3.amazonaws.com',
  'curyhomolog.sienge.com.br',
  'cury.sienge.com.br',
  's3.amazonaws.com',
  'cury.net',
  'cliente.cury.net',
  'homolog.cliente.cury.net'
];

/**
 * Valida se a URL fornecida pertence a um domínio permitido
 * @param url - URL a ser validada
 * @returns true se a URL for de um domínio permitido, false caso contrário
 */
function isAllowedDomain(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    return ALLOWED_DOMAINS.some((domain) => {
      return hostname === domain || hostname.endsWith(`.${domain}`);
    });
  } catch {
    // URL inválida
    return false;
  }
}

export async function GET(request: NextRequest) {
  const url = request.nextUrl.searchParams.get('url');
  const filename = request.nextUrl.searchParams.get('filename');

  if (!url) {
    return NextResponse.json({ error: 'URL não fornecida' }, { status: 400 });
  }

  // Validação de segurança: verificar se a URL é de um domínio permitido
  if (!isAllowedDomain(url)) {
    console.warn(`Tentativa de download de domínio não permitido: ${url}`);
    return NextResponse.json(
      {
        error: 'Domínio não autorizado para download',
        message:
          'Por motivos de segurança, apenas downloads de domínios autorizados são permitidos.'
      },
      { status: 403 }
    );
  }

  try {
    const res = await fetch(url, {
      headers: {
        'User-Agent': 'Cury-Cliente-App/1.0'
      }
    });

    if (!res.ok) {
      throw new Error(`Erro HTTP: ${res.status} ${res.statusText}`);
    }

    const blob = await res.blob();

    return new NextResponse(blob, {
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${filename || 'download'}"`,
        'Content-Length': blob.size.toString(),
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store'
      }
    });
  } catch (error) {
    console.error('Erro ao baixar o arquivo:', error);
    return NextResponse.json(
      {
        error: 'Erro ao baixar o arquivo',
        message: 'Não foi possível processar o download do arquivo solicitado.'
      },
      { status: 500 }
    );
  }
}
