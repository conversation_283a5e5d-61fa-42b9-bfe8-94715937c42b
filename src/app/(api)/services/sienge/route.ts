/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { NextRequest, NextResponse } from 'next/server';
import { cache } from 'react';

const urlsService = {
  installment:
    'bulk-data/v1/customer-extract-history?includeRevokedBillReceivable=false&customerId=||CodigoSienge__c||&startDueDate=1900-11-30&endDueDate=2323-11-30&unitNumber=||UnidadeAtivoName||',
  ir: 'v1/customer-income-tax/report/pdf?customerId=||customerId||&companyId=||companyId||&year=||year||',
  boleto:
    'v1/payment-slip-notification/?billReceivableId=||billReceivableId||&installmentId=||installmentId||'
};

const getTokenAndEndpoint = cache(async () => {
  const username = process.env.SIENGE_USERNAME;
  const password = process.env.SIENGE_PASSWORD;

  const token = await Buffer.from(`${username}:${password}`).toString('base64');
  const endpoint = process.env.SIENGE_BASE_URL;

  return { token, endpoint };
});

async function executeQuery(query: string, revalidate: number = 600) {
  const { token, endpoint } = await getTokenAndEndpoint();
  const url = `${endpoint}${query}`;
  console.log('executeQuery');
  console.log(url);
  try {
    const response = await fetch(url, {
      headers: {
        'Authorization': `Basic ${token}`,
        'Content-Type': 'application/json'
      },
      next: { revalidate: revalidate }
    });

    if (response.ok) {
      return await response.json();
    }
  } catch (e) {
    console.error(e);
  }

  return null;
}

export async function POST(request: NextRequest) {
  const body = await request.json();

  const {
    service,
    revalidate,
    CodigoSienge__c,
    UnidadeAtivoName,
    customerId,
    companyId,
    year,
    billReceivableId,
    installmentId
  } = body;

  let url = urlsService[service as keyof typeof urlsService];

  let revalidateFinal = revalidate;
  switch (service) {
    case 'installment':
      url = url
        .replace('||CodigoSienge__c||', encodeURIComponent(CodigoSienge__c))
        .replace('||UnidadeAtivoName||', encodeURIComponent(UnidadeAtivoName));

      break;
    case 'ir':
      url = url
        .replace('||customerId||', encodeURIComponent(customerId))
        .replace('||companyId||', encodeURIComponent(companyId))
        .replace('||year||', encodeURIComponent(year));

      break;

    case 'boleto':
      url = url
        .replace('||billReceivableId||', encodeURIComponent(billReceivableId))
        .replace('||installmentId||', encodeURIComponent(installmentId));
      revalidateFinal = 0;

      break;
  }

  try {
    const result = await executeQuery(url, revalidateFinal);

    if (result) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json({ error: 'Failed to execute query' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error executing query:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
