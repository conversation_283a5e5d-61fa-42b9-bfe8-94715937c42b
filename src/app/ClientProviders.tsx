/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { InformeProvider } from '@/contexts/useInforme';
import { LoadingProvider, ModalProvider } from '@/hooks';
import { ReactNode } from 'react';

interface ClientProvidersProps {
  children: ReactNode;
}

/**
 * Componente cliente que encapsula todos os providers que precisam ser executados no cliente
 */
export default function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <LoadingProvider>
      <InformeProvider>
        <ModalProvider>{children}</ModalProvider>
      </InformeProvider>
    </LoadingProvider>
  );
}
