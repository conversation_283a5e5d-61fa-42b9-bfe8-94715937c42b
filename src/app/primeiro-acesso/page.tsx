/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Metadata } from 'next';
import React from 'react';

import FirstLoginScreen from '@/templates/Auth/FirstLoginScreen/FirstLoginScreen';

export const metadata: Metadata = {
  title: 'Primeiro acesso.'
};

const PrimeiroAcesso = React.memo(() => {
  return <FirstLoginScreen />;
});
PrimeiroAcesso.displayName = 'PrimeiroAcesso';
export default PrimeiroAcesso;
