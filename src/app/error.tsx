/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { useData } from '@/hooks/useData';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';

export default function ErrorBoundary({
  _error,
  _reset
}: Readonly<{
  _error: Error & { digest?: string };
  _reset: () => void;
}>) {
  // return <CustomErrorBoundaryScreen error={error} />;
  const { destroyData } = useData();
  // async function clearCookies(): Promise<void> {
  //   await clearAuthCookies();
  //   destroyData();
  //   setTimeout(() => {
  //     console.log('redirecting to login');
  //     window.location.href = '/login';
  //   }, 1000);
  // }

  return (
    <div className='flex flex-col items-center  h-screen'>
      <h1 className='text-2xl font-bold text-navy-blue text-center'>
        Estamos passando por uma instabilidade.
      </h1>
      <Buttonblue
        text='Clique aqui para logar novamente.'
        background='bg-navy-blue'
        color='text-white'
        onClick={() => {
          // clearCookies();
          // window.location.href = '/login';
        }}
        classExtra='mt-4 bg-navy-blue text-white p-4'
      />
    </div>
  );
}
