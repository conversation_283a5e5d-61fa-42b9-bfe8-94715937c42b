/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use server';

import { validateImpersonationToken } from '@/server/services/ImpersonationService';
import { NextRequest, NextResponse } from 'next/server';
import { clearAuthCookies } from './server/services/cookiesService';

const AUTH_CONFIG = {
  publicRoutes: [
    '/login',
    '/verifica-codigo',
    '/login',
    '/verifica-codigo',
    '/recuperar-senha',
    '/senha-definitiva',
    '/primeiro-acesso',
    '/termos-de-uso',
    '/politica-de-privacidade',
    '/esqueci-minha-senha',
    '/nova-senha',
    '/sou-vizinho',
    '/services/impersonate/login',
    '/auth/impersonate'
  ],
  ignoredRoutes: ['/api/', 'out', '/_next/', '/favicon.ico'],
  isStaticFile: (pathname: string) => pathname.includes('.'),
  defaultAuthenticatedRoute: (accountId: string) => `/${accountId}/home`,
  loginRoute: '/login'
};

export async function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;

  // Processar token de impersonação na URL
  if (search && search.includes('impersonate_token=')) {
    const token = new URLSearchParams(search).get('impersonate_token');

    if (token) {
      try {
        const validation = await validateImpersonationToken(token);

        if (validation.success) {
          await clearAuthCookies(false);
          const response = NextResponse.redirect(
            // eslint-disable-next-line no-useless-escape
            new URL(pathname + search.replace(/[\?&]impersonate_token=[^&]+/, ''), request.url)
          );

          response.cookies.set('token', validation.temp_token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 7200
          });

          response.cookies.set('AccountId', validation.account_id, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 7200
          });

          response.cookies.set('is_impersonating', 'true', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 7200
          });

          return response;
        }
      } catch (error) {
        console.error('Erro na validação do token:', error);
      }
    }

    // Se chegou aqui, houve algum erro
    // return NextResponse.redirect(new URL('/login', request.url));
  }

  //Verificar e validar impersonação existente
  const impersonateToken = request.cookies.get('impersonate_token')?.value;
  const userToken = request.cookies.get('token')?.value;

  if (impersonateToken && !userToken) {
    try {
      const validation = await validateImpersonationToken(impersonateToken);

      if (validation && validation.success) {
        const response = NextResponse.next();
        response.cookies.set('token', validation.temp_token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7200
        });

        response.cookies.set('AccountId', validation.account_id, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7200
        });

        response.cookies.set('is_impersonating', 'true', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7200
        });

        return response;
      }
      const response = NextResponse.redirect(new URL('/login', request.url));
      response.cookies.delete('impersonate_token');
      return response;
    } catch (error) {
      console.error('Erro ao validar token de impersonação:', error);
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  if (shouldIgnoreRoute(pathname)) {
    return NextResponse.next();
  }

  const isPublic = isPublicRoute(pathname);
  const accountId = request.cookies.get('AccountId')?.value;

  // Redireciona usuário autenticado tentando acessar rota pública
  if (
    isPublic &&
    userToken &&
    !pathname.startsWith('/termos-de-uso') &&
    !pathname.startsWith('/politica-de-privacidade') &&
    !(pathname === '/login' && request.method === 'POST')
  ) {
    const homeUrl = accountId ? AUTH_CONFIG.defaultAuthenticatedRoute(accountId) : '/';
    return NextResponse.redirect(new URL(homeUrl, request.url));
  }

  // Redireciona para login se tentar acessar rota privada sem autenticação
  if (!isPublic && !userToken) {
    const loginUrl = new URL(AUTH_CONFIG.loginRoute, request.url);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

// Funções auxiliares
function isPublicRoute(pathname: string): boolean {
  return AUTH_CONFIG.publicRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );
}

function shouldIgnoreRoute(pathname: string): boolean {
  return (
    AUTH_CONFIG.ignoredRoutes.some((prefix) => pathname.startsWith(prefix)) ||
    AUTH_CONFIG.isStaticFile(pathname)
  );
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)']
};
