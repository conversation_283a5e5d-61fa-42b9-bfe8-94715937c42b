'use server';
import { API_URL } from '@/constants';
import { cookies } from 'next/headers';
import { getServerToken } from '../services/cookiesService';

/**
 * Constrói URL com parâmetros de consulta
 */
export async function buildApiUrl(
  path: string,
  queryParams?: Record<string, string | number | boolean>
): Promise<string> {
  console.log('buildApiUrl 13', path);
  console.log('buildApiUrl 14', queryParams);
  const baseUrl = path.startsWith('http') || path.startsWith('services') ? '' : API_URL;
  const url = baseUrl ? new URL(`${baseUrl}/${path}`) : path;
  if (queryParams && baseUrl !== '') {
    const urlObj = url as URL;
    Object.entries(queryParams).forEach(([key, value]) => {
      urlObj.searchParams.append(key, String(value));
    });
  }
  return typeof url === 'string' ? url : url.toString();
}

/**
 * Obtém cabeçalhos para requisição
 */
export async function getApiHeaders(requiresAuth: boolean = true): Promise<HeadersInit> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };

  if (requiresAuth) {
    const token = await getServerToken();

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Adicionar token de impersonificação se existir
    const cookieStore = await cookies();
    const impersonateToken = cookieStore.get('impersonate_token')?.value;
    if (impersonateToken) {
      headers['X-Impersonation-Token'] = impersonateToken;
    }
  }

  return headers;
}
