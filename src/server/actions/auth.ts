/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use server';

import { revalidatePath } from 'next/cache';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { z } from 'zod';

import { LoginType, PreLoginType, VerificationCodeResponse } from '@/@types/auth';
import { TokenType, User } from '@/@types/user';
import { StringUtils } from '@/utils/StringUtils';
import { apiPost } from '../services/api';
import { clearAuthCookies, getAuthCookies, setAuthCookies } from '../services/cookiesService';

// Schema de validação para login
const loginSchema = z.object({
  userLogin: z.string().min(11, 'CPF/CNPJ inválido'),
  password: z.string().min(6, 'Senha obrigatória'),
  deviceinfo: z.string(),
  rememberMe: z.string().transform((val) => val === 'true')
});

// Interface para resposta da ação de login
interface LoginActionResponse {
  success: boolean;
  error?: string;
  isFirstLogin?: boolean;
  needsVerification?: boolean;

  redirectTo?: string;
  tokenData?: any;
}

/**
 * Verifica pré-login para determinar o estado da conta
 */
async function checkPreLoginAction(userLogin: string, password: string): Promise<number | string> {
  try {
    const cleanedUserLogin = StringUtils.cleanData(userLogin);

    return await apiPost<PreLoginType>(
      'prelogin',
      {
        userLogin: cleanedUserLogin,
        password
      },
      undefined,
      {
        requiresAuth: false
      }
    );
  } catch (error) {
    console.error('Erro no prelogin:', error);
    return `error: ${error}`;
    // throw new Error('Falha na verificação de login');
  }
}

/**
 * Realiza o login do usuário
 */
export async function loginAction(
  _prevState: any,
  formData: FormData
): Promise<LoginActionResponse> {
  try {
    // Validar campos do formulário
    const validatedFields = loginSchema.safeParse({
      userLogin: formData.get('userLogin'),
      password: formData.get('password'),
      deviceinfo: formData.get('deviceinfo'),
      rememberMe: formData.get('rememberMe')
    });

    if (!validatedFields.success) {
      return { success: false, error: 'Dados de login inválidos. Verifique CPF/CNPJ e senha.' };
    }

    const { userLogin, password, deviceinfo: deviceinfoStr, rememberMe } = validatedFields.data;
    const deviceinfo = JSON.parse(deviceinfoStr || '{}');
    const preLoginResult = await checkPreLoginAction(userLogin, password);
    await clearAuthCookies();
    const loginResponse = await apiPost<{ data: LoginType }>(
      'v2/auth/login',
      {
        userLogin: StringUtils.cleanData(userLogin),
        password,
        deviceinfo,
        adminlogin: 'false',
        remember: rememberMe.toString()
      },
      undefined,
      {
        requiresAuth: false,
        revalidatePaths: ['login'],
        shouldRedirectOnAuthError: false
      }
    );

    const loginResult = loginResponse.data;
    const tokenData = {
      type: 'SET_TOKEN',
      device_info: deviceinfo,
      access_token: loginResult.token.access_token,
      expires_at: loginResult.token.expires_at,
      AccountId: loginResult.user.AccountId
    };

    // Armazenar cookies de autenticação
    await setAuthCookies(
      loginResult.token.access_token,
      loginResult.token.expires_at,
      loginResult.user.AccountId,
      deviceinfo,
      {
        cpf: loginResult.user.CPF__c || '',
        cnpj: loginResult.user.CNPJ__c || '',
        email: loginResult.user.Email__c || '',
        userLogin: StringUtils.cleanData(userLogin)
      },
      rememberMe
    );

    // Verificar se é primeiro login ou precisa verificação
    if (preLoginResult === 0 || !loginResult.user.email_verified_at) {
      return {
        success: true,
        isFirstLogin: preLoginResult === 0,
        needsVerification: true,
        redirectTo: '/senha-definitiva',
        tokenData
      };
    }

    // Login normal bem-sucedido
    const path = `/${loginResult.user.AccountId}/home`;
    revalidatePath(path);
    // redirect(`/${loginResult.user.AccountId}/home`);
    return {
      success: true,
      redirectTo: path,
      tokenData
    };
  } catch (error) {
    console.error('Erro no servidor durante login:', error);

    // Verificar se é erro de autenticação
    const isAuthError = error instanceof Error && error.message === 'AUTH_ERROR';

    return {
      success: false,
      error: isAuthError
        ? 'Acesso negado: Verifique seu usuário e senha.'
        : 'Acesso negado: Verifique seu usuário e senha. A senha do Portal do Cliente antigo não é válida.'
    };
  }
}

/**
 * Realiza o logout de um dispositivo específico
 * Esta função não redireciona automaticamente
 */
export async function logoutFromDevice(deviceinfo: string): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    await apiPost(
      'logout',
      {
        deviceinfo
      },
      undefined,
      {
        requiresAuth: true,
        revalidatePaths: [],
        shouldRedirectOnAuthError: false
      }
    );

    return {
      success: true
      // wasCurrentDevice: isCurrentDevice
    };
  } catch (error) {
    console.error('Erro ao realizar logout do dispositivo:', error);

    // Se foi erro de autenticação no dispositivo atual, limpar cookies de qualquer forma
    const isAuthError = error instanceof Error && error.message === 'AUTH_ERROR';
    if (isAuthError) {
      await clearAuthCookies();
      return {
        success: true,
        message: 'Sessão expirada'
      };
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

export async function saveTempData(
  CNPJ__c: string,
  CPF__c: string,
  Email__c: string,
  deviceInfo: string
) {
  const cookieStore = await cookies();
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    // maxAge: 60 * 60, // 1 hora
    maxAge: 60 * 60 * 24, // 1 dia
    path: '/'
  };
  cookieStore.set('user.temp-cnpj', CNPJ__c || '', cookieOptions);
  cookieStore.set('user.temp-cpf', CPF__c || '', cookieOptions);
  cookieStore.set('user.temp-email', Email__c || '', cookieOptions);
  cookieStore.set('user.temp-deviceinfo', deviceInfo || '', cookieOptions);
}

export async function verifyCodeAction(
  code: string
): Promise<{ success: boolean; error?: string; redirectTo?: string }> {
  try {
    const cookieStoreVerifyCode = await cookies();
    const cnpj = cookieStoreVerifyCode.get('user.temp-cnpj')?.value || '';
    const cpf = cookieStoreVerifyCode.get('user.temp-cpf')?.value || '';
    const email = cookieStoreVerifyCode.get('user.temp-email')?.value || '';
    const deviceInfo = cookieStoreVerifyCode.get('user.temp-deviceinfo')?.value || '';

    // Validar dados necessários
    if (!email) {
      return {
        success: false,
        error: 'Informações de usuário incompletas. Reinicie o processo.',
        redirectTo: '/esqueci-senha'
      };
    }

    const userLogin = cpf ? cpf : cnpj;
    const cleanedUserLogin = StringUtils.cleanData(userLogin);
    const response = await apiPost<{ data: LoginType }>(
      'verificationCode',
      {
        userLogin: cleanedUserLogin,
        email,
        code,
        deviceinfo: deviceInfo
      },
      undefined,
      {
        requiresAuth: false,
        revalidatePaths: [],
        shouldRedirectOnAuthError: false
      }
    );

    const result = response.data;

    // Armazenar token temporário para próxima etapa
    const cookieStore = await cookies();
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60, // 1 hora
      path: '/'
    };

    // Armazenar apenas o necessário para próxima etapa
    cookieStore.set('user.temp-token', result.token.access_token, cookieOptions);
    return { success: true, redirectTo: '/nova-senha' };
  } catch (error: any) {
    console.error('Erro ao verificar código:', error);

    // Tratamento mais específico de erros
    if (error.response) {
      const status = error.response.status;

      if (status === 401) {
        return { success: false, error: 'Código de verificação inválido ou expirado.' };
      } else if (status === 429) {
        return {
          success: false,
          error: 'Muitas tentativas. Aguarde alguns minutos e tente novamente.'
        };
      }
    }

    return {
      success: false,
      error: 'Erro ao verificar código. Tente novamente.'
    };
  }
}

/**
 * Interface para o resultado da atualização de dados
 */
interface RefreshDataResult {
  success: boolean;
  userData?: User;
  contentSelected?: number;
  expired?: boolean;
  message?: string;
}

/**
 * Interface para receber token de dispositivos externos
 */
interface ExternalTokenData {
  token: TokenType;
  accountId: string;
  deviceInfo: string;
}

/**
 * Atualiza os dados do usuário usando o token atual ou salva um novo token
 * Se receber dados de token, salva-os nos cookies
 * Se não receber dados, tenta usar o token atual para atualizar dados
 */
/**
 * Salva dados externos de token nos cookies
 */
async function saveExternalTokenData(externalData: ExternalTokenData): Promise<RefreshDataResult> {
  const cookieStore = await cookies();
  const { token, accountId, deviceInfo } = externalData;

  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 60 * 24 * 7, // 7 dias
    path: '/'
  };

  // Salvar token e dados relacionados
  cookieStore.set('token', token.access_token, cookieOptions);
  cookieStore.set('expires_at', token.expires_at, cookieOptions);
  cookieStore.set('AccountId', accountId, cookieOptions);
  cookieStore.set('device_info', deviceInfo, cookieOptions);

  return { success: true };
}

/**
 * Verifica se o token atual é válido
 */
async function verifyCurrentToken(): Promise<{
  isValid: boolean;
  result?: RefreshDataResult;
}> {
  const cookieStore = await cookies();

  // Verificar se temos token de autorização
  const tokenCookie = cookieStore.get('token');
  const expiresAtCookie = cookieStore.get('expires_at');

  if (!tokenCookie) {
    return {
      isValid: false,
      result: {
        success: false,
        message: 'Sem token de autorização'
      }
    };
  }

  // Verificar expiração do token (se tivermos informação)
  const tokenData = typeof tokenCookie !== 'string' ? tokenCookie?.value : tokenCookie;
  const expiresAtCookieData =
    typeof expiresAtCookie !== 'string' ? expiresAtCookie?.value : expiresAtCookie;

  try {
    if (expiresAtCookieData) {
      const expirationDate = new Date(expiresAtCookieData);
      const currentDate = new Date();

      if (expirationDate <= currentDate) {
        return {
          isValid: false,
          result: {
            success: false,
            expired: true,
            message: 'Token expirado'
          }
        };
      }
    }
  } catch (e) {
    // Se não conseguimos parsear o token, continuamos com o valor como está
    // clearAuthCookies();
    redirect('/login');
  }

  return { isValid: true };
}

/**
 * Atualiza os tokens nos cookies
 */
async function updateTokenInCookies(newToken: TokenType): Promise<void> {
  const cookieStore = await cookies();
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 60 * 24 * 7, // 7 dias
    path: '/'
  };

  cookieStore.set('token', newToken.access_token, cookieOptions);
  cookieStore.set('expires_at', newToken.expires_at, cookieOptions);
}

/**
 * Busca dados atualizados da API
 */
async function fetchUserDataFromApi(): Promise<RefreshDataResult> {
  const cookieStoreUserData = await cookies();
  const deviceInfo = cookieStoreUserData.get('device_info')?.value || '';
  const response = await apiPost<{ data: { user: User; token: TokenType } }>(
    'refresh-data',
    {
      deviceinfo: deviceInfo
    },
    undefined,
    {
      requiresAuth: true,
      revalidatePaths: [],
      shouldRedirectOnAuthError: false
    }
  );

  if (!response || !response.data) {
    return {
      success: false,
      message: 'Falha ao obter dados atualizados'
    };
  }

  const { user, token: newToken } = response.data;

  // Atualizar token se recebemos um novo
  if (newToken && newToken.access_token) {
    await updateTokenInCookies(newToken);
  }

  // Obter contentSelected dos cookies
  const cookieStore = await cookies();
  const contentSelectedCookie = cookieStore.get('contentSelected')?.value;
  const contentSelected = contentSelectedCookie ? parseInt(contentSelectedCookie, 10) : 0;

  return {
    success: true,
    userData: user,
    contentSelected
  };
}

/**
 * Atualiza os dados do usuário usando o token atual ou salva um novo token
 * Se receber dados de token, salva-os nos cookies
 * Se não receber dados, tenta usar o token atual para atualizar dados
 */
export async function refreshUserData(
  externalData?: ExternalTokenData
): Promise<RefreshDataResult> {
  try {
    // Se recebemos dados externos, salvamos nos cookies
    if (externalData) {
      return await saveExternalTokenData(externalData);
    }

    // Verificar validade do token atual
    const tokenStatus = await verifyCurrentToken();
    if (!tokenStatus.isValid) {
      return tokenStatus.result as RefreshDataResult;
    }

    // Buscar dados atualizados da API
    return await fetchUserDataFromApi();
  } catch (error) {
    console.error('Erro ao atualizar dados do usuário:', error);

    // Verificar se é erro de autenticação
    const isAuthError = error instanceof Error && error.message === 'AUTH_ERROR';
    clearAuthCookies();
    return {
      success: false,
      expired: isAuthError,
      message: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
