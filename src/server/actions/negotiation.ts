'use server';

import { Content } from '@/@types/content';
import { NegotiateType, ReturnType, SelectedInstallment } from '@/@types/installments.d';
import { NegotiationConditionsType } from '@/@types/negotiation';
import { MiscUtils } from '@/utils/MiscUtils';
import { apiPost } from '../services/api';

/**
 * Verifica o tipo de ação de negociação
 */
const checkNegotiateAction = async (action: string): Promise<number> => {
  if (action === 'simulacao' || action === 'NegociacaoParcialSimulacao') return 1;
  if (action === 'antecipar') return 2;
  return 3;
};

/**
 * Prepara os dados para negociação com base no status (adimplente/inadimplente)
 */
export const prepareNegotiationData = async (
  paid: boolean,
  user: any,
  content: Content,
  selectedInstallments: { selectedInstallment: SelectedInstallment[] } | null,
  negotiateData: NegotiateType,
  billReceivableId: number | null
): Promise<NegotiateType> => {
  if (paid) {
    return {
      type: 'adimplente',
      action: 'antecipar',
      codigo_sienge_user: user.CodigoSienge__c,
      assetId: content.AssetId,
      billReceivableId: billReceivableId as number,
      installments: selectedInstallments?.selectedInstallment
    };
  } else {
    const installmentsIds =
      selectedInstallments?.selectedInstallment.map((item) => Number(item.id)) || [];
    if (negotiateData.action === 'NegociacaoParcialSimulacao') {
      return {
        type: 'inadimplente',
        action: 'NegociacaoParcialSimulacao',
        assetId: content.AssetId,
        billReceivableId: billReceivableId as number,
        installmentsIds
      };
    }
    return {
      ...negotiateData,
      installmentsIds
    };
  }
};

/**
 * Processa a resposta da API de negociação
 */
const processNegotiateResponse = async (
  returnAPI: ReturnType,
  negotiateData: NegotiateType
): Promise<{
  success: boolean;
  negotiatedConditions?: NegotiationConditionsType;
  caseNumber?: string;
  returnTitle?: string;
  returnText?: string;
  area?: string;
}> => {
  const actionType = await checkNegotiateAction(negotiateData.action);

  // Caso de simulação
  if (actionType === 1) {
    const negotiatedConditionsResponse = MiscUtils.typeNegotiation(
      negotiateData.type,
      negotiateData.action
    )
      ? [returnAPI]
      : returnAPI;

    return {
      success: true,
      negotiatedConditions: negotiatedConditionsResponse,
      area: 'condicoes'
    };
  }

  // Caso de sucesso em outras ações
  if (returnAPI.success) {
    const textReturnApi =
      actionType === 2
        ? `Sua solicitação já foi enviada e dentro de 1 dia(s) seu boleto estará disponível aqui no Portal e no seu e-mail. Acompanhe sua antecipação através do caso <strong>${returnAPI.caso.CaseNumber}</strong>.`
        : `Sua solicitação já foi enviada e dentro de 1 dia(s) seu boleto estará disponível aqui no Portal e no seu e-mail. Acompanhe sua negociação através do caso <strong>${returnAPI.caso.CaseNumber}</strong>.<br></br> Assessoria de Cobrança responsável: Regional Sp. Telefone: (11) 3117-1300.`;

    return {
      success: true,
      caseNumber: returnAPI.caso.CaseNumber,
      returnTitle: 'Caso registrado',
      returnText: textReturnApi,
      area: 'return'
    };
  }

  return { success: false };
};

/**
 * Executa a negociação no servidor
 */
export const executeNegotiation = async (
  negData: NegotiateType
): Promise<{
  result: ReturnType | NegotiationConditionsType | null;
  processedResponse: any;
  error?: boolean;
}> => {
  try {
    const response = await apiPost('negotiate', { body: negData });
    if (response.errors !== null) {
      return { result: null, processedResponse: null, error: true };
    }

    const result = await response.data.negotiate;
    const processedResponse = await processNegotiateResponse(result as ReturnType, negData);
    return { result, processedResponse };
  } catch (error) {
    console.error('Erro na negociação:', error);
    return { result: null, processedResponse: null, error: true };
  }
};
