/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use server';

import { User, UserResponse } from '@/@types/user';
import { WhereTokens } from '@/@types/whereTokens';
import { apiGet, apiPost, apiPut } from '../services/api';
import { clearAuthCookies } from '../services/cookiesService';

export const fetchUserData = async (accountId: string) => {
  const response = await apiGet('showuser', { AccountId: accountId }, { cache: 'force-cache' });
  return response.data;
};

import { PaginatedResponse } from '@/@types/pagination';
import { Content } from '@/@types/content';
import { ApiError } from '../utils/ApiError';

export const getUserAssets = async (
  page: number = 1,
  perPage: number = 20
): Promise<PaginatedResponse<Content>> => {
  try {
    const response = await apiGet('v2/assets/getUserAssets', {
      page,
      perPage
    });
    console.log('response');
    console.log(response);

    return {
      contents: response.data.contents || [],
      pagination: response.data.pagination || {
        total: 0,
        per_page: perPage,
        current_page: page,
        last_page: 1,
        from: 0,
        to: 0
      }
    };
  } catch (error) {
    await clearAuthCookies();
    console.error('Erro ao buscar ativos do usuário:', error);

    return {
      contents: [],
      pagination: {
        total: 0,
        per_page: perPage,
        current_page: page,
        last_page: 1,
        from: 0,
        to: 0
      }
    };
  }
};

/**
 * Atualiza dados do perfil do usuário
 */
export async function updateUserProfile(
  userId: string,
  profileData: Partial<User>
): Promise<UserResponse | { data: null; message: string }> {
  console.log('updateUserProfile');
  console.log(userId);
  console.log(profileData);
  try {
    if (!userId) {
      return { data: null, message: 'ID de usuário não fornecido' };
    }

    const response = await apiPost<{ data: User }>(`updateuser`, profileData);

    return {
      data: response.data
    };
  } catch (error) {
    console.error('Erro ao atualizar perfil do usuário:', error);
    return {
      data: null,
      message: error instanceof Error ? error.message : 'Erro ao atualizar perfil'
    };
  }
}

/**
 * Busca todos os dispositivos conectados à conta do usuário atual
 *
 * @returns Lista de dispositivos conectados
 */
export async function getConnectedDevices(currentDeviceInfo: string): Promise<{
  success: boolean;
  devices?: WhereTokens[];
  message?: string;
}> {
  try {
    const response = await apiPost('where-logged');
    if (!response || !response.data || !response.data.tokens) {
      return {
        success: true,
        devices: []
      };
    }
    const devices = Object.values(response.data.tokens).map((token: any) => ({
      id: token.id || '',
      name: token.name || 'Dispositivo desconhecido',
      last_used_at: token.last_used_at || token.created_at || '',
      created_at: token.created_at || '',
      isCurrent: token.name === currentDeviceInfo
    }));
    return {
      success: true,
      devices
    };
  } catch (error) {
    console.error('Erro ao buscar dispositivos conectados:', error);
    if (error instanceof ApiError) {
      if (error.status === 401) {
        await clearAuthCookies();
      }
      return {
        success: false,
        message: error.message,
        devices: []
      };
    }
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Erro desconhecido',
      devices: []
    };
  }
}
