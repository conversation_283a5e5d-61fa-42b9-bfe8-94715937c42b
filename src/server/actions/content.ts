/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use server';

import type { Content } from '@/@types/content';
import type { ContentResponse } from '@/@types/responses';
import type { InitialServerData } from '@/@types/server-props';
import { IS_PRODUCTION } from '@/constants';
import { StringUtils } from '@/utils/StringUtils';
import { ValidationUtils } from '@/utils/ValidationUtils';
import { cookies } from 'next/headers';
import { cache } from 'react';
import { apiGet, apiPost } from '../services/api';
import { getServerAccountId, getServerToken, setAuthCookies } from '../services/cookiesService';
import { getServerIsImpersonate } from '../services/ImpersonationService';
import { fetchUserData, getUserAssets } from './user';
import { User } from '@/@types/user';

/**
 * Busca um conteúdo específico pelo ID da proposta
 */
export async function getServerContent({
  ProposalId,
  ContractId,
  type,
  AccountId,
  selectedIndex = 0,
  EmpreendimentoId,
  page = ''
}: {
  ProposalId: string | null;
  ContractId: string | null;
  type: string;
  AccountId: string;
  selectedIndex?: number;
  EmpreendimentoId: string;
  page?: string;
}): Promise<Content | null> {
  try {
    // Filtra valores nulos para evitar erro de tipo
    const queryParams: Record<string, string | number> = {
      type,
      AccountId,
      EmpreendimentoId,
      page
    };

    if (ContractId !== null) {
      queryParams.ContractId = ContractId;
    }

    if (ProposalId !== null) {
      queryParams.ProposalId = ProposalId;
    }

    if (selectedIndex !== undefined) {
      queryParams.selectedIndex = selectedIndex;
    }

    const response = await apiGet<ContentResponse>('contents', queryParams);

    if (response?.data?.content) {
      return response.data.content;
    }

    return null;
  } catch (error) {
    console.error('Erro ao buscar conteúdo:', error);
    return null;
  }
}

/**
 * Verifica o status do boleto de ato
 */
export async function checkStatusBoletoAto(
  codigoSienge: string,
  unidadeAtivoName: string
): Promise<string> {
  try {
    if (!codigoSienge || !unidadeAtivoName) {
      return 'boleto-nao-encontrado';
    }

    const response = await apiPost('checkBoletoAto', {
      CodigoSienge__c: codigoSienge,
      UnidadeAtivoName: unidadeAtivoName
    });
    return response.data.boletoAto;
  } catch (error) {
    console.error('Erro ao verificar boleto ato:', error);
    return 'boleto-nao-encontrado';
  }
}

/**
 * Define o conteúdo selecionado no cookie
 */
export async function setContentAction(value: number): Promise<void> {
  const cookieStore = await cookies();

  await cookieStore.set('contentSelected', value.toString(), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 60 * 24 * 7, // 7 dias
    path: '/'
  });
}

const getInitialContentSelected = async (): Promise<number> => {
  const cookieStore = await cookies();
  const contentSelectedCookie = cookieStore.get('contentSelected')?.value;
  return contentSelectedCookie ? parseInt(contentSelectedCookie, 10) : 0;
};

/**
 * Atualiza o conteúdo selecionado e busca os dados do servidor.
 * Centraliza a lógica para evitar múltiplas chamadas de API.
 */
export async function updateContentAndGetServerSide(
  user: User | null,
  currentContent: Content,
  index: number
): Promise<Content | null> {
  const { ProposalId, ContractId, type, AccountId, Empreendimento } = currentContent;

  try {
    await setContentAction(index);

    const newContent = await getServerContent({
      ProposalId: ProposalId || null,
      ContractId: ContractId || null,
      type,
      AccountId,
      EmpreendimentoId: Empreendimento?.EmpreendimentoId,
      selectedIndex: index
    });

    if (!newContent) {
      return null;
    }

    if (
      user?.CodigoSienge__c &&
      newContent.type === 'morador' &&
      newContent.assets.statusBoletoAto !== 'Recebimento'
    ) {
      const statusBoletoAto = await checkStatusBoletoAto(
        user.CodigoSienge__c,
        newContent.UnidadeAtivoName
      );
      newContent.statusBoletoAto = statusBoletoAto;
    }

    return newContent;
  } catch (error) {
    console.error('Erro ao atualizar e buscar conteúdo do servidor:', error);
    return null;
  }
}

export async function fetchCases(type: string) {
  const cookieStore = await cookies();
  const accountIdCookie = cookieStore.get('AccountId')?.value;
  const response = await apiGet(`case/showCases/${accountIdCookie}/${type}`, {
    cache: 'force-cache',
    revalidateTime: 600
  });
  return response.data?.cases || [];
}
export async function fetchCreateNewPassword(
  password: string,
  password_confirmation: string,
  deviceinfo: string
) {
  const cookiesNewPassword = await cookies();
  const token = cookiesNewPassword.get('user.temp-token')?.value;
  const email = cookiesNewPassword.get('user.temp-email')?.value;
  const userLogin =
    cookiesNewPassword.get('user.temp-cpf')?.value ||
    cookiesNewPassword.get('user.temp-cnpj')?.value;

  const response = await apiPost('create-new-password', {
    token: token,
    email: email,
    userLogin: userLogin,
    password: password,
    password_confirmation: password_confirmation,
    deviceinfo: deviceinfo
  });
  if (response && response.data) {
    const { user, token } = response.data;

    if (user && token) {
      const { CPF__c, CNPJ__c } = user;
      const userLogin = CPF__c ?? CNPJ__c;

      const tokenData = {
        type: 'SET_TOKEN',
        device_info: deviceinfo,
        access_token: token.access_token,
        expires_at: token.expires_at,
        AccountId: user.AccountId
      };

      // Armazenar cookies de autenticação
      await setAuthCookies(
        token.access_token,
        token.expires_at,
        user.AccountId,
        deviceinfo,
        {
          cpf: user.CPF__c || '',
          cnpj: user.CNPJ__c || '',
          email: user.Email__c || '',
          userLogin: StringUtils.cleanData(userLogin)
        },
        false
      );

      return { success: true, redirectTo: `/${user.AccountId}/home` };
    } else {
      return { success: false };
    }
  }
}

const buildDefaultResponse = async (): Promise<InitialServerData> => {
  const initialContentSelected = await getInitialContentSelected();
  return {
    initialUser: null,
    initialContentData: null,
    initialContentsData: null,
    initialContentSelected,
    initialShowSidebar: null,
    environment: IS_PRODUCTION ? 'production' : 'development',
    hasImpersonateToken: false
  };
};

export const fetchInitialServerData = cache(async (): Promise<InitialServerData> => {
  const [accountId, token] = await Promise.all([getServerAccountId(), getServerToken()]);

  if (!accountId || !token) {
    return buildDefaultResponse();
  }

  try {
    const [initialContentSelected, initialUser, initialContentsData, hasImpersonateToken] =
      await Promise.all([
        getInitialContentSelected(),
        fetchUserData(accountId),
        getUserAssets(1, 20),
        getServerIsImpersonate()
      ]);

    if (
      !initialUser ||
      !initialContentsData ||
      !initialContentsData.contents ||
      initialContentsData.contents.length === 0
    ) {
      return {
        ...(await buildDefaultResponse()),
        initialUser,
        initialContentsData: initialContentsData?.contents || [],
        initialContentSelected
      };
    }

    const safeIndex =
      initialContentSelected < initialContentsData.contents.length ? initialContentSelected : 0;

    const selectedContent = initialContentsData.contents[safeIndex];

    if (!selectedContent) {
      return {
        ...(await buildDefaultResponse()),
        initialUser,
        initialContentsData: initialContentsData.contents,
        initialContentSelected: 0
      };
    }

    let statusBoletoAto = 'boleto-nao-encontrado';

    if (
      initialUser.CodigoSienge__c &&
      selectedContent.UnidadeAtivoName &&
      selectedContent.statusBoletoAto !== 'Recebimento'
    ) {
      statusBoletoAto = await checkStatusBoletoAto(
        initialUser.CodigoSienge__c as string,
        selectedContent.UnidadeAtivoName as string
      );
      selectedContent.statusBoletoAto = statusBoletoAto;
    }

    const initialContentData = await getServerContent({
      ProposalId: selectedContent.ProposalId || null,
      ContractId: selectedContent.ContractId || null,
      type: selectedContent.type,
      AccountId: initialUser.AccountId,
      EmpreendimentoId: selectedContent.Empreendimento?.EmpreendimentoId,
      selectedIndex: safeIndex
    });

    if (!initialContentData) {
      return {
        ...(await buildDefaultResponse()),
        initialUser,
        initialContentsData: initialContentsData.contents,
        initialContentSelected: safeIndex
      };
    }

    let initialShowSidebar = null;

    try {
      let boletoAtoStatus = 'boleto-nao-encontrado';
      if (
        initialUser.CodigoSienge__c &&
        initialContentData.UnidadeAtivoName &&
        initialContentData.assets.statusBoletoAto !== 'Recebimento'
      ) {
        boletoAtoStatus = await checkStatusBoletoAto(
          initialUser.CodigoSienge__c,
          initialContentData.UnidadeAtivoName
        );
      } else {
        boletoAtoStatus = initialContentData.assets.statusBoletoAto;
      }

      initialContentData.statusBoletoAto = boletoAtoStatus;

      initialShowSidebar = ValidationUtils.validaShowSideBar(
        initialContentData.type,
        boletoAtoStatus
      );
    } catch (error) {
      console.error('Erro ao processar status do boleto ato:', error);
      initialShowSidebar = true;
    }

    return {
      initialUser,
      initialContentData,
      initialContentsData: initialContentsData.contents,
      initialContentSelected: safeIndex,
      initialShowSidebar,
      environment: IS_PRODUCTION ? 'production' : 'development',
      hasImpersonateToken
    };
  } catch (error) {
    if (error instanceof Error) {
      console.error('Detalhes do erro:', {
        message: error.message,
        stack: error.stack
      });
    }
    return buildDefaultResponse();
  }
});
