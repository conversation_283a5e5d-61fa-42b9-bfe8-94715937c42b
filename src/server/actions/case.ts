'use server';

import { CaseData } from '@/@types/cases';
import { apiPost } from '../services/api';

/**
 * Envia um caso para o servidor
 */
export const sendCaseToServer = async (caseData: CaseData): Promise<any> => {
  const response = await apiPost(
    '/api/case/insertCaseNegociaAntecipa',
    JSON.stringify(caseData)
  );

  // A apiPost agora retorna { error: '...' } em caso de falha.
  // Nós repassamos esse erro para o cliente.
  if (response.error) {
    console.error('Erro ao enviar o caso:', response.error);
    return { error: response.error };
  }

  // Se a resposta não tiver a propriedade 'ok', mas também não for um erro estruturado,
  // tratamos como uma falha genérica para garantir consistência.
  if (!response.ok) {
    const errorMessage = 'Falha ao enviar o caso';
    console.error(errorMessage);
    return { error: errorMessage };
  }

  // Se tudo deu certo, a apiPost já retorna os dados no formato correto.
  return response;
};
