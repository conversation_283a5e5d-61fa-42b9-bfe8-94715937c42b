import { Content } from '@/@types/content';
import { NegotiateType, ReturnType, SelectedInstallment } from '@/@types/installments.d';
import { NegotiationConditionsType } from '@/@types/negotiation';
import { sendCaseToServer } from '@/server/actions/case';
import { executeNegotiation, prepareNegotiationData } from '@/server/actions/negotiation';

/**
 * Serviço para negociações
 */
export class NegotiationService {
  /**
   * Executa uma simulação de negociação
   */
  static async simulateNegotiation(
    paid: boolean,
    user: any,
    content: Content,
    selectedInstallments: { selectedInstallment: SelectedInstallment[] } | null,
    billReceivableId: number | null
  ): Promise<{
    result: ReturnType | NegotiationConditionsType | null;
    processedResponse: any;
    error?: boolean;
  }> {
    const action = paid
      ? 'simulacao'
      : billReceivableId
        ? 'NegociacaoParcialSimulacao'
        : 'simulacao';
    const simulationNegotiateData: NegotiateType = {
      type: paid ? 'adimplente' : 'inadimplente',
      action: action,
      assetId: content.AssetId,
      billReceivableId: billReceivableId as number
    };

    if (paid) {
      simulationNegotiateData.codigo_sienge_user = user.CodigoSienge__c;
      simulationNegotiateData.installments = selectedInstallments?.selectedInstallment;
    } else {
      simulationNegotiateData.installmentsIds =
        selectedInstallments?.selectedInstallment.map((item) => Number(item.id)) || [];
    }

    return executeNegotiation(simulationNegotiateData);
  }

  /**
   * Confirma uma ação de negociação
   */
  static async confirmNegotiation(
    paid: boolean,
    user: any,
    content: Content,
    selectedInstallments: { selectedInstallment: SelectedInstallment[] } | null,
    negotiateData: NegotiateType,
    billReceivableId: number | null
  ): Promise<{
    result: ReturnType | NegotiationConditionsType | null;
    processedResponse: any;
    error?: boolean;
  }> {
    const finalNegotiateData = await prepareNegotiationData(
      paid,
      user,
      content,
      selectedInstallments,
      negotiateData,
      billReceivableId
    );

    return executeNegotiation(finalNegotiateData);
  }

  /**
   * Envia um caso para mais informações
   */
  static async sendCaseForMoreInfo(
    user: any,
    content: Content,
    cancelMotivation: string,
    descriptionToCancel: string,
    paid: boolean
  ): Promise<any> {
    return sendCaseToServer({
      user,
      content,
      tipoAtendimento: 'Negociação',
      classificacao: 'Gestão da Carteira',
      assunto: paid ? 'Portal Antecipação – Informações' : 'Portal Renegociação - Informações',
      MotivoCancelamento: cancelMotivation,
      description: descriptionToCancel,
      type: 'case/insertCaseNegociaAntecipa',
      textLoadingCase: ''
    });
  }
}
