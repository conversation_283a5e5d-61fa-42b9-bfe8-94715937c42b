import { InstallmentItem, Installments } from '@/@types/installments.d';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';

/**
 * Serviço para manipulação de parcelas
 */
export class InstallmentService {
  /**
   * Filtra parcelas indesejadas com base nos critérios definidos
   */
  static filterInstallments(installments: InstallmentItem[]): InstallmentItem[] {
    const excludedPaymentTerms = new Set(['DN', 'FN', 'FI']);
    const excludedReceiptTypes = new Set([
      'Cancelamento',
      'Adiantamento',
      'Reparcelamento',
      'Recebimento',
      'Substituição',
      'Outros'
    ]);

    return installments.filter((inst) => {
      if (excludedPaymentTerms.has(inst.paymentTerms.id)) return false;
      const receipt = inst.receipts[0];
      if (!receipt || receipt.days < 1 || excludedReceiptTypes.has(receipt.type)) return false;
      return true;
    });
  }

  /**
   * "Achata" a estrutura de parcelas e as ordena por data
   */
  static flattenAndSortInstallments(installments: Installments): InstallmentItem[] {
    return installments.data
      .flatMap((subArray, parentIndex) =>
        subArray.installments.map((inst: InstallmentItem) => ({
          ...inst,
          parentIndex,
          billReceivableId: subArray.billReceivableId
        }))
      )
      .sort(
        (a, b) =>
          new Date((a.isoDueDate ?? a.dueDate) as string).getTime() -
          new Date((b.isoDueDate ?? b.dueDate) as string).getTime()
      );
  }

  /**
   * Classifica as parcelas como adimplente ou inadimplente
   */
  static classifyInstallments(
    installments: Installments,
    allInstallmentData: InstallmentItem[]
  ): {
    isPaid: boolean;
    filteredInstallments: InstallmentItem[];
    hasReparcelamentoToday: boolean;
  } {
    const filteredData = this.filterInstallments(allInstallmentData);
    const today = new Date().toISOString().split('T')[0];

    // Verifica se há parcelas vencidas (inadimplente)
    if (filteredData.length > 0) {
      const parcialInstallments = allInstallmentData.filter(
        (inst) =>
          inst.installmentSituation === '0' &&
          DateUtils.compareDates(today, (inst.isoDueDate ?? inst.dueDate) as string)
      );

      return {
        isPaid: false,
        filteredInstallments: parcialInstallments,
        hasReparcelamentoToday: false
      };
    }

    // Caso adimplente
    const availableInstallments = allInstallmentData.filter((inst) =>
      FinanceUtils.checkIfInstallmentsCanBeAntecipate(inst)
    );

    // Verifica se há reparcelamento hoje
    const hasReparcelamentoToday = installments.data.some((sub) =>
      sub.installments.some(
        (inst: InstallmentItem) =>
          (inst.receipts[0].type === 'Reparcelamento' && inst.receipts[0].date === today) ||
          (inst.paymentTerms.descrition === 'Adiantamento de Reserva' &&
            inst.receipts[0].date === today)
      )
    );

    return {
      isPaid: true,
      filteredInstallments: availableInstallments,
      hasReparcelamentoToday
    };
  }
}
