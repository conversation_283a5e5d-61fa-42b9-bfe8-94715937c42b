'use server';

import { cookies } from 'next/headers';
import { apiGet, apiPost } from './api';

export async function getServerIsImpersonate(): Promise<boolean> {
  const cookieStore = await cookies();
  return cookieStore.get('is_impersonating')?.value === 'true' || false;
}

/**
 * Valida token de impersonificação
 */
export async function validateImpersonationToken(token: string): Promise<{
  success: boolean;
  temp_token: string;
  account_id: string;
  is_impersonating: boolean;
}> {
  return apiGet(`impersonate/validate/${token}`);
}

/**
 * Finaliza uma sessão de impersonificação
 */
export async function stopImpersonation(): Promise<{
  success: boolean;
  message: string;
}> {
  const cookieStore = await cookies();
  const impersonateToken = cookieStore.get('token')?.value;
  cookieStore.delete('is_impersonating');
  const device_info = cookieStore.get('device_info')?.value;

  return apiPost('impersonate/stop', {
    impersonation_token: impersonateToken,
    device_info: device_info
  });
}
