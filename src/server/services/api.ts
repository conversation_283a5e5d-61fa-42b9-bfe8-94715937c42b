/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use server';

import type { ApiRequestOptions, ApiServiceOptions } from '@/@types/apiTypes';
import { buildFormData } from '@/hooks/apiUtils';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { buildApiUrl, getApiHeaders } from '../utils';
import { clearAuthCookies } from './cookiesService';
import { ApiError } from '../utils/ApiError';

/**
 * Uma função base para realizar chamadas de API, aproveitando o cache nativo do Next.js.
 * @param options - As opções da requisição.
 * @returns Os dados da resposta da API.
 */
async function apiFetch<T = any>(options: ApiRequestOptions): Promise<T> {
  const {
    method,
    path,
    body,
    queryParams,
    file,
    requiresAuth = true,
    revalidatePaths = [],
    shouldRedirectOnAuthError = false,
    revalidate = 60
  } = options;
  const url = await buildApiUrl(path, queryParams);
  let headers = await getApiHeaders(requiresAuth);
  let requestBody: BodyInit | null = body ? JSON.stringify(body) : null;

  if (file || body?.file instanceof File) {
    headers = Object.fromEntries(
      Object.entries(headers).filter(([key]) => key.toLowerCase() !== 'content-type')
    );
    requestBody = buildFormData(body, body?.file);
  }
  console.log('url', url);
  try {
    const response = await fetch(url, {
      method,
      headers,
      body: requestBody,
      cache: options.cache,
      next: { tags: [path], revalidate: revalidate }
    });
    console.log('response', response);
    if (!response.ok) {
      if (response.status === 401 && shouldRedirectOnAuthError) {
        await clearAuthCookies();
        return redirect('/login');
      }
      const errorData =
        (await response.json().catch(() => ({ message: `Erro ${response.statusText}` }))) || {};
      const errorMessage =
        errorData?.errors?.message ||
        errorData?.errors?.default ||
        errorData?.message ||
        'Erro desconhecido';
      // throw new Error(errorMessage);
      return { error: errorMessage, status: response.status } as T;
    }

    if (response.status === 204 || response.headers.get('content-length') === '0') {
      return { success: true } as T;
    }

    const data = (await response.json()) as T;

    if (revalidatePaths.length > 0) {
      revalidatePaths.forEach((p) => revalidatePath(p));
    }

    return { ...data, success: true };
  } catch (error) {
    console.error('Erro na requisição ao servidor:', error);
    if (error !== 'Caso inexistente') throw error;
    return null as T;
  }
}

/**
 * Funções auxiliares para realizar chamadas de API.
 */

export async function apiPost<T = any>(
  path: string,
  body?: any,
  queryParams?: Record<string, string | number>,
  options?: ApiServiceOptions
): Promise<T> {
  return apiFetch<T>({ method: 'POST', path, body, queryParams, ...options });
}

export async function apiGet<T = any>(
  path: string,
  queryParams?: Record<string, string | number>,
  options?: ApiServiceOptions
): Promise<T> {
  return apiFetch<T>({ method: 'GET', path, queryParams, ...options });
}

export async function apiPut<T = any>(
  path: string,
  body?: any,
  queryParams?: Record<string, string | number>,
  options?: ApiServiceOptions
): Promise<T> {
  return apiFetch<T>({ method: 'PUT', path, body, queryParams, ...options });
}

export async function apiDelete<T = any>(
  path: string,
  queryParams?: Record<string, string | number>,
  options?: ApiServiceOptions
): Promise<T> {
  return apiFetch<T>({ method: 'DELETE', path, queryParams, ...options });
}

export async function apiPatch<T = any>(
  path: string,
  body?: any,
  queryParams?: Record<string, string | number>,
  options?: ApiServiceOptions
): Promise<T> {
  return apiFetch<T>({ method: 'PATCH', path, body, queryParams, ...options });
}
