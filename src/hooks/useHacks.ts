/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useAppContext } from '@/contexts/AppContext';
import { usePathname, useRouter } from 'next/navigation';
import { MouseEvent, useEffect } from 'react';

export const useHacks = () => {
  const { user } = useAppContext();
  const threshold = 30;
  let touchStartX = 0;
  let touchCurrentX = 0;

  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    function handleTouchStart(event: TouchEvent) {
      touchStartX = event.touches[0].clientX;
      if (event.touches && event.touches.length > 1) {
        event.preventDefault();
      }
    }

    function handleTouchMove(event: TouchEvent) {
      touchCurrentX = event.touches[0].clientX;
    }

    function handleTouchEnd(event: TouchEvent) {
      if (touchStartX > 0 && touchStartX < threshold && touchCurrentX > touchStartX) {
        // window.history.back();
        goBackAction();
      }
      touchStartX = 0;
      touchCurrentX = 0;
      if (event.touches && event.touches.length > 1) {
        event.preventDefault();
      }
    }

    document.addEventListener('touchstart', handleTouchStart, {
      passive: false
    });
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, []);

  const goBack = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    goBackAction();
    return true;
  };
  const goBackAction = () => {
    if (pathname === null || pathname === undefined) return null;
    const pathParts = pathname.split('/').filter(Boolean);
    if (pathParts.length > 0 && pathParts[0] === 'sindico') {
      router.push(`/${user?.AccountId}/home`);
      return null;
    }
    if (
      pathParts.length > 1 &&
      (pathParts[2] === 'parcela-detalhada' || pathParts[2] === '2via-de-boleto')
    ) {
      router.back();
      return null;
    }
    if (pathParts.length === 0) return null;
    pathParts.pop();
    pathParts.shift();
    const endpoint = pathParts.length === 0 ? 'home' : pathParts.join('/');
    const newPath = `/${user?.AccountId}/${endpoint}`;

    router.push(newPath);
    return newPath;
  };

  return {
    goBack
    // pathname
    // pathParts
  };
};
