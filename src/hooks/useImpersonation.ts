'use client';

import { ADMIN_URL } from '@/constants';
import { clearAuthCookies } from '@/server/services/cookiesService';
import { getServerIsImpersonate, stopImpersonation } from '@/server/services/ImpersonationService';
import { useEffect, useState } from 'react';

export function useImpersonation() {
  const [isImpersonating, setIsImpersonating] = useState<boolean>(false);

  useEffect(() => {
    const checkImpersonation = async () => {
      const hasImpersonateToken = await getServerIsImpersonate();
      setIsImpersonating(hasImpersonateToken);
    };
    checkImpersonation();
  }, []);

  useEffect(() => {
    if (isImpersonating) {
      const handleBeforeUnload = async (e: BeforeUnloadEvent) => {
        try {
          await stopImpersonationForced();
        } catch (error) {
          console.error('Erro ao fazer logout:', error);
        }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, [isImpersonating]);

  const stopImpersonationForced = async (): Promise<void> => {
    console.log('stopImpersonationForced');
    try {
      const confirmExit = window.confirm(
        'Ao continuar, você será deslogado da conta do usuário.\n\nClique em "ok" para confirmar ou "Cancel" para cancelar.'
      );

      if (!confirmExit) {
        return;
      }

      await stopImpersonation();

      console.log('after stopImpersonation');

      document.cookie = 'impersonate_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      document.cookie = 'AccountId=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
      await clearAuthCookies(false);
      window.location.href = `${ADMIN_URL}/admin/clientes`;
    } catch (error) {
      console.error('Erro ao finalizar impersonificação:', error);
    }
  };

  return {
    isImpersonating,
    setIsImpersonating,
    stopImpersonationForced
  };
}
