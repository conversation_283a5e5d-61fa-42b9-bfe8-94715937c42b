/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { Content } from '@/@types/content';
import permissionsConfig from '@/config/permissions.config';

/**
 * Hook para gerenciar permissões baseadas no estado do conteúdo.
 * @param content - O conteúdo atual ou null.
 * @returns Função para verificar permissões de componentes.
 */
export const usePermissions = (content: Content | null) => {
  /**
   * Calcula o StatusMacro final com base nos campos do Empreendimento e do Contract.
   * @returns O StatusMacro final para consulta no permissionsConfig.
   */
  const getFinalStatusMacro = (): string => {
    if (!content || !content.Empreendimento) {
      return 'Lançamento'; // Default fallback
    }

    const { StatusMacro__c, DataRealizadaHabitese__c } = content.Empreendimento;
    // const { SituacaoEntrega__c } = content?.Contract as Contract;
    const situacaoEntrega = content?.Contract?.SituacaoEntrega__c ?? content?.SituacaoEntrega__c;

    // Determina o StatusMacro base
    let statusMacro = StatusMacro__c ?? 'Lançamento';

    // Ajusta o StatusMacro com base em EstagioComercializacao__c
    if (StatusMacro__c === 'Não iniciada') {
      statusMacro = 'Não iniciada';
    } else if (StatusMacro__c === 'Em obra') {
      statusMacro = 'Em obra';
    } else if (StatusMacro__c === 'Em entrega') {
      statusMacro = 'Em entrega';
    } else if (StatusMacro__c === 'Obra concluída') {
      statusMacro = 'Obra concluída';
    }

    // Verifica se o Habite-se foi emitido
    if (DataRealizadaHabitese__c) {
      const habiteseDate = new Date(DataRealizadaHabitese__c);
      const currentDate = new Date();
      if (habiteseDate <= currentDate) {
        statusMacro = 'Habite-se emitido';
      } else {
        statusMacro = 'Habite-se';
      }
    }

    // Ajusta o status final com base em SituacaoEntrega__c
    if (statusMacro === 'Habite-se emitido' || statusMacro === 'Obra concluída') {
      if (situacaoEntrega === 'Chaves entregues') {
        statusMacro = 'Entregue com chaves';
      } else if (situacaoEntrega === 'Chaves não entregues') {
        statusMacro = 'Entregue sem chaves';
      }
    }

    return statusMacro;
  };

  /**
   * Verifica se um componente tem permissão para ser exibido.
   * @param component - Nome do componente a verificar (ex.: 'planta', 'assembleiageral').
   * @returns Booleano indicando se o componente tem permissão.
   */
  const getPermission = (component: string): boolean => {
    const statusCarteira = content?.Contract?.StatusCarteira__c ?? content?.StatusCarteira__c;
    if (!content || !statusCarteira) {
      return false;
    }

    const finalStatusMacro = getFinalStatusMacro();

    // Obtém as permissões configuradas para o componente
    const componentPermissions = permissionsConfig[component];

    if (!componentPermissions) {
      return false; // Componente não configurado
    }

    // Obtém as permissões para o StatusCarteira
    const statusPermissions = componentPermissions[statusCarteira];

    if (!statusPermissions) {
      return false; // StatusCarteira não configurado
    }

    // Verifica a permissão para o StatusMacro final
    const permission = statusPermissions[finalStatusMacro];

    return permission !== undefined ? permission : false;
  };

  return { getPermission };
};
