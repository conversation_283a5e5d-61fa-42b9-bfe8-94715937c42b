/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { ApiOptions, ErrorNegotiateType } from '@/@types/apiTypes';
import { buildFormData, buildUrl, GetHeaders } from './apiUtils';
import { useData } from './useData';

export function useApi() {
  const { getData, destroyData } = useData();

  const getFetch = async <T>(options: ApiOptions): Promise<T | null> => {
    const {
      method,
      route,
      params = {},
      body = null,
      file = null,
      isNegotiate = false,
      cache = 'force-cache'
    } = options;
    const url = buildUrl(route, params);
    const baseHeaders = await GetHeaders(getData);
    const isFormData = body instanceof FormData;
    const headers = {
      ...baseHeaders,
      ...(!isFormData && (isNegotiate || route.startsWith('services'))
        ? { 'Content-Type': 'application/json' }
        : {})
    };
    try {
      const response = await fetch(url, {
        method,
        headers,
        body: isFormData
          ? body
          : isNegotiate || route.startsWith('services')
            ? JSON.stringify(body)
            : buildFormData(body, file),
        next: { revalidate: 60 },
        cache
      });

      if (!response.ok) {
        return await handleErrorResponse(response);
      }
      if (response.status === 500) {
        return await handleErrorResponse(response);
      }

      const data = await response.json();

      if (isNegotiate) {
        handleNegotiateResponse(data as unknown as { code: number });
      }

      return data as T;
    } catch (error) {
      console.error(error);
      console.log(error);
      if (route === 'where-logged' && body === null) {
        return null;
      }
      throw error;
    }
  };

  const handleErrorResponse = async (response: Response) => {
    if (response.status === 401 && !isLoginRelatedPage()) {
      destroyData();
      window.location.replace('/login');
      return null;
    }

    const responseData = await response.json();
    const errorMessage =
      responseData.errors?.message || responseData.errors?.default || 'Erro desconhecido';
    throw new Error(errorMessage);
  };

  const handleNegotiateResponse = (data: { code: number }) => {
    if (data.code === 422) {
      const errorReturn: ErrorNegotiateType = {
        code: data.code,
        message: 'Tente novamente em breve.'
      };
      throw new Error(errorReturn.message);
    }
  };

  const isLoginRelatedPage = () => {
    const loginRelatedPages = [
      '/login',
      '/esqueci-minha-senha',
      '/nova-senha',
      '/senha-temporaria',
      '/sou-vizinho',
      '/verifica-codigo'
    ];
    return loginRelatedPages.includes(new URL(window.location.href).pathname);
  };

  return { getFetch };
}
