import { Installments, UseFilterInstallmentsReturn } from '@/@types/installments.d';
import { InstallmentService } from '@/server/services/InstallmentService';
import { useCallback } from 'react';

/**
 * Hook para processar e filtrar parcelas
 */
export const useFilterInstallments = (): UseFilterInstallmentsReturn => {
  /**
   * Processa as parcelas e determina se é adimplente ou inadimplente
   */
  const processInstallments = useCallback((installments: Installments) => {
    if (installments.data.length === 0) {
      return {
        isPaid: true,
        filteredInstallments: [],
        hasReparcelamentoToday: false,
        allInstallmentData: []
      };
    }

    const allInstallmentData = InstallmentService.flattenAndSortInstallments(installments);
    const { isPaid, filteredInstallments, hasReparcelamentoToday } =
      InstallmentService.classifyInstallments(installments, allInstallmentData);

    return {
      isPaid,
      filteredInstallments,
      hasReparcelamentoToday,
      allInstallmentData
    };
  }, []);

  return { processInstallments };
};
