/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { useAppContext } from '@/contexts/AppContext';
/**
 * Hook para gerenciar atualizações de dados e autenticação entre plataformas
 */
import { refreshUserData } from '@/server/actions/auth'; // Importação correta
import { useLogout } from '@/templates/Auth/hooks/useLogout';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

// Definição global para o método receiveUserData
declare global {
  interface Window {
    receiveUserData?: (data: any) => void;
  }
}

export function useRefresh() {
  const router = useRouter();
  const { user, setUser } = useAppContext();
  const { logout } = useLogout();
  const pathname = usePathname();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoginAdmin, setIsLoginAdmin] = useState(false);

  /**
   * Processa os dados recebidos de plataformas externas (React Native ou Admin)
   */
  const processExternalData = useCallback(
    async (dataObject: any, viaAdmin: boolean) => {
      if (
        dataObject.type === 'SET_TOKEN' &&
        dataObject.device_info &&
        dataObject.access_token &&
        dataObject.expires_at &&
        dataObject.account_id
      ) {
        try {
          setIsRefreshing(true);

          // Se vier do admin, fazer logout do dispositivo atual primeiro
          if (viaAdmin) {
            await logout({ deviceinfoname: dataObject.device_info });
            setIsLoginAdmin(true);
          }

          // Salvar token utilizando server action
          await refreshUserData({
            token: {
              access_token: dataObject.access_token,
              expires_at: dataObject.expires_at
            },
            accountId: dataObject.account_id,
            deviceInfo: dataObject.device_info
          });

          // Iniciar o processo de atualização de dados
          setTimeout(() => {
            refreshDataStart();
          }, 100);
        } catch (error) {
          console.error('Erro ao processar dados externos:', error);
        } finally {
          setIsRefreshing(false);
        }
      }
    },
    [logout]
  );

  // Configurar listeners para comunicação cross-platform
  useEffect(() => {
    // Listener para mensagens do Admin
    const handleMessage = (event: MessageEvent) => {
      processExternalData(event.data, true);
    };

    window.addEventListener('message', handleMessage);

    // Método para receber dados do App React Native
    window.receiveUserData = (incomingData) => {
      try {
        const parsedData =
          typeof incomingData === 'string' ? JSON.parse(incomingData) : incomingData;

        processExternalData(parsedData, false);
      } catch (error) {
        console.error('Erro ao parsear dados do React Native:', error);
      }
    };

    // Limpeza dos listeners ao desmontar
    return () => {
      window.removeEventListener('message', handleMessage);
      delete window.receiveUserData;
    };
  }, [processExternalData]);

  /**
   * Inicia o processo de atualização de dados do usuário
   */
  const refreshDataStart = useCallback(async () => {
    try {
      setIsRefreshing(true);

      // Verificar se é necessário atualizar os dados (sem usuário mas com token)
      if (!user) {
        // Usar server action para obter dados atualizados
        const result = await refreshUserData();
        if (result.success && result.userData) {
          // Atualizar estado do usuário
          setUser(result.userData);

          // Atualizar conteúdo selecionado
          // if (result.contentSelected !== undefined) {
          //   setRefreshDataToContent(result.contentSelected);
          // }

          // Se veio do admin, redirecionar para home
          if (isLoginAdmin) {
            setIsLoginAdmin(false);

            if (result.userData?.AccountId) {
              router.push(`/${result.userData.AccountId}/home`);
            }
          }
        } else if (result.expired) {
          // Token expirado, solicitar novas credenciais
          BrowserUtils.ActionReactNative({ type: 'credentialsRequired' });
        }
      }

      return true;
    } catch (error) {
      console.error('Erro ao atualizar dados:', error);

      // Em caso de erro crítico, redirecionar para login

      if (
        pathname !== '/esqueci-minha-senha' &&
        pathname !== '/nova-senha' &&
        pathname !== '/verifica-codigo' &&
        pathname !== '/sou-vizinho' &&
        pathname !== '/termos-de-uso' &&
        pathname !== '/politica-de-privacidade' &&
        pathname !== '/primeiro-acesso' &&
        pathname !== '/login'
      ) {
        router.push('/login');
      }
      return false;
    } finally {
      setIsRefreshing(false);
    }
    // }, [user, setUser, setRefreshDataToContent, isLoginAdmin, router]);
  }, []);

  return {
    refreshDataStart,
    isRefreshing
  };
}
