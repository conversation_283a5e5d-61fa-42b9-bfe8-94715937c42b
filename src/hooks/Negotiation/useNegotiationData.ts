import { NegotiateType, SelectedInstallment } from '@/@types/installments.d';
import { NegotiationDataState } from '@/@types/negotiation';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';
import { useCallback, useState } from 'react';

export interface UseNegotiationDataReturn extends NegotiationDataState {
  setNegotiateData: (data: NegotiateType | null) => void;
  setNegotiatedConditions: (data: any) => void;
  setPaid: (paid: boolean) => void;
  setFilteredInstallments: (installments: any) => void;
  setCancelMotivation: (motivation: string) => void;
  setDescriptionToCancel: (description: string) => void;
  setDataAntecipacaoDesconto__c: (date: string) => void;
  selectInstallments: (
    // content: any,
    // negotiateData: NegotiateType | null,
    newInstallment: SelectedInstallment
  ) => void;
}

/**
 * Hook para gerenciar dados de negociação
 */
export const useNegotiationData = (): UseNegotiationDataReturn => {
  const [paid, setPaid] = useState(false);
  const [negotiateData, setNegotiateData] = useState<NegotiateType | null>(null);
  const [filteredInstallments, setFilteredInstallments] = useState(null);
  const [selectedInstallments, setSelectedInstallments] = useState<{
    selectedInstallment: SelectedInstallment[];
  } | null>(null);
  const [billReceivableId, setBillReceivableId] = useState<number | null>(null);
  const [negotiatedConditions, setNegotiatedConditions] = useState(null);
  const [cancelMotivation, setCancelMotivation] = useState('Maiores Informações');
  const [descriptionToCancel, setDescriptionToCancel] = useState('');
  const [DataAntecipacaoDesconto__c, setDataAntecipacaoDesconto__c] = useState('');

  /**
   * Seleciona parcelas para negociação
   */
  const selectInstallments = useCallback(
    (
      // content: any,
      // negotiateData: NegotiateType | null,
      newInstallment: SelectedInstallment
    ): void => {
      const hasEarlierDueDate = DateUtils.compareDates(
        DataAntecipacaoDesconto__c as string,
        (newInstallment.isoDueDate ?? newInstallment.dueDate) as string
      );

      const hasEarlierDueDateSelect = selectedInstallments
        ? selectedInstallments.selectedInstallment.some((installmentSelect) => {
          return DateUtils.compareDates(
              DataAntecipacaoDesconto__c as string,
              (installmentSelect.isoDueDate ?? installmentSelect.dueDate) as string
          );
        })
        : false;

      const current =
        hasEarlierDueDate || hasEarlierDueDateSelect
          ? { selectedInstallment: [] }
          : selectedInstallments || { selectedInstallment: [] };

      setBillReceivableId(newInstallment.billReceivableId);

      const index = current.selectedInstallment.findIndex(
        (inst) =>
          inst.id === newInstallment.id && inst.billReceivableId === newInstallment.billReceivableId
      );

      const updated =
        index > -1
          ? {
            ...current,
            selectedInstallment: current.selectedInstallment.filter(
              (_, idx) => idx !== index && billReceivableId === newInstallment.billReceivableId
            )
          }
          : { ...current, selectedInstallment: [...current.selectedInstallment, newInstallment] };

      let description = '';
      if (negotiateData) {
        updated.selectedInstallment.forEach((inst: SelectedInstallment) => {
          description += `Parcela: ${inst.id}: Valor ${FinanceUtils.MoneyFormat.format(Number(inst.value))}\n`;
        });
      }

      const typeText =
        negotiateData?.action === 'NegociacaoParcialDisponivel'
          ? 'Negociação Parcial Disponível'
          : 'Simulação Antecipação';

      const titleText = paid
        ? 'Adimplente. Parcelas selecionadas.'
        : 'Inadimplente. Parcelas selecionadas.';

      const descResult = `Título: ${titleText}\n${typeText}\n${description}`;

      setSelectedInstallments(updated);
      setDescriptionToCancel(descResult);
    },
    [paid, selectedInstallments, billReceivableId]
  );

  return {
    paid,
    negotiateData,
    filteredInstallments,
    selectedInstallments,
    billReceivableId,
    negotiatedConditions,
    cancelMotivation,
    descriptionToCancel,
    DataAntecipacaoDesconto__c,
    setNegotiateData,
    setNegotiatedConditions,
    setPaid,
    setFilteredInstallments,
    setCancelMotivation,
    setDescriptionToCancel,
    setDataAntecipacaoDesconto__c,
    selectInstallments
  };
};
