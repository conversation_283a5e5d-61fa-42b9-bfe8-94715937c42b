import { NegotiationUIState } from '@/@types/negotiation';
import { useLoading } from '@/hooks/useLoading';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { useCallback, useState } from 'react';

export interface UseNegotiationUIReturn extends NegotiationUIState {
  showArea: (area: string) => void;
  setReturnTitle: (title: string) => void;
  setReturnText: (text: string) => void;
  setCaseNumber: (caseNumber: string) => void;
  setShowCancelamento: (show: boolean) => void;
}

/**
 * Hook para gerenciar estado da UI de negociação
 */
export const useNegotiationUIReturn = (): UseNegotiationUIReturn => {
  const { setLoadingInfo } = useLoading();
  const [returnTitle, setReturnTitle] = useState('');
  const [returnText, setReturnText] = useState('');
  const [caseNumber, setCaseNumber] = useState('');
  const [showParcelas, setShowParcelas] = useState(false);
  const [showCondicoes, setShowCondicoes] = useState(false);
  const [showReturn, setShowReturn] = useState(false);
  const [showCancelamento, setShowCancelamento] = useState(false);

  /**
   * Controla qual área da UI será exibida
   */
  const showArea = useCallback(
    (area: string): void => {
      setLoadingInfo(false);
      BrowserUtils.handleBodyScroll(true);
      setShowParcelas(area === 'parcelas');
      setShowCondicoes(area === 'condicoes');
      setShowReturn(area === 'return');
      setShowCancelamento(area === 'cancelamento');
    },
    [setLoadingInfo]
  );

  return {
    returnTitle,
    returnText,
    caseNumber,
    showParcelas,
    showCondicoes,
    showReturn,
    showCancelamento,
    showArea,
    setReturnTitle,
    setReturnText,
    setCaseNumber,
    setShowCancelamento
  };
};
