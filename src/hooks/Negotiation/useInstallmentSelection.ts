import { InstallmentItem, SelectedInstallment } from '@/@types/installments.d';
import { useCallback, useState } from 'react';

interface UseInstallmentSelectionProps {
  paid: boolean;
  selectInstallments: (newInstallment: SelectedInstallment) => void;
}

/**
 * Hook para gerenciar a seleção de parcelas
 * Mantém estado local de seleção e fornece funções auxiliares
 */
export const useInstallmentSelection = ({
  paid,
  selectInstallments
}: UseInstallmentSelectionProps) => {
  // Estados locais
  const [selectedInstallments, setSelectedInstallments] = useState<{
    selectedInstallment: SelectedInstallment[];
  }>({ selectedInstallment: [] });
  const [billReceivableId, setBillReceivableId] = useState<number | null>(null);
  const [filteredInstallments, setFilteredInstallments] = useState<InstallmentItem[] | null>(null);
  const [descriptionToCancel, setDescriptionToCancel] = useState('');

  /**
   * Atualiza os dados de parcelas filtradas
   */
  const updateFilteredInstallments = useCallback((installments: InstallmentItem[] | null) => {
    setFilteredInstallments(installments);
  }, []);

  /**
   * Processa a seleção de uma parcela
   * Adapta a interface para manter compatibilidade com o hook principal
   */
  const handleSelectInstallment = useCallback(
    (params: any) => {
      // Verifica se já recebemos um objeto SelectedInstallment ou precisamos criar um //
      const newInstallment: SelectedInstallment = params.id
        ? {
            id: params.id,
            billReceivableId: params.billReceivableId,
            value: params.value,
            indexerId: params.indexerId,
            isoDueDate: params.isoDueDate ?? params.dueDate,
            dueDate: params.dueDate ?? params.isoDueDate,
            paymentTerms: {
              id: params.paymentTerms.id,
              description: params.paymentTerms.description,
              descrition: params.paymentTerms.descrition
            }
          }
        : params;

      // Atualiza o estado local primeiro
      const index = selectedInstallments.selectedInstallment.findIndex(
        (inst) =>
          inst.id === newInstallment.id && inst.billReceivableId === newInstallment.billReceivableId
      );

      if (index > -1) {
        // Remove a parcela se já estiver selecionada
        setSelectedInstallments({
          selectedInstallment: selectedInstallments.selectedInstallment.filter(
            (_, idx) => idx !== index
          )
        });
      } else {
        // Adiciona a parcela se não estiver selecionada
        setSelectedInstallments({
          selectedInstallment: [...selectedInstallments.selectedInstallment, newInstallment]
        });
      }

      // Atualiza o billReceivableId
      setBillReceivableId(newInstallment.billReceivableId);

      // Propaga a seleção para o hook principal
      selectInstallments(newInstallment);
    },
    [selectInstallments, selectedInstallments]
  );

  /**
   * Limpa a seleção de parcelas
   */
  const clearSelection = useCallback(() => {
    setSelectedInstallments({ selectedInstallment: [] });
    setBillReceivableId(null);
    setDescriptionToCancel('');
  }, []);

  return {
    // Estados
    selectedInstallments,
    filteredInstallments,
    billReceivableId,
    descriptionToCancel,

    // Ações
    handleSelectInstallment,
    updateFilteredInstallments,
    clearSelection,
    setDescriptionToCancel
  };
};
