import { Content } from '@/@types/content';
import { Installments, SelectedInstallment } from '@/@types/installments.d';
import { UseNegotiateYourInstallmentsReturn } from '@/@types/negotiation';
import { useAppContext } from '@/contexts/AppContext';
import { useInstallments } from '@/hooks/Installments/useInstallments';
import { useNegotiationData } from '@/hooks/Negotiation/useNegotiationData';
import { useNegotiationProcess } from '@/hooks/Negotiation/useNegotiationProcess';
import { useLoading } from '@/hooks/useLoading';
import { apiPost } from '@/server/services/api';
import { useCallback, useLayoutEffect, useState } from 'react';
import { useFilterInstallments } from '../Installments/UseFilterInstallmentsReturn';
import { useApi } from '../useApi';
import { useNegotiationUIReturn } from './useNegotiationUIReturn';

/**
 * Hook principal para negociação de parcelas
 */
export const useNegotiateYourInstallments = (): UseNegotiateYourInstallmentsReturn => {
  const { getFetch } = useApi();
  const { user, content } = useAppContext();
  const { getInstallments } = useInstallments();
  const { loading, setLoadingInfo } = useLoading();
  const { processInstallments } = useFilterInstallments();
  const [contentLocalInstallment, setContentLocalInstallment] = useState<Content | null>(null);

  // Inicializa hooks de negociação
  const uiState = useNegotiationUIReturn();
  const dataState = useNegotiationData();
  const processState = useNegotiationProcess(uiState, dataState);

  const { setFilteredInstallments, setPaid, setDataAntecipacaoDesconto__c } = dataState;
  const { showArea } = uiState;

  /**
   * Carrega as parcelas iniciais
   */
  const loadInstallments = useCallback(async () => {
    try {
      const installmentsData = await getInstallments(
        user?.CodigoSienge__c as string,
        content?.UnidadeAtivoName as string
      );

      const { isPaid, filteredInstallments, hasReparcelamentoToday } = processInstallments(
        installmentsData as Installments
      );

      if (filteredInstallments.length === 0 && isPaid) {
        uiState.setReturnTitle('Você não tem nenhuma parcela em aberto.');
        showArea('return');
        setLoadingInfo(false);
        return;
      }

      if (hasReparcelamentoToday) {
        uiState.setReturnTitle('Você já fez uma negociação hoje.');
        uiState.setReturnText(
          '<p style="width:250px;text-align:center;">Você poderá realizar 1 operação novamente amanhã.</p><p style="text-align:center;">Obrigado.</p>'
        );
        showArea('return');
        setLoadingInfo(false);
        return;
      }

      // Configura estado inicial
      setFilteredInstallments(filteredInstallments);
      setPaid(isPaid);

      // Mostra área apropriada com base no status
      if (!isPaid) {
        const newNegotiateData = {
          type: 'inadimplente',
          action: 'simulacao',
          codigo_sienge_user: user?.CodigoSienge__c as string,
          assetId: content?.AssetId as string
        };
        dataState.setNegotiateData(newNegotiateData);
        showArea('condicoes');
        processState.simulationAction(newNegotiateData);
      } else {
        showArea('parcelas');
        setLoadingInfo(false);
      }
    } catch (error) {
      console.error('Erro ao carregar parcelas:', error);
      setLoadingInfo(false);
    }
  }, [
    user,
    content,
    getInstallments,
    processInstallments,
    showArea,
    setLoadingInfo,
    setFilteredInstallments,
    setPaid,
    uiState,
    dataState,
    processState
  ]);

  useLayoutEffect(() => {
    const getDateToNegotiation = async () => {
      const returnNegotiate = await apiPost('getDateToNegotiation', null, {});
      // const returnNegotiate = await getFetch<{ data: { DataAntecipacaoDesconto__c: string } }>({
      //   method: 'POST',
      //   route: 'getDateToNegotiation',
      //   body: null,
      //   isNegotiate: true
      // });
      setDataAntecipacaoDesconto__c(returnNegotiate?.data?.DataAntecipacaoDesconto__c as string);
    };
    getDateToNegotiation();
  }, []);

  useLayoutEffect(() => {
    if (content?.type !== 'morador') return;

    const ContractId = content?.Contract?.ContractId ?? content?.ContractId ?? '';
    const ContractIdLocal =
      contentLocalInstallment?.Contract?.ContractId ?? contentLocalInstallment?.ContractId ?? '';

    if (ContractId !== ContractIdLocal) {
      setContentLocalInstallment(content);
      loadInstallments();
    }
  }, [content]);

  // Wrapper para selectInstallments
  const selectInstallments = (newInstallment: SelectedInstallment) => {
    // dataState.selectInstallments(content, dataState.negotiateData, newInstallment);
    dataState.selectInstallments(newInstallment);
  };

  // Retorna combinação dos states de UI, dados e processamento
  return {
    ...uiState,
    ...dataState,
    ...processState,
    selectInstallments
  };
};
