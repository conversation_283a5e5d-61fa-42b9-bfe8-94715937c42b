import { NegotiateType, NegotiatedConditionsType } from '@/@types/installments.d';
import { UseNegotiationProcessReturn } from '@/@types/negotiation';
import { useAppContext } from '@/contexts/AppContext';
import { useModal } from '@/hooks';
import { useLoading } from '@/hooks/useLoading';
import { NegotiationService } from '@/server/services/NegotiationService';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { UseNegotiationDataReturn } from './useNegotiationData';
import { UseNegotiationUIReturn } from './useNegotiationUIReturn';

const ENVIANDO_DADOS = 'Enviando condição selecionada... Aguarde!';

/**
 * Hook para processar ações de negociação
 */
export const useNegotiationProcess = (
  uiState: UseNegotiationUIReturn,
  dataState: UseNegotiationDataReturn
): UseNegotiationProcessReturn => {
  const { toggleModal } = useModal();
  const router = useRouter();
  const { user, content } = useAppContext();
  const { setLoadingInfo } = useLoading();

  const { showArea, setReturnTitle, setReturnText, setCaseNumber } = uiState;

  const {
    paid,
    negotiateData,
    selectedInstallments,
    billReceivableId,
    cancelMotivation,
    descriptionToCancel,
    setNegotiateData,
    setNegotiatedConditions,
    setCancelMotivation
  } = dataState;

  /**
   * Exibe modal de erro
   */
  const errorApi = useCallback(
    (error: string | null = null): void => {
      toggleModal({
        text: 'Cliente Cury, identificamos que existem débitos em seu contrato e no momento não temos opções de negociação automática. <br><br>Por favor, abra uma solicitação ou entre em contato com os nossos Canais de Atendimento. ',
        params: {
          style: { fontSize: 16 },
          callback: () => {
            router.push(`/${user?.AccountId}/fale-com-a-cury/solicitar-atendimento`);
          }
        }
      });
    },
    [toggleModal, router, user]
  );

  /**
   * Seleciona condição de negociação
   */
  const selectCondictionBase = useCallback(
    (condictionSelected: string | NegotiatedConditionsType | null): void => {
      if (!condictionSelected) {
        return;
      }

      if (condictionSelected === 'queroReceberContato') {
        dataState.setDescriptionToCancel('Título: Quero receber contato para mais informações.');
        setCancelMotivation('Inadimplente: Mais informações.');
        showArea('condicoes');
        return;
      }

      if (dataState.negotiatedConditions !== null && typeof condictionSelected !== 'string') {
        const dataSelected = condictionSelected;
        let dataDescription = '';

        if (dataSelected.id !== 'NegociacaoParcialDisponivel' && dataSelected.data) {
          dataSelected.data.forEach((element) => {
            let formattedValue = '';
            if (element.title === 'vencimento' || element.title === 'Prazo para pagamento') {
              formattedValue = DateUtils.formatDate(element.value);
            } else if (element.title === 'parcela') {
              formattedValue = element.value;
            } else {
              formattedValue = FinanceUtils.MoneyFormat.format(Number(element.value));
            }
            dataDescription += `${element.title} - ${formattedValue}\n`;
          });
        }

        const descriptionToCancelText = `Título: ${dataSelected.title}\n${dataDescription}`;

        const newNegotiateData: NegotiateType = {
          type: paid ? 'adimplente' : 'inadimplente',
          action: dataSelected.id === null ? 'antecipar' : dataSelected.id,
          assetId: content?.AssetId as string
        };

        let installmentQuantity = {};
        if (['ParcelamentoSaldoDevedor', 'ParcelamentoSaldoVencido'].includes(dataSelected.id)) {
          installmentQuantity = {
            installmentQuantity: dataSelected.data.find((item) => item.title === 'parcela')?.value
          };
        }

        setNegotiateData({ ...newNegotiateData, ...installmentQuantity });
        dataState.setDescriptionToCancel(descriptionToCancelText);
      }
    },
    [dataState, paid, content, showArea, setCancelMotivation, setNegotiateData]
  );

  /**
   * Envia solicitação de cancelamento ou mais informações
   */
  const sendCancel = useCallback(async (): Promise<void | null> => {
    if (cancelMotivation === '') {
      toggleModal({
        text: 'Obrigado! Faça sua simulação e confira das melhores condições para o fluxo do seu contrato.',
        params: {
          callback: () => {
            router.push(`/${user?.AccountId}/financeiro/negocie-suas-parcelas`);
          }
        }
      });
      setLoadingInfo(false);
      BrowserUtils.handleBodyScroll(true);
      return null;
    }

    if (!user || !content) {
      return null;
    }

    try {
      await NegotiationService.sendCaseForMoreInfo(
        user,
        content,
        cancelMotivation,
        descriptionToCancel,
        paid
      );

      setLoadingInfo(false);
      BrowserUtils.handleBodyScroll(true);
    } catch (error) {
      setLoadingInfo(false);
      BrowserUtils.handleBodyScroll(true);
    }
  }, [
    user,
    content,
    cancelMotivation,
    descriptionToCancel,
    paid,
    router,
    toggleModal,
    setLoadingInfo
  ]);

  /**
   * Confirma ação de negociação
   */
  const confirmAction = useCallback(async (): Promise<boolean> => {
    if (descriptionToCancel === 'Título: Quero receber contato para mais informações.') {
      setLoadingInfo(true, 'Criando sua solicitação. Aguarde!');
      BrowserUtils.handleBodyScroll(false);
      await sendCancel();
      return false;
    }

    if (!user || !content || !negotiateData) {
      setLoadingInfo(false);
      BrowserUtils.handleBodyScroll(true);
      return false;
    }

    if (negotiateData.action === 'NegociacaoParcialDisponivel') {
      showArea('parcelas');
      setLoadingInfo(false);
      BrowserUtils.handleBodyScroll(true);
      return false;
    }

    setLoadingInfo(true, ENVIANDO_DADOS);
    BrowserUtils.handleBodyScroll(false);

    try {
      const { result, processedResponse, error } = await NegotiationService.confirmNegotiation(
        paid,
        user,
        content,
        selectedInstallments,
        negotiateData,
        billReceivableId
      );
      if (error) {
        errorApi();
        return false;
      }

      if (processedResponse.success) {
        if (processedResponse.negotiatedConditions) {
          setNegotiatedConditions(processedResponse.negotiatedConditions);
          showArea(processedResponse.area);
        } else if (processedResponse.caseNumber) {
          setCaseNumber(processedResponse.caseNumber);
          setReturnTitle(processedResponse.returnTitle);
          setReturnText(processedResponse.returnText);
          showArea(processedResponse.area);
        }
        return true;
      }
    } catch (error) {
      setLoadingInfo(false);
      BrowserUtils.handleBodyScroll(true);
      errorApi();
    }

    return false;
  }, [
    descriptionToCancel,
    user,
    content,
    negotiateData,
    selectedInstallments,
    paid,
    billReceivableId,
    setLoadingInfo,
    showArea,
    sendCancel,
    errorApi,
    setNegotiatedConditions,
    setCaseNumber,
    setReturnTitle,
    setReturnText
  ]);

  /**
   * Executa ação de simulação
   */
  const simulationAction = useCallback(
    (newNegotiateData = null): void => {
      if (!user || !content || (paid && !selectedInstallments)) {
        return;
      }

      setLoadingInfo(true, 'Simulando... Aguarde!');
      BrowserUtils.handleBodyScroll(false);
      NegotiationService.simulateNegotiation(
        paid,
        user,
        content,
        selectedInstallments,
        billReceivableId
      )
        .then(({ result, processedResponse, error }) => {
          if (error) {
            errorApi();
            return;
          }
          if (processedResponse.success) {
            if (processedResponse.negotiatedConditions) {
              setNegotiatedConditions(processedResponse.negotiatedConditions);
              showArea(processedResponse.area);
            }
          }
        })
        .catch(() => {
          setLoadingInfo(false);
          BrowserUtils.handleBodyScroll(true);
          errorApi();
        });
    },
    [
      user,
      content,
      selectedInstallments,
      paid,
      billReceivableId,
      setLoadingInfo,
      errorApi,
      setNegotiatedConditions,
      showArea
    ]
  );

  /**
   * Executa ação de cancelamento
   */
  const cancelAction = useCallback(async (): Promise<void> => {
    if (paid) {
      showArea('cancelamento');
    } else {
      setLoadingInfo(true);
      BrowserUtils.handleBodyScroll(false);
      await sendCancel();
    }
  }, [paid, showArea, setLoadingInfo, sendCancel]);

  /**
   * Seleciona motivação de cancelamento
   */
  const cancelMotivationSelect = useCallback(
    (value: string | number): void => {
      setCancelMotivation(value as string);
    },
    [setCancelMotivation]
  );

  return {
    selectCondictionBase,
    confirmAction,
    simulationAction,
    cancelAction,
    sendCancel,
    cancelMotivationSelect
  };
};
