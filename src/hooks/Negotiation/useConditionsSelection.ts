import { NegotiatedConditionsType } from '@/@types/installments';
import { useCallback, useEffect, useState } from 'react';

interface UseConditionsSelectionProps {
  paid: boolean;
  negotiatedConditions: NegotiatedConditionsType[] | null;
  selectCondictionBase: (condictionSelected: null | string | NegotiatedConditionsType) => void;
}

/**
 * Hook para gerenciar a seleção de condições de negociação
 */
export const useConditionsSelection = ({
  paid,
  negotiatedConditions,
  selectCondictionBase
}: UseConditionsSelectionProps) => {
  // Estados locais para gerenciar UI de seleção
  const [condictionSelect, setCondictionSelect] = useState<string | null>(null);
  const [indexSelect, setIndexSelect] = useState<number | null>(null);
  const [textButton, setTextButton] = useState<string>(paid ? 'Antecipar' : 'Renegociar');

  /**
   * Seleciona uma condição de negociação pelo índice
   * CORREÇÃO: Agora retorna o índice selecionado apenas para casos válidos
   */
  const selectCondiction = useCallback(
    (index: number) => {
      if (!negotiatedConditions && index !== -1) {
        return null;
      }

      setIndexSelect(index);

      // Caso especial para "quero receber contato"
      if (index === -1) {
        selectCondictionBase('queroReceberContato');
        setCondictionSelect('queroReceberContato');
        return index; // Retorna o índice selecionado
      }

      // Verifica se o índice é válido para a lista de condições negociadas
      if (negotiatedConditions && index >= 0 && index < negotiatedConditions.length) {
        // Seleciona condição pelo índice
        const selectedCondition = negotiatedConditions[index];
        setCondictionSelect(selectedCondition.id || '0');
        selectCondictionBase(selectedCondition);
        return index; // Retorna o índice selecionado
      }

      return null; // Retorna null para casos inválidos
    },
    [negotiatedConditions, selectCondictionBase]
  );

  /**
   * Limpa a seleção de condições
   */
  const clearSelection = useCallback(() => {
    setCondictionSelect(null);
    setIndexSelect(null);
    selectCondictionBase(null);
  }, [selectCondictionBase]);

  /**
   * Atualiza automaticamente o texto do botão com base na condição selecionada
   */
  useEffect(() => {
    switch (condictionSelect) {
      case 'NegociacaoParcialDisponivel':
        setTextButton('Selecionar Parcelas');
        break;
      case 'queroReceberContato':
        setTextButton('Pedir mais informações');
        break;
      default:
        setTextButton(paid ? 'Antecipar' : 'Renegociar');
        break;
    }
  }, [condictionSelect, paid]);

  /**
   * Determina se deve mostrar o card estático de "Quero mais informações"
   */
  const shouldShowStaticCard = useCallback(() => {
    if (negotiatedConditions && negotiatedConditions.length > 0) {
      return !paid && negotiatedConditions[0].id !== 'NegociacaoParcialSimulacao';
    }
    return !paid;
  }, [negotiatedConditions, paid]);

  /**
   * Tenta autoselecionar a única condição disponível
   * CORREÇÃO: Agora verifica explicitamente se a seleção foi bem-sucedida
   */
  const tryAutoSelect = useCallback(() => {
    if (negotiatedConditions && negotiatedConditions.length === 1) {
      const result = selectCondiction(0);
      return result !== null;
    }
    return false;
  }, [negotiatedConditions, selectCondiction]);

  /**
   * Log de diagnóstico para debugar o estado das condições negociadas
   */

  return {
    // Estados
    condictionSelect,
    indexSelect,
    textButton,

    // Ações
    selectCondiction,
    clearSelection,
    shouldShowStaticCard,
    tryAutoSelect
  };
};
