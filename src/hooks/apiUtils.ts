/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { BodyType } from '@/@types/apiTypes';
import { API_URL } from '@/constants';
import { getServerToken } from '@/server/services/cookiesService';

export const buildUrl = (
  route: string,
  { queryString = {} }: { queryString?: Record<string, string> }
): string => {
  const searchParams = new URLSearchParams(queryString).toString();
  const paramsFinal = searchParams ? `?${searchParams}` : '';
  const api = !route.startsWith('services') ? API_URL : '';
  return `${api}/${route}${paramsFinal}`;
};

export const buildFormData = (body: BodyType, file: File | null | undefined): FormData | null => {
  if (!body && !file) return null;

  const formData = new FormData();

  if (body) {
    Object.keys(body).forEach((key) => {
      if (key === 'file') return; // Skip, pois tratamos o file separadamente

      const value = body[key];

      // Ignorar valores null e undefined
      if (value === null || value === undefined) {
        return;
      }

      // Tratar arrays e objetos
      if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
        formData.append(key, JSON.stringify(value));
      } else {
        // Converter para string outros tipos primitivos
        formData.append(key, String(value));
      }
    });
  }

  if (file) {
    formData.append('file', file);
  }

  return formData;
};

export const GetHeaders = async (
  getData: ({ id, index }: { id: string; index?: string | number }) => string | null
): Promise<HeadersInit> => {
  let token: string | null = null;

  if (getData({ id: 'savePass' })) {
    token = getData({ id: 'token' });
  } else {
    token = await getServerToken();
  }

  const headers: Record<string, string> = {
    'Content-Encoding': 'gzip, deflate, br'
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Adicionar token de impersonificação
  if (typeof document !== 'undefined') {
    const impersonateToken = document.cookie
      .split('; ')
      .find((row) => row.startsWith('impersonate_token='))
      ?.split('=')[1];

    if (impersonateToken) {
      headers['X-Impersonation-Token'] = impersonateToken;
    }
  }

  return headers;
};
