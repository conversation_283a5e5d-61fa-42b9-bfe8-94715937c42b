import { ChangeEvent, ClipboardEvent, KeyboardEvent, useState } from 'react';

// 2. Hook personalizado para gerenciar o estado do código
export function useVerificationCode(length = 6) {
  const [inputs, setInputs] = useState<string[]>(Array(length).fill(''));
  const [isCodeValid, setIsCodeValid] = useState<boolean | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const resetCode = () => {
    setInputs(Array(length).fill(''));
    setIsCodeValid(null);
    setErrorMessage('');
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    const id = parseInt(event.target.getAttribute('id') ?? '', 10);

    // Permitir apenas dígitos alfanuméricos
    const sanitizedValue = value.replace(/[^0-9A-Z]/gi, '').toUpperCase();

    // Atualizar o input atual
    if (sanitizedValue !== value) {
      event.target.value = sanitizedValue;
    }

    // Atualizar o estado
    setInputs(inputs.map((input, index) => (id === index ? sanitizedValue : input)));
    setIsCodeValid(null);
    setErrorMessage('');

    // Focar no próximo campo se tiver valor
    if (sanitizedValue && id < length - 1) {
      const nextInput = document.getElementById((id + 1).toString());
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    const id = parseInt(event.currentTarget.getAttribute('id') ?? '', 10);

    // Se pressionar Backspace em um campo vazio, voltar para o anterior
    if (event.key === 'Backspace' && !inputs[id] && id > 0) {
      const prevInput = document.getElementById((id - 1).toString());
      if (prevInput) {
        prevInput.focus();
      }
    }

    // Se pressionar Delete em um campo vazio, avançar para o próximo
    if (event.key === 'Delete' && !inputs[id] && id < length - 1) {
      const nextInput = document.getElementById((id + 1).toString());
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  const handlePaste = (event: ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
    const pastedText = event.clipboardData.getData('text');
    const sanitizedText = pastedText
      .replace(/[^0-9A-Z]/gi, '')
      .toUpperCase()
      .slice(0, length);

    // Preencher os inputs com o texto colado
    const newInputs = [...inputs];
    for (let i = 0; i < sanitizedText.length; i++) {
      newInputs[i] = sanitizedText[i];
    }

    setInputs(newInputs);

    // Focar no próximo campo vazio após o último preenchido
    if (sanitizedText.length < length) {
      const nextInput = document.getElementById(sanitizedText.length.toString());
      if (nextInput) {
        nextInput.focus();
      }
    }

    // Se preencheu todos os campos, verificar automaticamente
    if (sanitizedText.length === length) {
      return sanitizedText;
    }

    return null;
  };

  const getCode = () => inputs.join('');

  const setError = (message: string) => {
    setIsCodeValid(false);
    setErrorMessage(message);
  };

  return {
    inputs,
    isCodeValid,
    isSubmitting,
    errorMessage,
    setIsSubmitting,
    setIsCodeValid,
    setError,
    handleChange,
    handleKeyDown,
    handlePaste,
    resetCode,
    getCode
  };
}
