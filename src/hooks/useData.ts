'use client';

import { destroyCookie, parseCookies, setCookie } from 'nookies';
import { useCallback, useState } from 'react';

interface DataOptions<T> {
  id: string;
  value?: T;
  index?: string | number;
  isSetCookie?: boolean;
  cookieMaxAge?: number;
}

export function useData() {
  const [dataMap, setDataMap] = useState<Record<string, any>>({});

  // Função para obter dados (suporta cache, cookies e localStorage)
  const getData = useCallback(
    <T>({ id, index = 0 }: { id: string; index?: string | number }): T | null => {
      const key = `${id}-${index}`;
      if (dataMap[key]) return dataMap[key];

      // Tenta obter do cookie
      const cookies = parseCookies();
      const cookieValue = cookies[id];
      if (cookieValue && cookieValue !== 'undefined') {
        try {
          const parsedValue = JSON.parse(cookieValue) as T;
          setDataMap((prev) => ({ ...prev, [key]: parsedValue }));
          return parsedValue;
        } catch (error) {
          console.error(`Erro ao parsear cookie [${id}]:`, error);
        }
      }

      // Tenta obter do localStorage
      // if (window) {
      if (typeof window !== 'undefined') {
        const localStorageValue = localStorage.getItem(id);
        if (localStorageValue && localStorageValue !== 'undefined') {
          try {
            const parsedValue = JSON.parse(localStorageValue) as T;
            setDataMap((prev) => ({ ...prev, [key]: parsedValue }));
            return parsedValue;
          } catch (error) {
            console.error(`Erro ao parsear localStorage [${id}]:`, error);
          }
        }
        // }
      }

      return null;
    },
    [dataMap]
  );

  // Função para criar ou atualizar dados
  const createData = useCallback(
    <T>({
      id,
      value,
      index = 0,
      isSetCookie = false,
      cookieMaxAge = 60 * 60 * 24 * 7 // 7 dias por padrão
    }: DataOptions<T>) => {
      if (value === undefined) return;

      const key = `${id}-${index}`;
      setDataMap((prev) => ({ ...prev, [key]: value }));

      if (isSetCookie) {
        setCookie(null, id, JSON.stringify(value), {
          maxAge: cookieMaxAge,
          path: '/'
        });
      }
      if (window) {
        if (typeof window !== 'undefined') {
          try {
            localStorage.setItem(id, JSON.stringify(value));
          } catch (error) {
            console.error(`Erro ao armazenar em localStorage [${id}]:`, error);
          }
        }
      }
    },
    []
  );

  // Função para obter valor específico de um cookie
  const getCookie = useCallback(<T>(id: string): T | null => {
    const cookies = parseCookies();
    const cookieValue = cookies[id];
    if (cookieValue && cookieValue !== 'undefined') {
      try {
        return JSON.parse(cookieValue) as T;
      } catch (error) {
        console.error(`Erro ao parsear cookie [${id}]:`, error);
      }
    }
    return null;
  }, []);

  const getLocalStorage = <T>(id: string): T | null => {
    const localStorageSaved = typeof window !== 'undefined' ? localStorage.getItem(id) : null;
    const localStorageResponse =
      localStorageSaved && localStorageSaved !== 'undefined' ? JSON.parse(localStorageSaved) : null;
    return localStorageResponse as T;
  };

  const destroyData = () => {
    const cookie = parseCookies();
    if (cookie) {
      Object.keys(cookie).forEach((cookieKey) => {
        destroyCookie(null, cookieKey);
      });
    }
    return cookie;
  };

  return {
    getData,
    createData,
    getCookie,
    getLocalStorage,
    destroyData
  };
}
