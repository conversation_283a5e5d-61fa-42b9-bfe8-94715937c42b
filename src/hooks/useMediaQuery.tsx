/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useCallback, useLayoutEffect, useState } from 'react';

import { BREAKPOINTS } from '@/constants/BREAKPOINTS';

interface MediaQueryProps {
  windowWidth: number;
  isMobile: boolean | null;
  isTablet: boolean | null;
  isLaptop: boolean | null;
  isLaptopLarge: boolean | null;
  isDesktop: boolean | null;
}

const THROTTLE_TIMEOUT = 150; // ms

export const useMediaQuery = () => {
  const [state, setState] = useState<MediaQueryProps>({
    windowWidth: 0,
    isMobile: null,
    isTablet: null,
    isLaptop: null,
    isLaptopLarge: null,
    isDesktop: null
  });

  const resizeHandler = useCallback(() => {
    const currentWindowWidth = window.screen.width || window.innerWidth;

    setState((prevState) => ({
      ...prevState,
      windowWidth: currentWindowWidth,
      isMobile: currentWindowWidth < BREAKPOINTS.tablet,
      isTablet:
        currentWindowWidth >= BREAKPOINTS.tablet && currentWindowWidth < BREAKPOINTS.laptop,
      isLaptop:
        currentWindowWidth >= BREAKPOINTS.laptop && currentWindowWidth < BREAKPOINTS.laptopLarge,
      isLaptopLarge:
        currentWindowWidth >= BREAKPOINTS.laptopLarge && currentWindowWidth < BREAKPOINTS.desktop,
      isDesktop: currentWindowWidth >= BREAKPOINTS.desktop
    }));
  }, []);

  const throttledResizeHandler = useCallback(() => {
    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(resizeHandler, THROTTLE_TIMEOUT);
    };

    return handleResize;
  }, [resizeHandler]);

  useLayoutEffect(() => {
    // Executa imediatamente na montagem
    resizeHandler();
    
    const handleResize = throttledResizeHandler();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [resizeHandler, throttledResizeHandler]);

  return state;
};
