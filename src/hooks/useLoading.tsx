/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { LoadingContextProviderProps, LoadingContextType } from '@/@types/loading';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';

const LoadingContext = createContext<LoadingContextType>({
  loading: false,
  textInfoLoad: '',
  setLoadingInfo: () => {}
});

export const useLoading = () => useContext(LoadingContext);

export const LoadingProvider = ({ children }: LoadingContextProviderProps) => {
  const [loading, setLoading] = useState(false);
  const [textInfoLoad, setTextInfoLoad] = useState('');

  const setLoadingInfo = useCallback((loading: boolean, textInfoLoad = '') => {
    setLoading((prevLoading) => {
      if (prevLoading !== loading) {
        return loading;
      }
      return prevLoading;
    });

    setTextInfoLoad((prevTextInfoLoad) => {
      if (prevTextInfoLoad !== textInfoLoad) {
        return textInfoLoad;
      }
      return prevTextInfoLoad;
    });
  }, []);

  const value = useMemo(
    () => ({
      loading,
      textInfoLoad,
      setLoadingInfo
    }),
    [loading, textInfoLoad, setLoadingInfo]
  );

  return <LoadingContext.Provider value={value}>{children}</LoadingContext.Provider>;
};
