/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimateFadeIn, TRANSITIONS } from '@/constants/ANIMATIONS';
import { useModal } from '@/hooks';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { downloadFile } from '@/utils/DownloadUtils';
// import { AnimatePresence, motion } from "motion/react"
import { AnimatePresence, motion } from "motion/react"

import Image from 'next/image';
import { useEffect } from 'react';
import { Buttonblue } from '../Commons/buttons/Buttonblue';

// import 'react-pdf/dist/Page/AnnotationLayer.css';
// import 'react-pdf/dist/Page/TextLayer.css';

interface InputProps {
  filename: string;
  urlBoleto: string;
  digitableNumberBoleto: string;
  // password?: string;
  show: boolean;
  toggleBoleto: () => void;
}

const Boleto = ({
  filename,
  urlBoleto,
  digitableNumberBoleto,
  // password,
  show,
  toggleBoleto
}: InputProps) => {
  const srcBoleto = urlBoleto;
  const { toggleModal } = useModal();
  const download = () => {
    downloadFile(srcBoleto, filename, toggleModal)
      .then(() => {})
      .catch((error) => {
        console.error('Failed:', error);
      });
  };
  function copy() {
    toggleModal({ text: 'Código de barra copiado com sucesso.' });
    BrowserUtils.copyToClipboard(digitableNumberBoleto);
  }

  useEffect(() => {
    BrowserUtils.handleBodyScroll(!show);
  }, [show]);

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          id='boleto-modal'
          initial='collapsed'
          animate={show ? 'open' : 'collapsed'}
          className='mb-10 w-full h-full z-[600000] flex justify-center items-center'
          variants={AnimateFadeIn}
          exit='exit'
          transition={TRANSITIONS.linear}>
          <div className='bg-[#1a374d90] bg-opacity-90 w-full h-full fixed inset-0 z-50 p-4 flex flex-col justify-start items-center overflow-auto'>
            <div className='bg-white rounded relative px-4 py-10 md:max-w-[1000px] top-[140px] flex flex-col justify-center items-center'>
              <button
                type='button'
                className='bg-transparent absolute top-1 right-1'
                onClick={() => toggleBoleto()}>
                <Image
                  src='/images/close.png'
                  alt='Icon'
                  className='w-auto h-auto'
                  width={30}
                  height={30}
                  priority
                />
              </button>
              <h1 className='text-navy-blue font-bold tracking-1 text-xl text-center'>
                Boleto gerado com sucesso
              </h1>
              <p className='text-center w-full md:w-[60%]'>
                Baixe o Boleto (PDF) para seu dispositivo ou copie o código de barras para pagamento
                escolhendo uma das opções abaixo.
              </p>
              {/* <ShowPDF
                className={isMobile ? 'w-[70%]' : 'w-[30%]'}
                filePath={srcBoleto}
                password={password}
              /> */}
            </div>
            <div className='flex justify-center w-full flex-row mb-14 pt-[140px] md:max-w-[1000px]'>
              <div className='flex flex-col items-center w-1/2 mr-1'>
                <Buttontransparent
                  text='Copiar código de barras'
                  color='white'
                  classExtra='!bg-navy-blue'
                  onClick={copy}
                />
              </div>
              <div className='flex flex-col items-center w-1/2 ml-1'>
                <Buttonblue
                  text='Baixar Boleto (PDF)'
                  background='white'
                  color='navy-blue'
                  disabled={false}
                  onClick={download}
                />
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Boleto;
