/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Dispatch, SetStateAction, useState } from 'react';
import { Calendar } from 'react-calendar';

import { Holidays } from '@/@types/Schedule';

import 'react-calendar/dist/Calendar.css';

type ValuePiece = Date | null;
export type Value = ValuePiece | [ValuePiece, ValuePiece];

interface CustomCalendarProps {
  setDayOfWeek: Dispatch<SetStateAction<string>>;
  setDateSeleted: Dispatch<SetStateAction<string>>;
  allowedDaysOfWeek: Set<unknown>;
  setSelectDateValue: Dispatch<SetStateAction<Value | null>>;
  holidays: Holidays[];
  busyDays: string[];
}

export default function CustomCalendar({
  setDayOfWeek,
  setDateSeleted,
  allowedDaysOfWeek,
  setSelectDateValue,
  holidays,
  busyDays
}: Readonly<CustomCalendarProps>) {
  const [value, setValue] = useState<Value>();
  const selectDate = (ev: Value) => {
    setValue(ev);
    setSelectDateValue(ev);
    let date: Date | null = null;
    if (ev instanceof Date) {
      date = ev;
    } else if (ev && Array.isArray(ev)) {
      date = ev[0];
    }
    if (date) {
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
      const dateFormat = date.toLocaleDateString('pt-BR', {
        day: 'numeric',
        month: 'numeric',
        year: 'numeric'
      });
      setDayOfWeek(dayOfWeek);
      setDateSeleted(dateFormat);
    }
  };

  const isWeekend = (date: Date): boolean => {
    const day = date.getDay();
    return day === 0 || day === 6;
  };

  const getNextWorkingDay = (date: Date): Date => {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);

    while (isWeekend(nextDay) || isHoliday(nextDay)) {
      nextDay.setDate(nextDay.getDate() + 1);
    }

    return nextDay;
  };

  const isHoliday = (date: Date) => {
    const formattedDate = date.toISOString().slice(0, 10);
    return holidays.some((holiday: Holidays) => holiday.NextOccurrenceDate === formattedDate);
  };

  const isBusyDay = (date: Date) => {
    const formattedDate = date.toISOString().slice(0, 10);
    return busyDays.some((busyDay: string) => busyDay === formattedDate);
  };

  const disableDays = (date: Date): boolean => {
    const today = new Date();
    const maxAllowedDate = new Date(today);
    maxAllowedDate.setDate(today.getDate() + (30 * 6));
    let minAllowedDate = today;
    for (let i = 0; i < 2; i++) {
      minAllowedDate = getNextWorkingDay(minAllowedDate);
    }
    today.setHours(0, 0, 0, 0);
    return (
      date > maxAllowedDate ||
      date < minAllowedDate ||
      !allowedDaysOfWeek.has(date.getDay()) ||
      isHoliday(date) ||
      isBusyDay(date)
    );
  };
  return (
    <Calendar
      locale='pt-BR'
      showNeighboringMonth={true}
      onChange={selectDate}
      value={value}
      tileDisabled={({ date, view }) => {
        return view === 'month' && disableDays(date);
      }}
    />
  );
}
