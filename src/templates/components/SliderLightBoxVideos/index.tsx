/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import Image from 'next/image';
import { useState } from 'react';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { MiscUtils } from '@/utils/MiscUtils';

interface SliderLightboxProps {
  videos: string[];
  startIndex: number;
}
const SliderLightboxVideos = ({ videos, startIndex }: SliderLightboxProps) => {
  const [prevSlide, setPrevSlide] = useState(0);
  const [currentSlide, setCurrentSlide] = useState(
    startIndex === -1 ? videos.length - 1 : startIndex
  );

  const handleNextSlide = () => {
    setPrevSlide(currentSlide);

    setCurrentSlide((prevSlide) => (prevSlide === videos.length - 1 ? 0 : prevSlide + 1));
  };

  const handlePrevSlide = () => {
    setPrevSlide(currentSlide);

    setCurrentSlide((prevSlide) => (prevSlide === 0 ? videos.length - 1 : prevSlide - 1));
  };
  const { maxWidth } = BrowserUtils.getSizeWindow();
  let imageWidth = maxWidth;
  if (maxWidth < 768) {
    imageWidth = 768;
  } else if (maxWidth >= 768 && maxWidth <= 1024) {
    imageWidth = 1024;
  } else if (maxWidth >= 1024) {
    imageWidth = 1400;
  }
  if (videos === null) return null;

  const videosCards = Array.isArray(videos) ? videos : (JSON.parse(videos || '[]') as string[]);
  const videoSRCprevSlide = MiscUtils.converterLinkYouTube(videosCards[prevSlide]);
  const videoSRCcurrentSlide = MiscUtils.converterLinkYouTube(videosCards[currentSlide]);

  return (
    <div className='w-full h-[100%]  relative  overflow-hidden'>
      <div className='flex justify-center items-center'>
        {prevSlide !== null && prevSlide !== 0 && (
          <div className='relative w-full' style={{ paddingTop: '56.25%' }}>
            <iframe
              className='absolute top-0 left-0 w-full h-full'
              src={videoSRCprevSlide}
              title='YouTube video player'
              allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'></iframe>
          </div>
        )}

        <div className='relative w-full' style={{ paddingTop: '56.25%' }}>
          <iframe
            className='absolute top-0 left-0 w-full h-full'
            src={videoSRCcurrentSlide}
            title='YouTube video player'
            width={imageWidth}
            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'></iframe>
        </div>
      </div>

      <button
        className='absolute top-1/2 left-[-6rem] transform -translate-y-1/2 z-10 p-4 text-white bg-white rotate-180 shadow-lg'
        onClick={handlePrevSlide}>
        <Image
          src='/images/angle-right.svg'
          alt='Icon'
          className=' w-auto h-auto'
          width={30}
          height={30}
          priority
        />
      </button>
      <button
        className='absolute top-1/2 right-[-6rem] transform -translate-y-1/2 z-10 p-4 text-white bg-white  shadow-lg'
        onClick={handleNextSlide}>
        <Image
          src='/images/angle-right.svg'
          alt='Icon'
          className=' w-auto h-auto'
          width={30}
          height={30}
          priority
        />
      </button>
    </div>
  );
};

export default SliderLightboxVideos;
