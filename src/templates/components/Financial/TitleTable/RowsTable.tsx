/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useAppContext } from '@/contexts/AppContext';
import { memo, useMemo } from 'react';
import CustomLink from '../../Commons/buttons/CustomLink';

interface RowsTableProps {
  value: string | number;
  id: number;
  parentIndex: number;
  customClass?: string;
}

// Base className that never changes
const baseClassName =
  'p-3 px-4 mx-6 text-navy-blue text-sm tracking-wide text-left w-auto min-w-auto border-b-1 border-b-[#e3e7ea]';
const linkClassName = 'text-center block w-auto min-w-auto';

const MemoizedCustomLink = memo(CustomLink);

const RowsTable = memo(({ value, id, parentIndex, customClass }: RowsTableProps) => {
  const { user } = useAppContext();

  // Memoize the href construction to prevent unnecessary recalculations
  const href = useMemo(
    () => `/${user?.AccountId}/financeiro/parcela-detalhada/${id}/${parentIndex}`,
    [user?.AccountId, id, parentIndex]
  );

  // Memoize the className to prevent string concatenation on every render
  const className = useMemo(
    () => (customClass ? `${baseClassName} ${customClass}` : baseClassName),
    [customClass]
  );

  return (
    <td className={className}>
      <MemoizedCustomLink href={href} className={linkClassName}>
        {value}
      </MemoizedCustomLink>
    </td>
  );
});

RowsTable.displayName = 'RowsTable';

export default RowsTable;
