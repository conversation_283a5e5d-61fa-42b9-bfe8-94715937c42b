/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import React from 'react';

interface TitleTableProps {
  value: string;
}

export const TitleTable: React.FC<TitleTableProps> = ({ value }) => (
  <th className='pb-1.5 mx-6 text-light-blue text-sm font-[500] tracking-wide text-center w-auto min-w-auto border-b-1  border-b-[#e3e7ea]'>
    {value}
  </th>
);
