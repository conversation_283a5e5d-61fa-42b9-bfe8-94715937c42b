/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { FILES_URL } from '@/constants';
import { useMediaQuery } from '@/hooks';
import Image from 'next/image';
import { useRef, useState } from 'react';
import { useSwipeable } from 'react-swipeable';
import 'swiper/css/zoom';
import ImageLoad from '../Commons/ImageLoad';
// import { useGesture } from 'react-use-gesture';
// import { useGesture } from '@use-gesture/react';

import { createUseGesture, dragAction, pinchAction } from '@use-gesture/react';

// import styles from './styles.module.css'

const useGesture = createUseGesture([dragAction, pinchAction]);

interface SliderLightboxProps {
  images: string[];
  startIndex: number;
}

const SliderLightbox = ({ images, startIndex }: SliderLightboxProps) => {
  const [_, setPrevSlide] = useState<number>(0);
  const { isMobile } = useMediaQuery();
  let imageLenght = 0;

  const imageElement = useRef<HTMLImageElement>(null);
  const dragStartPos = useRef({ x: 0, y: 0 });
  const [imagePos, setImagePos] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0
  });

  const [imageScale, setImageScale] = useState<number>(1);

  const [currentSlide, setCurrentSlide] = useState<number>(
    startIndex === -1 ? images.length - 1 : startIndex
  );

  const resetImagePos = () => {
    if (isMobile) {
      setImagePos({ x: 0, y: 0 });
      setImageScale(1);
      dragStartPos.current = { x: 0, y: 0 };
    }
  };

  const handleNextSlide = () => {
    setPrevSlide(currentSlide);
    setCurrentSlide((prevSlide) => (prevSlide === imageLenght - 1 ? 0 : prevSlide + 1));
    resetImagePos();
  };

  const handlePrevSlide = () => {
    setPrevSlide(currentSlide);
    setCurrentSlide((prevSlide) => (prevSlide === 0 ? imageLenght - 1 : prevSlide - 1));
    resetImagePos();
  };

  const swipeHandlers = useSwipeable({
    onSwipedLeft: handleNextSlide,
    onSwipedRight: handlePrevSlide,
    preventScrollOnSwipe: true,
    trackMouse: true,
    swipeDuration: 200
  });

  useGesture(
    {
      onDragStart: () => {
        if (isMobile) {
          dragStartPos.current = {
            x: imagePos.x,
            y: imagePos.y
          };
        }
      },
      onPinch: ({ offset: [scale] }) => {
        if (scale < 1) {
          scale = 1;
        }
        if (isMobile) setImageScale(scale);
      },
      onDrag: ({ offset: [x, y] }) => {
        if (isMobile) {
          const newPos = {
            x: dragStartPos.current.x + x,
            y: dragStartPos.current.y + y
          };
          setImagePos(newPos);
        }
      }
    },
    {
      drag: {
        from: [0, 0],
        threshold: 5
      },
      target: imageElement,
      eventOptions: { passive: true }
    }
  );

  if (images === null) return null;
  const imagesCards = Array.isArray(images) ? images : (JSON.parse(images || '[]') as string[]);
  if (imageLenght === 0) imageLenght = imagesCards.length;

  return (
    <div className='w-full h-[100%] flex items-stretch' {...swipeHandlers}>
      <div className='absolute w-full self-center'>
        {/* <animated.div className={styles.card} ref={ref} style={style}></animated.div> */}

        <div
          className='relative md-max:w-full md-max:top-1/2 md-max:transform md-max:-translate-y-1/2'
          style={{
            left: imagePos.x,
            top: imagePos.y,
            scale: imageScale,
            touchAction: 'none'
          }}
          // style={style}
          ref={imageElement}>
          <ImageLoad
            key={imagesCards[currentSlide]}
            imageUrl={`${FILES_URL}${imagesCards[currentSlide]}.webp`}
            customClass={`imageSlider_${currentSlide} absolute object-contain transition-opacity duration-1000 opacity-100 z-2 scale-[1]`}
          />
        </div>
      </div>

      {!isMobile && (
        <>
          <button
            type='button'
            className='cursor-pointer block max-md:hidden absolute top-1/2 left-[-6rem] transform -translate-y-1/2 z-[10000] p-4 text-white bg-white rotate-180 shadow-lg'
            onClick={handlePrevSlide}>
            <Image
              src='/images/angle-right.svg'
              alt='Icon'
              className='w-auto h-auto'
              width={30}
              height={30}
              priority
            />
          </button>
          <button
            type='button'
            className='cursor-pointer block max-md:hidden absolute top-1/2 right-[-6rem] transform -translate-y-1/2 z-[1000001] p-4 text-white bg-white shadow-lg'
            onClick={handleNextSlide}>
            <Image
              src='/images/angle-right.svg'
              alt='Icon'
              className='w-auto h-auto'
              width={30}
              height={30}
              priority
            />
          </button>
        </>
      )}
    </div>
  );
};

export default SliderLightbox;
