/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import Image from 'next/image';
import { useEffect, type JSX } from 'react';

import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { BrowserUtils } from '@/utils/BrowserUtils';

interface ModalPlantProps {
  show: boolean;
  toogleImage: () => void;
  imgPlanta: string;
  titlePlant: JSX.Element | null;
}

/**
 * Renders the modal for displaying a PDF document of a plant image.
 *
 * @param {boolean} show - Boolean state to control the display of the image modal
 * @param {Dispatch<SetStateAction<boolean>>} setshow - Function to set the show state
 * @return {JSX.Element} The JSX element representing the modal with the PDF document
 */
export default function ModalPlant({ show, toogleImage, imgPlanta, titlePlant }: ModalPlantProps) {
  useEffect(() => {
    BrowserUtils.handleBodyScroll(!show);
  }, [show]);

  if (!imgPlanta) return null;
  return (
    <motion.div
      className={`absolute bg-navy-blue bg-opacity-90 w-full h-full  inset-0 z-50 p-4 flex flex-col justify-center items-center ${show ? 'z-[999999999]' : 'z-[-10]'}`}
      initial='collapsed'
      animate={show ? 'open' : 'collapsed'}
      variants={AnimateFadeIn}
      exit='exit'
      transition={{
        duration: 0.3,
        ease: 'easeInOut'
      }}>
      <div
        onClick={toogleImage}
        className='bg-white rounded relative p-4 flex flex-col w-[90%] h-[90%] justify-center items-center modalpdf'>
        <button className='bg-transparent absolute top-1 right-1 z-[100]'>
          <Image
            src='/images/close.png'
            alt='Icon'
            className='w-auto h-auto'
            width={30}
            height={30}
            priority
          />
        </button>
        {imgPlanta && (
          // <ShowPDF className={'w-[100%] h-[100%]'} filePath={imgPlanta} />
          <Image
            src={imgPlanta}
            className={'w-[100%] h-auto'}
            width={1200}
            height={1200}
            priority
            alt='planta'
          />
        )}
      </div>
      {titlePlant}
    </motion.div>
  );
}
