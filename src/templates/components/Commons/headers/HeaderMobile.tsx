/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { useAppContext } from '@/contexts/AppContext';
import { MiscUtils } from '@/utils/MiscUtils';
import { usePathname } from 'next/navigation';
import { useCallback } from 'react';
import ButtonBack from '../buttons/ButtonBack';

const HeaderMobile = () => {
  const { content, mobile, nameFinal } = useAppContext();
  const subtitleHeader = mobile?.subtitleHeader || '';
  const pathname = usePathname();

  const textUnidade = useCallback(
    () => MiscUtils.defineTextUnidade(content?.type as string, content?.UnidadeAtivoName as string),
    [content?.type, content?.UnidadeAtivoName]
  );
  if (!content || pathname === null || pathname === undefined) return null;
  const pathParts = pathname.split('/').filter(Boolean);
  return (
    <nav className='w-full bg-neutral-blue p-5 flex justify-center sticky top-0 z-20'>
      <div className='container flex flex-row items-center'>
        <ButtonBack />
        <div className='flex flex-col ml-2'>
          <small className='tracking-widest text-xs text-white flex'>
            {pathParts[1] === 'home'
              ? 'Bem vindo cliente Cury'
              : `${textUnidade()} | ${content?.Empreendimento.name}`}
          </small>
          {/* <p className='tracking-widest font-bold text-xl text-white'>{subtitleHeaderMobile}</p> */}
          <p className='tracking-widest font-bold text-xl text-white'>
            {pathParts[1] === 'home' ? `Olá, ${nameFinal}` : `${subtitleHeader}`}
          </p>
        </div>
      </div>
    </nav>
  );
};

export default HeaderMobile;
