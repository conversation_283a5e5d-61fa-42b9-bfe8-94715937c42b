/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import Image from 'next/image';
import { useCallback, useLayoutEffect, useState } from 'react';

import { useMessage } from '@/templates/Messages/hooks/useMessage';

import { useAppContext } from '@/contexts/AppContext';
import SelectContent from '../SelectContent';
import CustomLink from '../buttons/CustomLink';

const HeaderDesk = () => {
  const { user, nameFinal, avatar } = useAppContext();
  const { getCountMessagesUnread } = useMessage();
  const [total, setTotal] = useState(0);

  const getTotalMessage = useCallback(async () => {
    const totalmessages = await getCountMessagesUnread(user?.AccountId as string);
    setTotal(totalmessages);
  }, []);

  useLayoutEffect(() => {
    getTotalMessage()
      .then(() => {})
      .catch((error) => {
        console.error('Failed:', error);
      });
  }, []);

  return (
    <nav className='w-full bg-white p-5 flex justify-center sticky top-0 z-[3500]'>
      <div className='container flex flex-row items-center justify-between  '>
        <CustomLink href={`/${user?.AccountId}/home`}>
          <Image
            src='/images/logo.svg'
            alt='Logo'
            className='w-auto h-auto'
            width={230}
            height={80}
            quality={75}
            priority
          />
        </CustomLink>
        <div className='flex flex-row items-center'>
          <div className='flex flex-row items-center'>
            <div className='max-md:hidden'>
              <small className='tracking-1 text-xs text-navy-blue flex justify-end'>
                Bem vindo cliente Cury
              </small>
              <p className='tracking-1 font-medium text-sm text-navy-blue text-end whitespace-nowrap overflow-hidden'>
                Olá, {nameFinal}
              </p>
            </div>
            <CustomLink href={`/${user?.AccountId}/meus-dados`}>
              <div className='relative aspect-square overflow-hidden rounded-full flex justify-center items-center max-w-[36px] max-h-[36px] mx-4'>
                <Image
                  src={avatar}
                  alt='Perfil'
                  className='rounded-full mx-4  w-auto h-auto'
                  width={36}
                  height={36}
                  priority
                />
              </div>
            </CustomLink>

            <CustomLink href={`/${user?.AccountId}/mensagens`}>
              <div className='relative'>
                <Image
                  src='/images/email-2.svg'
                  alt='Mensagens'
                  className='rounded-full'
                  width={36}
                  height={36}
                  priority
                />
                {total > 0 && (
                  <div className='absolute top-0.5 right-0 w-2.5 h-2.5 bg-salmon rounded-full'></div>
                )}
              </div>
            </CustomLink>
          </div>

          <div className='max-md:hidden ml-4'>
            <SelectContent />
          </div>
        </div>
      </div>
    </nav>
  );
};

export default HeaderDesk;
