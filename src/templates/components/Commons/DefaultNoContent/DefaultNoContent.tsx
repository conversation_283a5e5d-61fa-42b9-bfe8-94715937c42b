/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useState } from 'react';

export default function DefaultNoContent({ text }: { text: string }) {
  const [show, setShow] = useState(false);
  setTimeout(() => {
    setShow(true);
  }, 1000);
  if (!show) return null;
  return (
    <div className='w-full home'>
      <h1 className='font-normal text-center text-base text-navy-blue tracking-1 mt-2 mb-4'>
        <p>{text}</p>
        <p>
          Se tiver alguma dúvida entre em contato com a Cury pela Central de Relacionamento: SP (11)
          3117-1300, RJ (21) 3543- 6887 - somente fixo interior e litoral de SP, de
          segunda-feira a sexta-feira das 09h às 16h.
        </p>
      </h1>
    </div>
  );
}
