/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import React from 'react';
import { AnimationItem } from '@/@types/animations';
import { FILES_URL } from '@/constants';
import ButtonContentHome from './ButtonContentHome';

interface ContentHomeProps {
  src: string;
  name: string;
  UnidadeAtivoName: string;
  index: number;
  showItens: boolean;
  contentSelected: number;
  variants: AnimationItem;
  selectItensInner: () => Promise<void>;
  // selectItensInner: (value: number) => void;
}
function ContentHome({
  index,
  name,
  UnidadeAtivoName,
  src,
  showItens,
  contentSelected,
  variants,
  selectItensInner
}: Readonly<ContentHomeProps>) {
  const srcImage = src ? `${FILES_URL}${src}.webp` : '/images/logo.svg';

  // Simplificamos a estrutura para evitar re-renderizações desnecessárias
  if (!showItens) return null;

  return (
    <ButtonContentHome
      index={index}
      name={name}
      UnidadeAtivoName={UnidadeAtivoName}
      contentSelected={contentSelected}
      src={srcImage}
      action={selectItensInner}
    />
  );
}

// Usar React.memo para evitar re-renderizações desnecessárias
export default React.memo(ContentHome, (prevProps, nextProps) => {
  // Só re-renderiza se alguma dessas props mudar
  return (
    prevProps.index === nextProps.index &&
    prevProps.name === nextProps.name &&
    prevProps.UnidadeAtivoName === nextProps.UnidadeAtivoName &&
    prevProps.src === nextProps.src &&
    prevProps.contentSelected === nextProps.contentSelected &&
    prevProps.showItens === nextProps.showItens
  );
});
