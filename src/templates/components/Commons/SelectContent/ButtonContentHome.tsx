/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Content } from '@/@types/content';
import Image from 'next/image';

interface ButtonContentHomeProps {
  src: string;
  name: string;
  UnidadeAtivoName: string;
  index: number;
  action: () => void;
  contents?: Content[];
  contentSelected?: number;
  showItens?: boolean;
}
export default function ButtonContentHome({
  index,
  name,
  UnidadeAtivoName,
  src,
  action,
  contents,
  contentSelected,
  showItens
}: Readonly<ButtonContentHomeProps>) {
  let classBorder = '';

  if (contentSelected !== -1) {
    classBorder = index !== contentSelected ? 'bg-neutral-blue' : 'bg-blue-secondary';
  }

  return (
    <button
      onClick={action}
      className={`${classBorder} h-auto p-2 pb-1 w-full max-md:ml-0 top-70  rounded left-17 max-md:left-0 w-max-[150px] cursor-pointer`}>
      <div className='flex flex-row items-center justify-start shadow rounded bg-white mb-1 p-3'>
        <div className='flex justify-center items-center w-auto h-[100%] max-w-[110px] min-h-[80px] max-h-[110px] max-md:max-w-auto max-md:max-h-[60px*0.7] mx-2 px-2 max-md:px-0'>
          <Image
            src={src}
            alt={name}
            className={'w-auto h-auto'}
            width={110}
            height={60}
            priority
          />
        </div>
        <div className='flex flex-row justify-between items-center border-l-2 border-light-gray px-4 py-1 w-[70%]'>
          <div className='mr-2'>
            <p className=' tracking-1 font-medium text-left text-xs text-navy-blue break-pretty h-[auto] flex items-center'>
              {name}
            </p>
            <small className='tracking-1 flex text-navy-blue text-xs text-left'>
              {UnidadeAtivoName}
            </small>
          </div>
          {contents && contents.length > 1 && (
            <Image
              src='/images/angle-down.svg'
              alt='Icon'
              className={` ${showItens ? 'rotate-180 transition-all duration-300' : 'transition-all duration-300'} object-contain w-auto h-auto`}
              width={12}
              height={8}
              priority
            />
          )}
        </div>
      </div>
    </button>
  );
}
