/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { BUILD_ID } from '@/BuildId';
import { useEffect, useState } from 'react';
// import { BUILD_ID } from '../BuildId';

const ServiceWorkerRegistration = () => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [currentVersion, setCurrentVersion] = useState(BUILD_ID);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker
          .register(`/service-worker.js?v=${BUILD_ID}`)

          .then(
            (registration) => {
              registration.onupdatefound = () => {
                const installingWorker = registration.installing;
                if (installingWorker) {
                  installingWorker.onstatechange = () => {
                    if (installingWorker.state === 'installed') {
                      if (navigator.serviceWorker.controller) {
                        // Nova versão instalada
                        setUpdateAvailable(true);
                      }
                    }
                  };
                }
              };
            },
            (err) => console.log('Service Worker registration failed: ', err)
          );

        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data && event.data.type === 'VERSION_INFO') {
            const swVersion = event.data.version;
            // Se a versão do SW é diferente do BUILD_ID atual, uma atualização está disponível
            if (swVersion !== BUILD_ID) {
              setUpdateAvailable(true);
              setCurrentVersion(swVersion);
            }
          }
        });
        const checkVersion = () => {
          if (navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
              type: 'GET_VERSION'
            });
          }
        };

        // Verifica a versão inicial após 2 segundos para dar tempo ao SW para inicializar
        setTimeout(checkVersion, 2000);

        // Verifica a versão a cada 5 minutos
        const intervalId = setInterval(checkVersion, 5 * 60 * 1000);

        return () => clearInterval(intervalId);
      });
    }
  }, []);

  const handleUpdate = () => {
    // Força o recarregamento da página para aplicar a nova versão
    window.location.reload();
  };

  if (!updateAvailable) return null;

  return (
    <div className='fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50'>
      <p>Nova versão disponível!</p>
      <button
        onClick={handleUpdate}
        className='mt-2 bg-white text-blue-600 py-1 px-3 rounded font-semibold'>
        Atualizar agora
      </button>
    </div>
  );
};

export default ServiceWorkerRegistration;
