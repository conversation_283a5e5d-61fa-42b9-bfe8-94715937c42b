/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import { useState } from 'react';
import SpinnerLoading from '../Loading/SpinnerLoading';

interface ImageLoadProps {
  imageUrl: string | null;
  customClass?: string;
  showAfterLoad?: boolean;
  customObjectFit?: boolean;
}

const ImageLoad = ({
  imageUrl,
  // customClass = 'object-cover w-full h-full',
  customClass = 'object-cover w-full h-full',
  showAfterLoad = true,
  customObjectFit = true
}: ImageLoadProps) => {
  const [imageLoading, setImageLoading] = useState(true);
  if (!imageUrl) return null;
  const imageLoaded = () => {
    setImageLoading(false);
  };
  let animateStart = {};
  let initialOpacity = {};
  let initialTransition = {};

  if (showAfterLoad) {
    animateStart = { opacity: imageLoading ? 0 : 1 };
    initialOpacity = { opacity: 0 };
    initialTransition = { opacity: { duration: 0.4 } };
  }
  const classFinal = imageLoading
    ? `absolute w-[0px] h-[0px] ${customClass}`
    : `relative w-full h-auto ${customClass}`;

  return (
    <>
      <motion.img
        key={`Image_${imageUrl}`}
        initial={initialOpacity}
        animate={animateStart}
        transition={initialTransition}
        style={{ objectFit: customObjectFit ? 'cover' : 'contain' }}
        className={classFinal}
        onLoad={imageLoaded}
        width='1'
        src={imageUrl}
        sizes='(max-width: 400px) 400px, (max-width: 800px) 800px, 1200px'
        loading='lazy'
        decoding='async'
      />
      {imageLoading && (
        <div className='relative flex w-full h-full min-h-[auto] justify-center items-center '>
          <SpinnerLoading
            width={50}
            height={50}
            className={'absolute w-auto h-auto max-w-16 max-h-16'}
          />
        </div>
      )}
    </>
  );
};

export default ImageLoad;
