/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { useData } from '@/hooks/useData';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { AnimatePresence, motion } from "motion/react"
import { usePathname } from 'next/navigation';
import { useLayoutEffect, useState } from 'react';
import { Buttonblue } from '../buttons/Buttonblue';
import Buttontransparent from '../buttons/Buttontransparent';

const ModalFirstLogin = () => {
  const [openModalFirstLogin, setOpenModalFirstLogin] = useState(false);
  const { getCookie } = useData();
  const thisIsFirstLogin = getCookie<boolean>('thisIsFirstLogin');

  const pathname = usePathname();

  useLayoutEffect(() => {
    if (!thisIsFirstLogin && pathname === '/login') setOpenModalFirstLogin(true);
  }, []);

  if (!openModalFirstLogin) return null;
  BrowserUtils.handleBodyScroll(false);

  const onClose = () => {
    setOpenModalFirstLogin(false);
    BrowserUtils.handleBodyScroll(true);
  };
  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key='loading'
        initial={{ opacity: openModalFirstLogin ? 0 : 1 }}
        animate={{ opacity: openModalFirstLogin ? 1 : 0 }}
        exit={{ opacity: openModalFirstLogin ? 0 : 1 }}
        transition={{
          duration: 0.4,
          ease: [0.4, 0, 0.2, 1]
        }}
        className='absolute z-[100099900] w-full h-full flex flex-col justify-center items-center bg-[rgba(255,255,255,0.8)]'>
        <div
          style={{
            position: 'absolute',
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            flexDirection: 'column',
            justifyItems: 'center',
            alignItems: 'center',
            width: '80%',
            height: '50%',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'white',
            padding: '20px',
            zIndex: 1000,
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            borderRadius: '5px',
            border: '5px solid rgb(98 130 154)'
          }}>
          <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
            Olá, esse é seu primeiro acesso no novo App Cliente Cury?
          </h3>
          <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 w-full md:w-[80%]'>
            <div className='w-full flex justify-between max-md:flex-col'>
              <div className='w-[300px] max-md:w-full'>
                <Buttontransparent
                  text='Já acessei'
                  color='navy-blue'
                  onClick={onClose}
                  classExtra='w-[300px] max-md:w-full'
                />
              </div>
              <div className='w-[300px] max-md:w-full'>
                <Buttonblue
                  text='É meu primeiro acesso'
                  background='navy-blue'
                  color='white'
                  route={'/primeiro-acesso'}
                  classExtra='w-[300px] max-md:w-full'
                />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ModalFirstLogin;
