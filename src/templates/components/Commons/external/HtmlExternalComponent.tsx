/* eslint-disable react/prop-types */
/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import parse, { DOMNode, domToReact, Element, HTMLReactParserOptions } from 'html-react-parser';
import Link from 'next/link';
import React from 'react';

interface HtmlExternalComponentProps {
  html: string;
  className?: string;
}

type PostMessageData = {
  type: string;
  url: string;
  data: Record<string, unknown>;
};

const VOID_ELEMENTS = new Set([
  'area',
  'base',
  'br',
  'col',
  'embed',
  'hr',
  'img',
  'input',
  'link',
  'meta',
  'param',
  'source',
  'track',
  'wbr'
]);

const ATTRIBUTE_MAPPING: { [key: string]: string } = {
  class: 'className',
  for: 'htmlFor',
  cellpadding: 'cellPadding',
  cellspacing: 'cellSpacing',
  colspan: 'colSpan',
  rowspan: 'rowSpan',
  tabindex: 'tabIndex',
  maxlength: 'maxLength',
  readonly: 'readOnly',
  contenteditable: 'contentEditable',
  crossorigin: 'crossOrigin',
  allowfullscreen: 'allowFullScreen',
  marginwidth: 'marginWidth',
  marginheight: 'marginHeight',
  frameborder: 'frameBorder',
  enctype: 'encType',
  autocomplete: 'autoComplete'
};

const convertGoogleDriveUrl = (url: string): string => {
  if (url.includes('drive.google.com/uc?')) {
    const id = url.match(/id=([^&]*)/)?.[1];
    if (id) {
      return `https://drive.google.com/file/d/${id}/preview`;
    }
  }
  return url;
};

const HtmlExternalComponent: React.FC<HtmlExternalComponentProps> = ({ html, className }) => {
  const cleanHtml = (dirtyHtml: string): string => {
    let cleaned = dirtyHtml
      .replace(/<html[^>]*>/gi, '')
      .replace(/<\/html>/gi, '')
      .replace(/<head[^>]*>.*?<\/head>/gi, '')
      .replace(/<body[^>]*>/gi, '')
      .replace(/<\/body>/gi, '')
      .replace(/<p>&nbsp;<\/p>/gi, '');

    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    return cleaned;
  };

  const parseStyle = (styleString: string): React.CSSProperties => {
    if (!styleString) return {};

    const styleObject: Record<string, string> = {};

    styleString.split(';').forEach((style) => {
      const [key, value] = style.split(':').map((str) => str.trim());
      if (key && value) {
        const camelKey = key.replace(/-([a-z])/g, (_, g) => g.toUpperCase());
        const processedValue = value.replace(/(\d+)/g, '$1');
        styleObject[camelKey] = processedValue;
      }
    });

    return styleObject as React.CSSProperties;
  };

  const convertAttribsToReact = (attribs: { [key: string]: string }) => {
    const newAttribs: { [key: string]: any } = {};
    Object.entries(attribs).forEach(([key, value]) => {
      if (key === 'style') {
        newAttribs.style = parseStyle(value);
      } else {
        const reactKey = ATTRIBUTE_MAPPING[key.toLowerCase()] || key;
        const finalKey = ATTRIBUTE_MAPPING[key.toLowerCase()]
          ? reactKey
          : reactKey.replace(/-([a-z])/g, (_, g) => g.toUpperCase());
        newAttribs[finalKey] = value;
      }
    });

    return newAttribs;
  };
  const options: HTMLReactParserOptions = {
    replace: (domNode) => {
      if (domNode instanceof Element) {
        const isVoidElement = VOID_ELEMENTS.has(domNode.name);

        if (['html', 'head', 'body'].includes(domNode.name)) {
          return <>{domToReact(domNode.children as DOMNode[], options)}</>;
        }

        if (domNode.name === 'a') {
          const { href } = domNode.attribs;
          const children = domToReact(domNode.children as DOMNode[], options);
          const linkStyle = domNode.attribs.style ? parseStyle(domNode.attribs.style) : {};

          if (DeviceUtils.isMobileApp()) {
            return (
              <button
                className='text-sm underline text-navy-blue tracking-1'
                style={linkStyle}
                onClick={() => {
                  const postData: PostMessageData = {
                    type: 'openUrlBrowser',
                    url: href,
                    data: {}
                  };
                  BrowserUtils.ActionReactNative(postData);
                }}>
                {children}
              </button>
            );
          }

          return (
            <Link
              className='text-navy-blue text-lg tracking-8 leading-8 underline'
              style={linkStyle}
              href={href}
              target='_blank'>
              {children}
            </Link>
          );
        }

        if (domNode.name === 'img') {
          const props = convertAttribsToReact(domNode.attribs);
          const imgStyle = props.style || {};

          if (props.src && props.src.includes('drive.google.com')) {
            const iframeSrc = convertGoogleDriveUrl(props.src);
            return (
              <iframe
                src={iframeSrc}
                style={{
                  border: 'none',
                  width: '100%',
                  height: 'auto',
                  ...imgStyle
                }}
                loading='lazy'
                allowFullScreen
              />
            );
          }

          return (
            <img
              {...props}
              style={{
                maxWidth: '100%',
                height: 'auto',
                ...imgStyle
              }}
              alt={props.alt || ''}
            />
          );
        }

        const props = convertAttribsToReact(domNode.attribs);

        if (isVoidElement) {
          return React.createElement(domNode.name, props);
        }

        return React.createElement(
          domNode.name,
          props,
          domToReact(domNode.children as DOMNode[], options)
        );
      }

      return undefined;
    }
  };

  return <div className={className}>{parse(cleanHtml(html), options)}</div>;
};

export default HtmlExternalComponent;
