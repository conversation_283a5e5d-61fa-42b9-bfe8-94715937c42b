/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { AnimatePresence, motion } from "motion/react"

import { useLoading } from '@/hooks/useLoading';
import SpinnerLoading from './SpinnerLoading';

const CustomLoading = () => {
  const { loading, textInfoLoad } = useLoading();
  if (loading !== true) return null;
  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key='loading'
        initial={{ opacity: loading ? 0 : 1 }}
        animate={{ opacity: loading ? 1 : 0 }}
        exit={{ opacity: loading ? 0 : 1 }}
        transition={{
          duration: 0.4,
          ease: [0.4, 0, 0.2, 1]
        }}
        style={{ pointerEvents: 'none' }}
        className='absolute z-[10000029229929292] w-full h-full flex flex-col justify-center items-center bg-[rgba(255,255,255,0.8)]'>
        {textInfoLoad !== '' ? (
          <p className='font-bold text-xl max-md:text-base text-white bg-navy-blue p-4 tracking-1 mt-10 max-md:mt-4 m-[20px] mb-2 text-center'>
            {textInfoLoad}
          </p>
        ) : null}
        <SpinnerLoading />
      </motion.div>
    </AnimatePresence>
  );
};

export default CustomLoading;
