/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';

interface SpinnerLoadingProps {
  width?: number;
  height?: number;
  className?: string;
}

const SpinnerLoading = ({ width = 100, height = 100, className = '' }: SpinnerLoadingProps) => {
  return (
    <Image
      src='/images/Spinner-1s-200px.svg'
      alt='Loading'
      width={width}
      height={height}
      className={className}
      priority
    />
  );
};

export default SpinnerLoading;
