/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { ChangeEventHandler } from 'react';
// import './styles.css';

interface OptionProps {
  label: string;
  value: string;
}
interface InputProps {
  label: string;
  name: string;
  defaultValue?: string;
  onChange?: ChangeEventHandler<HTMLSelectElement> | undefined;
  options?: OptionProps[];
}

const InputSelect = ({
  label,
  onChange,
  name,
  defaultValue = 'Selecione',
  options = [
    {
      label: '',
      value: ''
    }
  ]
}: InputProps) => {
  return (
    <>
      <label className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3'>{label}</label>

      <select
        onChange={onChange}
        name={name}
        className='rounded border-1 border-neutral-blue bg-white h-12 px-3 text-sm text-navy-blue tracking-1 w-full'>
        <option className='bg-white text-navy-blue hover:bg-gray-100' value=''>
          {defaultValue}
        </option>
        {options.map((option) => (
          <option
            className='bg-white text-navy-blue hover:bg-gray-100'
            key={option.value}
            value={option.label}>
            {option.value}
          </option>
        ))}
      </select>
    </>
  );
};

export default InputSelect;
