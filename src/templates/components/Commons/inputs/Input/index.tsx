/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { InputProps, TextAreaFieldProps } from '@/@types/Inputs';
import { useData } from '@/hooks/useData';
import InputMask from '@mona-health/react-input-mask';
import React, { ChangeEventHandler, forwardRef, Ref, useEffect, useState } from 'react';
import PasswordControls from './PasswordControls';

// Extracted TextArea component to reduce complexity
const TextAreaField: React.FC<TextAreaFieldProps> = ({
  name,
  rows,
  ref,
  onChange,
  required,
  disabled,
  disabledColor,
  enabledColor,
  ...rest
}) => (
  <textarea
    className={`rounded border-1 border-neutral-blue h-36 px-3 text-[16px] md:text-sm text-navy-blue tracking-1 w-full pt-2 pb-2 ${disabled ? disabledColor : enabledColor}`}
    name={name}
    rows={rows}
    ref={ref}
    onChange={onChange}
    required={required}
    disabled={disabled}
    {...rest}
  />
);

interface TextMaskFieldProps {
  mask: string | (string | RegExp)[];
  defaultValue?: string;
  type: string;
  showPass: boolean;
  name: string;
  onChange?: ChangeEventHandler<HTMLInputElement>;
  required?: boolean;
  disabled?: boolean;
  disabledColor: string;
  enabledColor: string;
  [key: string]: any;
}

// Extracted TextMask component to reduce complexity
const TextMaskField: React.FC<TextMaskFieldProps> = ({
  mask,
  defaultValue,
  type,
  showPass,
  name,
  onChange,
  required,
  disabled,
  disabledColor,
  enabledColor,
  ...rest
}) => (
  <InputMask
    mask={mask}
    defaultValue={defaultValue}
    className={`rounded border-1 border-neutral-blue h-12 px-3 text-[16px] md:text-sm text-navy-blue tracking-1 w-full ${disabled ? disabledColor : enabledColor}`}
    type={showPass ? 'text' : type}
    name={name}
    onChange={onChange}
    required={required}
    disabled={disabled}
    {...rest}
  />
);

interface StandardInputProps {
  type: string;
  showPass: boolean;
  name: string;
  disabled?: boolean;
  ref: Ref<HTMLInputElement>;
  onChange?: ChangeEventHandler<HTMLInputElement>;
  required?: boolean;
  disabledColor: string;
  enabledColor: string;
  [key: string]: any;
}

// Extracted StandardInput component to reduce complexity
const StandardInput: React.FC<StandardInputProps> = ({
  type,
  showPass,
  name,
  disabled,
  ref,
  onChange,
  required,
  disabledColor,
  enabledColor,
  showSavePass,
  ...rest
}) => (
  <input
    className={`rounded border-1 border-neutral-blue h-12 px-3 text-[16px] md:text-sm text-navy-blue tracking-1 w-full ${disabled ? disabledColor : enabledColor}`}
    type={showPass ? 'text' : type}
    name={name}
    disabled={disabled}
    ref={ref}
    onChange={onChange}
    required={required}
    {...rest}
  />
);

const Input = forwardRef<HTMLInputElement | HTMLTextAreaElement, InputProps>(
  (
    {
      label,
      type,
      name,
      disabled,
      errors,
      rows,
      onChange,
      className,
      defaultValue,
      value,
      showSavePass = false,
      required = false,
      mask,
      ...rest
    }: InputProps,
    ref
  ) => {
    // Password state management
    const [showPass, setShowPass] = useState<boolean>(false);
    const [savePass, setSavePass] = useState<boolean>(false);
    const { createData } = useData();

    // Handle password visibility toggle
    const handleShowPass = (): void => setShowPass((prev) => !prev);

    // Handle password saving
    function handleSavePass(): void {
      const value = !savePass;
      createData<boolean>({
        id: 'savePass',
        value,
        index: 'savePass',
        isSetCookie: false
      });
      setSavePass(value);
    }

    // Load saved password preference
    useEffect(() => {
      const savedPass = localStorage.getItem('savePass');
      if (savedPass === 'true') {
        setSavePass(true);
      }
    }, []);

    const classpassword = !savePass
      ? 'w-6 h-6 bg-white border-2 border-gray-400 rounded-md cursor-pointer flex items-center justify-center bg-navy-blue border-navy-blue transition-all duration-200 after:content-["✓"] after:text-white after:opacity-0'
      : 'w-6 h-6 bg-white border-2 border-gray-400 rounded-md cursor-pointer flex items-center justify-center !bg-navy-blue border-navy-blue transition-all duration-200 after:content-["✓"] after:text-white after:opacity-100';

    const disabledColor = 'bg-[#e1e3e9] text-[#868da9]';
    const enabledColor = 'bg-white';

    // Render the appropriate input based on type
    const renderInput = () => {
      if (type === 'textarea') {
        return (
          <TextAreaField
            name={name}
            rows={rows}
            ref={ref as Ref<HTMLTextAreaElement>}
            onChange={onChange as ChangeEventHandler<HTMLTextAreaElement>}
            required={required}
            disabled={disabled}
            disabledColor={disabledColor}
            enabledColor={enabledColor}
            {...rest}
          />
        );
      }

      if (type === 'textmask' && mask) {
        return (
          <TextMaskField
            mask={mask}

            type={type}
            showPass={showPass}
            name={name}
            disabled={disabled}
            ref={ref as Ref<HTMLTextAreaElement>}
            onChange={onChange as ChangeEventHandler<HTMLInputElement>}
            required={required}
            disabledColor={disabledColor}
            enabledColor={enabledColor}
            {...rest}
          />
        );
      }

      if (type === 'text' || type === 'email' || type === 'number' || type === 'password') {
        return (
          <StandardInput
            type={type}
            showPass={showPass}
            name={name}
            disabled={disabled}
            ref={ref as React.Ref<HTMLInputElement>}
            onChange={onChange as ChangeEventHandler<HTMLInputElement>}
            required={required}
            disabledColor={disabledColor}
            enabledColor={enabledColor}
            showSavePass={showSavePass}
            {...rest}
          />
        );
      }

      return null;
    };

    return (
      <div className={className} style={{ position: type === 'password' ? 'relative' : 'inherit' }}>
        {label && (
          <label className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3' htmlFor={name}>
            {label}
          </label>
        )}

        {renderInput()}

        {type === 'password' && (
          <PasswordControls
            showPass={showPass}
            handleShowPass={handleShowPass}
            savePass={savePass}
            handleSavePass={handleSavePass}
            classpassword={classpassword}
            showSavePass={showSavePass}
          />
        )}

        {errors && <p className='text-danger text-[#F00] text-[0.8rem]'>{errors}</p>}
      </div>
    );
  }
);

Input.displayName = 'Input';
export default Input;
