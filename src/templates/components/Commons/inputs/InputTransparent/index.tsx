/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

interface InputTransparentProps {
  label: string;
  type: string;
  defaultValue: string;
}
const InputTransparent = ({ label, type, defaultValue }: InputTransparentProps) => {
  return (
    <>
      <label className='text-sm text-[#62829A] tracking-widest flex mb-1.5 mt-3'>{label}</label>
      <input
        className='rounded border-1 border-[#62829A] bg-transparent h-12 px-3 text-sm text-navy-blue tracking-1 w-full'
        type={type}
        defaultValue={defaultValue}
      />
    </>
  );
};

export default InputTransparent;
