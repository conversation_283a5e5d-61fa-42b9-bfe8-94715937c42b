/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { ChangeEventHandler } from 'react';

interface TextareaProps {
  label: string;
  name: string;
  onChange?: ChangeEventHandler<HTMLTextAreaElement> | undefined;
  defaultValue?: string;
  maxLength?: number;
  disabled?: boolean;
  errors?: string;
}

const Textarea = ({
  label,
  onChange,
  name,
  defaultValue,
  maxLength = 100000000000,
  disabled = false,
  errors
}: TextareaProps) => {
  return (
    <>
      <label className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3' htmlFor={name}>
        {label}
      </label>
      <textarea
        onChange={onChange}
        rows={7}
        className='rounded border-1 pt-2 resize-none border-neutral-blue bg-white px-3 text-[16px] md:text-sm text-navy-blue tracking-1 w-full'
        id={name}
        name={name}
        autoComplete={name}
        defaultValue={defaultValue}
        value={disabled ? defaultValue : undefined}
        maxLength={maxLength}
        disabled={disabled}
      />
      {errors && <p className='text-danger text-[#F00] text-[0.8rem]'>{errors}</p>}
    </>
  );
};

export default Textarea;
