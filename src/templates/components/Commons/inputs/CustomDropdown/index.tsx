/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { IFormInputRequestService } from '@/templates/TalkToCury/hooks/useRequestService';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

export interface Option {
  id: string | number;
  value: string | number | boolean;
  name: string | number;
}

interface CustomDropdownProps {
  optionsExternal: Option[];
  onChange: (
    value: string | number | boolean,
    name?: string,
    field?: keyof IFormInputRequestService
  ) => void;
  defaultValue?: string;
  onClose?: () => void;
  label?: string | null;
  name?: string | null;
  disabled?: boolean | null;
}

export interface CustomDropdownHandles {
  openDropdown: () => void;
  closeDropdown: () => void;
  setNewOption: (newOptions: Option[]) => void;
}
const CustomDropdown = forwardRef<CustomDropdownHandles, CustomDropdownProps>(
  (
    {
      optionsExternal,
      onChange,
      defaultValue = 'Todos',
      onClose,
      label = null,
      disabled = false,
      name = null
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [options, setOptions] = useState<Option[]>(optionsExternal);
    const [selected, setSelected] = useState<string | number | null>(null);

    useEffect(() => {
      if (options) setSelected(options[0]?.name ? options[0]?.name : defaultValue);
    }, []);

    const toggleDropdown = () => {
      if (onClose) onClose();
      setIsOpen(!isOpen);
    };

    const handleSelect = (index: number) => {
      setSelected(options[index].name);
      onChange(options[index].value, name as string);
      setIsOpen(false);
      if (onClose) onClose();
    };
    useImperativeHandle(ref, () => ({
      setNewOption: (newOptions: Option[]) => {
        setOptions(newOptions);
        if (newOptions) {
          setSelected(newOptions[0]?.name ? newOptions[0]?.name : defaultValue);
        } else {
          setSelected(defaultValue);
        }
      },
      openDropdown: () => {
        setIsOpen(true);
      },
      closeDropdown: () => {
        setIsOpen(false);
      }
    }));
    return (
      <div className='relative'>
        {label && (
          <span className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3'>{label}</span>
        )}
        <button
          type='button'
          onClick={toggleDropdown}
          disabled={disabled as boolean}
          className='rounded border-1 flex justify-between items-center border-neutral-blue bg-white h-12 px-3 text-sm text-navy-blue tracking-1 w-full'>
          <span>{selected}</span>
          <span className='ml-2'>{isOpen ? '▲' : '▼'}</span>
        </button>

        {isOpen && options && (
          <div className='absolute w-full bg-white  shadow-md rounded-md z-[100] max-h-[300px] overflow-auto'>
            {options.map((option, index) => (
              <div key={option.id} className='w-full flex flex-col items-center hover:bg-gray-200'>
                <button
                  type='button'
                  disabled={disabled as boolean}
                  onClick={() => handleSelect(index)}
                  className='border-1 border-neutral-blue w-full text-left  py-2 pl-4 y-2  text-navy-blue hover:bg-gray-200 cursor-pointer h-full'>
                  {option.name}
                </button>
                {/* <hr className='opacity-70 w-[100%]'></hr> */}
              </div>
            ))}
          </div>
        )}
      </div>
    );
    // }
  }
);
CustomDropdown.displayName = 'CustomDropdown';
export default CustomDropdown;
