/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

/**
 * Componente que inicia a atualização de dados do usuário durante a montagem da aplicação
 */
import { useRefresh } from '@/hooks/useRefresh';
import { useEffect } from 'react';

const RefreshPage = () => {
  const { refreshDataStart } = useRefresh();

  useEffect(() => {
    let isMounted = true;

    const refreshData = async () => {
      try {
        if (isMounted) {
          await refreshDataStart();
        }
      } catch (error) {
        console.error('Falha ao atualizar dados:', error);
      }
    };

    refreshData();

    return () => {
      isMounted = false;
    };
  }, []);

  return null;
};

export default RefreshPage;
