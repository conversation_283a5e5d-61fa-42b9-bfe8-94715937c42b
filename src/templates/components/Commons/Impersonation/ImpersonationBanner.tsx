'use client';

import { useImpersonation } from '@/hooks/useImpersonation';

const ImpersonationBanner: React.FC = () => {
  const { isImpersonating, stopImpersonationForced } = useImpersonation();

  if (!isImpersonating) return null;

  return (
    <div className='bg-amber-500 text-white p-2 text-center z-[44444444444444444444444444]'>
      <p className='font-semibold'>
        Você está visualizando como cliente
        <button
          // onClick={stopImpersonationForced}
          onClick={stopImpersonationForced}
          className='ml-4 bg-white text-amber-800 px-2 py-1 rounded-md text-sm'>
          Voltar para Admin
        </button>
      </p>
    </div>
  );
};

export default ImpersonationBanner;
