/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';
import { MouseEventHandler } from 'react';
import CustomLink from './CustomLink';

interface ButtonSubMenuProps {
  src: string;
  text: string;
  route?: string | null;
  border?: string;
  onClick?: MouseEventHandler<HTMLButtonElement> | undefined;
  target?: string;
  permission?: boolean;
  disabled?: boolean;
}
interface ContentProps {
  src: string;
  text: string;
}
const Content = ({ src, text }: ContentProps) => {
  return (
    <>
      <div className='border-r-1 border-neutral-blue px-2 py-3 w-10 flex justify-center items-center'>
        <Image
          src={src}
          alt='Icon'
          className='max-md:hidden w-[auto] h-[auto]'
          width={22}
          height={22}
          priority
        />
      </div>
      <div className='flex items-center justify-between px-2 w-52'>
        <p className='text-sm text-neutral-blue tracking-1'>{text}</p>
        <Image
          src='/images/angle-right.svg'
          alt='Icon'
          className='max-md:hidden  w-auto h-auto'
          width={9}
          height={9}
          priority
        />
      </div>
    </>
  );
};
const ButtonSubMenu = ({
  src,
  text,
  route,
  border,
  onClick,
  permission = true,
  disabled
}: ButtonSubMenuProps) => {
  if (!permission) return null;
  return (
    <>
      {route ? (
        <CustomLink
          className={`bg-white flex items-center rounded ${border}-b-1 border-neutral-blue`}
          href={route}
          aria-disabled={disabled}>
          <Content src={src} text={text} />
        </CustomLink>
      ) : (
        <button
          className={`cursor-pointer bg-white flex items-center rounded ${border}-b-1 border-neutral-blue`}
          disabled={disabled}
          aria-disabled={disabled}
          onClick={onClick}>
          <Content src={src} text={text} />
        </button>
      )}
    </>
  );
};

export default ButtonSubMenu;
