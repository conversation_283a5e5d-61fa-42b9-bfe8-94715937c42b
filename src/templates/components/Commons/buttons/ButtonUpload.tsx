/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useModal } from '@/hooks';
import Image from 'next/image';
import React, { RefObject } from 'react';

interface ButtonUploadProps {
  src?: string;
  permission?: boolean;
  handleFileChange: ({ files }: { files: FileList }) => void;
  handleButtonClick: () => void;
  fileInputRef: RefObject<HTMLInputElement | null>;
  fileName?: string;
}

const ButtonUpload = ({
  src,
  fileInputRef,
  permission = true,
  handleFileChange,
  handleButtonClick,
  fileName
}: ButtonUploadProps) => {
  const { toggleModal } = useModal();
  if (!permission) return null;

  const handleFileChangeComp = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const file = event.target.files[0];
      const maxFileSize = 5 * 1024 * 1024;

      if (file.size > maxFileSize) {
        toggleModal({ text: 'O arquivo deve ser menor que 5MB!' });
        event.target.value = '';
      } else {
        handleFileChange({ files: event.target.files });
      }
    }
  };
  return (
    <>
      <label className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3' htmlFor={'file'}>
        Anexar arquivo:
      </label>
      <div
        className={'flex h-14 mb-2.5 bg-white w-full flex-row items-center rounded  bg-transparent mt-3 focus:outline-none'}>
        <input
          type='file'
          name='file'
          ref={fileInputRef}
          onChange={handleFileChangeComp}
          className='hidden'
          lang='pt-BR'
        />
        <div className='flex bg-neutral-blue  items-center justify-center rounded-tl rounded-bl rounded h-14 w-14 focus:outline-none'>
          <Image src={src as string} alt='Icon' width={25} height={25} priority />
        </div>
        <button
          onClick={handleButtonClick}
          className='flex px-3 text-sm tracking-1 w-full h-full items-center'
          type='button'>
          {fileName ? fileName : 'Enviar arquivo'}
        </button>
        <Image
          src='/images/angle-down.svg'
          alt='Icon'
          className='-rotate-90  object-contain w-auto h-auto mr-[12px]'
          width={12}
          height={8}
          priority
        />
      </div>
    </>
  );
};

export default ButtonUpload;
