/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useHacks } from '@/hooks/useHacks';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

export default function ButtonBack() {
  const pathname = usePathname();
  const { goBack } = useHacks();

  if (pathname === null || pathname === undefined) return null;
  const pathParts = pathname.split('/').filter(Boolean);
  if (pathParts[1] === 'home' || pathname === '/') return null;

  return (
    <>
      <div
        className='md:hidden absolute  opacity-20 w-[100vw] h-[100vh]  inset-0'
        style={{ pointerEvents: 'none' }}></div>
      <button
        onClick={goBack}
        className='flex items-center justify-center w-[40px] h-[40px]  -ml-4'>
        <Image
          src='/images/angle-down.svg'
          alt='Icon'
          className='rotate-90 object-contain w-auto h-auto filter brightness-[1000]'
          width={12}
          height={8}
          priority
        />
      </button>
    </>
  );
}
