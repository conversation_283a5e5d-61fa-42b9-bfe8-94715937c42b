/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';
import CustomLink from './CustomLink';

interface ButtonProps {
  text: string;
  src: string;
  href?: string;
  classCustom?: string;
}

const Button = ({ text, src, href = '', classCustom = '' }: ButtonProps) => {
  return (
    <CustomLink
      href={href}
      className={`flex md:w-36 md:h-36 mb-2.5 bg-neutral-blue flex-col justify-between items-center rounded relative max-md:h-23 max-md:w-23  focus:outline-none ${classCustom}`}>
      <div className='h-20 w-20 flex flex-col justify-center items-center max-md:h-20 max-md:w-20'>
        <Image
          src={src}
          alt='Icon'
          className='w-14 max-md:w-full max-md:max-w-[38px] max-md:mt-[12px] mt-5'
          width={55}
          height={55}
          quality={100}
        />
      </div>
      <p className='flex items-start max-md:items-center justify-center w-30 h-14 leading-5 text-center tracking-[0.08em] font-medium text-base text-white font-bold  max-md:text-8 max-md:font-[500] max-md:leading-[17px] max-md:h-15 max-md:w-20 max-md:tracking-[-0.02em] [-webkit-text-size-adjust:80%]'>
        {text}
      </p>
    </CustomLink>
  );
};

export default Button;
