/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import Image from 'next/image';

import { MouseEventHandler, useEffect, useMemo, useState } from 'react';
import CustomLink from './CustomLink';

interface ContentProps {
  text: string;
  src?: string;
  srcMobile: string;
  bg_icon?: string;
}
interface ButtonBlueProps {
  src?: string;
  srcMobile: string;
  text: string;
  route?: string | null;
  permission?: boolean;
  onClick?: MouseEventHandler<HTMLButtonElement> | undefined;
  target?: string;
  bg?: string;
  bg_icon?: string;
  w_custom?: string;
  h_custom?: string;
  border?: string;
}
const Content = ({ text, src, srcMobile, bg_icon = 'bg-neutral-blue' }: ContentProps) => {
  return (
    <>
      <div
        className={`w-12 h-full ${bg_icon} flex flex-row items-center justify-center rounded-tl rounded-bl max-md:rounded max-md:h-18 max-md:w-18 max-md:bg-white focus:outline-none`}>
        {src && (
          <Image src={src} alt='Icon' className='max-md:hidden' width={25} height={25} priority />
        )}
        <Image
          src={srcMobile}
          alt='Icon'
          className='hidden max-md:block max-md:w-10'
          width={30}
          height={30}
          priority
        />
      </div>
      <div className='flex flex-row justify-between items-center px-2 w-full max-md:justify-center max-md:w-16 max-md:p-0'>
        <p className='tracking-1 font-medium text-center md:text-left text-sm text-navy-blue max-md:text-12 max-md:tracking-normal max-md:leading-4 max-md:mt-2'>
          {text}
        </p>
        <Image
          src='/images/angle-down.svg'
          alt='Icon'
          className='-rotate-90 max-md:hidden object-contain w-auto h-auto mt-[-6px]'
          width={12}
          height={8}
          priority
        />
      </div>
    </>
  );
};
const ButtonSidebar = ({
  text,
  src,
  srcMobile,
  route = null,
  permission = true,
  onClick,
  target = '_self',
  bg = 'bg-white',
  bg_icon,
  w_custom = 'w-11/12',
  h_custom = 'h-12',
  border
}: ButtonBlueProps) => {
  const [component, setComponent] = useState<React.ReactNode>(null);

  const classButtons = useMemo(
    () =>
      `flex cursor-pointer ${h_custom}  ${bg} ${w_custom} flex-row items-center rounded max-md:flex-col max-md:h-[132px] max-md:w-full  max-md:bg-transparent md:mb-3 focus:outline-none text-center md:text-left ${border} `,
    [h_custom, bg, w_custom, border]
  );

  useEffect(() => {
    setComponent(
      route ? (
        <CustomLink className={classButtons} target={target} href={route}>
          <Content text={text} src={src} srcMobile={srcMobile} bg_icon={bg_icon} />
        </CustomLink>
      ) : (
        <button type='button' className={classButtons} onClick={onClick}>
          <Content text={text} src={src} srcMobile={srcMobile} bg_icon={bg_icon} />
        </button>
      )
    );
  }, [route]);

  if (!permission) return null;

  return <>{component}</>;
};

export default ButtonSidebar;
