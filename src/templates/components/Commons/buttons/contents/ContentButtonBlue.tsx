/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { MouseEventHandler } from 'react';

interface ButtonBlueProps {
  text: string;
  background: string;
  color: string;
  type?: 'button' | 'submit' | 'reset' | undefined;
  disabled?: boolean;
  route?: string | null;
  onClick?: MouseEventHandler<HTMLButtonElement>;
  target?: string;
  classExtra?: string;
}

const ContentButtonBlue = ({
  text,
  onClick,
  color,
  type,
  disabled,
  background,
  classExtra
}: ButtonBlueProps) => {
  return (
    <button
      className={`cursor-pointer flex justify-center h-12  bg-${background} flex-row items-center rounded w-full max-w-[300px] max-md:w-full min-w-[160px] disabled:opacity-50 ${classExtra}`}
      type={type}
      disabled={disabled}
      onClick={onClick}>
      <p className={`font-medium text-sm px-2 text-${color}`}>{text}</p>
    </button>
  );
};

export default ContentButtonBlue;
