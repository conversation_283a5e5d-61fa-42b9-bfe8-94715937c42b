/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { MouseEventHandler } from 'react';
import ContentButtonBlue from './contents/ContentButtonBlue';
import CustomLink from './CustomLink';

// import ContentButtonBlue from './contents/ContentButtonBlue';

interface ButtonBlueProps {
  text: string;
  background: string;
  color: string;
  type?: 'button' | 'submit' | 'reset' | undefined;
  disabled?: boolean;
  route?: string | null;
  onClick?: MouseEventHandler<HTMLButtonElement>;
  target?: string;
  classExtra?: string;
}

export const Buttonblue = ({
  text,
  route,
  onClick,
  color,
  disabled,
  background,
  type,
  target = '_self',
  classExtra = ''
}: ButtonBlueProps) => {
  return route ? (
    <CustomLink
      href={route}
      target={target}
      className={`cursor-pointer flex justify-center h-12 mb-2.5 mt-8 bg-${background} flex-row items-center rounded w-full max-md:w-full  disabled:opacity-50 ${classExtra}`}>
      <ContentButtonBlue
        text={text}
        color={color}
        type={type}
        disabled={disabled}
        background={background}
        classExtra={classExtra}
      />
    </CustomLink>
  ) : (
    <ContentButtonBlue
      text={text}
      onClick={onClick}
      color={color}
      type={type}
      disabled={disabled}
      background={background}
      classExtra={` mb-2.5 mt-8 ${classExtra}`}
    />
  );
};
