/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { ReactNode, memo, useCallback } from 'react';

interface Props {
  href: string;
  children: ReactNode;
  className?: string;
  target?: string;
}

const CustomLink = memo(({ href, children, className, target, ...props }: Props) => {
  const router = useRouter();
  // const { setLoadingInfo } = useLoading();
  const pathname = usePathname();

  // Memoize the click handler to prevent recreation on every render
  const handleClick = useCallback(
    (e: React.MouseEvent<HTMLAnchorElement>) => {
      e.preventDefault();

      if (pathname === href) return null;

      if (e.currentTarget.target === '_blank') {
        window.open(href, '_blank');
      } else {
        // setLoadingInfo(true);
        router.push(href);
      }

      return true;
    },
    [href, pathname, router]
  );

  return (
    <Link
      prefetch={false}
      href={href}
      className={className}
      {...props}
      target={target}
      onClick={handleClick}>
      {children}
    </Link>
  );
});

CustomLink.displayName = 'CustomLink';

export default CustomLink;
