/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { MouseEventHandler } from 'react';
import CustomLink from './CustomLink';

interface ButtonImageProps {
  text: string;
  color: string;
  route?: string;
  target?: string;
  classExtra?: string;
  onClick?: MouseEventHandler<HTMLButtonElement>;
}

const Content = ({ text, onClick, color, classExtra = '' }: ButtonImageProps) => {
  return (
    <button
      type='button'
      className={`cursor-pointer flex justify-center min-w-[160px]  h-12 mb-2.5 mt-8 bg-transparent border-1 border-solid border-navy-blue flex-row items-center rounded w-full  max-w-[300px]  ${classExtra}`}
      onClick={onClick}>
      <p className={`font-medium text-sm text-${color}`}>{text}</p>
    </button>
  );
};

const Buttontransparent = ({
  text,
  route,
  onClick,
  color,
  target = '_self',
  classExtra
}: ButtonImageProps) => {
  return route ? (
    <CustomLink href={route} target={target} className={`w-auto ${classExtra}`}>
      <Content text={text} color={color} />
    </CustomLink>
  ) : (
    <Content text={text} onClick={onClick} color={color} classExtra={classExtra} />
  );
};

export default Buttontransparent;
