/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { MouseEventHandler } from 'react';

interface ButtonImageProps {
  name: string;
  // description: string;
  week: string;
  day: string;
  route?: MouseEventHandler<HTMLButtonElement>;
}
const ButtonImage = ({
  name,
  // description,
  week,
  day,
  route
}: ButtonImageProps) => {
  return (
    <button
      className='flex mt-2 mb-1 bg-light-gray max-md:bg-white w-full flex-row items-center rounded focus:outline-none min-h-[75px]'
      onClick={route}>
      <div className='w-32 h-full bg-neutral-blue flex flex-col items-center justify-center rounded-tl rounded-bl focus:outline-none'>
        <p className='uppercase text-[18px] tracking-wider text-white font-normal'>{week}</p>
        <p className='uppercase text-[35px] tracking-wider text-white font-light leading-5'>
          {day}
        </p>
      </div>
      <div className='flex flex-row justify-between p-2.5 w-full'>
        <div className='flex flex-col items-start'>
          <p className='tracking-1 font-medium text-base text-navy-blue text-left leading-4'>
            {name}
          </p>
          {/* {description ? (
            <p className='tracking-1 font-medium text-xs text-navy-blue text-left mt-3'>
              {description}
            </p>
          ) : null} */}
        </div>
      </div>
    </button>
  );
};

export default ButtonImage;
