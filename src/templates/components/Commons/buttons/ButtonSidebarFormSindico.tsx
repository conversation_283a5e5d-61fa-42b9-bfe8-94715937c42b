/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { MiscUtils } from '@/utils/MiscUtils';
import Image from 'next/image';

interface ContentProps {
  text: string;
  src?: string;
  srcMobile: string;
}
interface ButtonSidebarFormSindicoProps {
  src?: string;
  srcMobile: string;
  text: string;
  url: string;
  urlMobile: string;
}
const Content = ({ text, src, srcMobile }: ContentProps) => {
  return (
    <>
      <div className='w-12 h-full bg-neutral-blue flex flex-row items-center justify-center rounded-tl rounded-bl max-md:rounded max-md:h-18 max-md:w-18 max-md:bg-white focus:outline-none'>
        {src && (
          <Image src={src} alt='Icon' className='max-md:hidden' width={25} height={25} priority />
        )}
        <Image
          src={srcMobile}
          alt='Icon'
          className='hidden max-md:block max-md:w-9'
          width={30}
          height={30}
          priority
        />
      </div>
      <div className='flex flex-row justify-between p-2.5 w-full max-md:justify-center max-md:w-16 max-md:p-0'>
        <p className='tracking-1 font-medium text-center md:text-left text-sm text-navy-blue max-md:text-10 max-md:tracking-normal max-md:leading-3 max-md:mt-1'>
          {text}
        </p>
        <Image
          src='/images/angle-down.svg'
          alt='Icon'
          className='-rotate-90 max-md:hidden object-contain w-auto h-auto'
          width={12}
          height={8}
          priority
        />
      </div>
    </>
  );
};
const ButtonSidebarFormSindico = ({
  text,
  src,
  srcMobile,
  url,
  urlMobile
}: ButtonSidebarFormSindicoProps) => {
  const { content } = useAppContext();

  if (content === null || content === undefined) return null;
  const codEmpreendimento = content.Empreendimento.CodigoSienge__c;
  const inputValueCod = MiscUtils.calculateCode(codEmpreendimento);

  function openUrlBrowser() {
    BrowserUtils.ActionReactNative({
      type: 'openUrlBrowser',
      url: urlMobile
    });
  }
  return (
    <form
      name='form1'
      method='post'
      action={url}
      target='_blank'
      className='flex cursor-pointer h-12  bg-white w-11/12 flex-row items-center rounded max-md:flex-col max-md:h-[132px] max-md:w-full  max-md:bg-transparent md:mb-3 focus:outline-none text-center md:text-left'>
      <input type='hidden' name='num_emp' value={codEmpreendimento} />
      <input type='hidden' name='idemp' value={codEmpreendimento} />
      <input type='hidden' name='tipo' value='S' />
      <input type='hidden' name='cod' value={inputValueCod} />

      <button
        className={'flex cursor-pointer h-12  bg-white w-full flex-row items-center rounded max-md:flex-col max-md:h-full max-md:bg-transparent max-md:mt-0 focus:outline-none'}
        type={DeviceUtils.isMobileApp() ? 'button' : 'submit'}
        onClick={DeviceUtils.isMobileApp() ? openUrlBrowser : () => {}}>
        <Content text={text} src={src} srcMobile={srcMobile} />
      </button>
    </form>
  );
};

export default ButtonSidebarFormSindico;
