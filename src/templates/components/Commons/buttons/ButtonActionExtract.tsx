/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import Image from 'next/image';

import { MouseEventHandler } from 'react';
import CustomLink from './CustomLink';

interface ContentProps {
  text: string;
  src?: string;
}
interface ButtonActionExtractProps {
  src?: string;
  text: string;
  route?: string | null;
  permission?: boolean;
  onClick?: MouseEventHandler<HTMLButtonElement> | undefined;
  target?: string;
}
const Content = ({ text, src = '' }: ContentProps) => {
  return (
    <>
      <div
        className={'w-12 h-full bg-transparent flex flex-row items-center justify-center rounded-tl rounded-bl max-md:rounded max-md:h-18   focus:outline-none'}>
        <Image src={src} alt='Icon' className='max-md:w-9' width={30} height={30} priority />
      </div>
      <div className='flex flex-row justify-between p-2.5 w-full '>
        <p className='ml-4 font-medium text-left  text-navy-blue tracking-normal max-md:leading-3 max-md:mt-1'>
          {text}
        </p>
        <Image
          src='/images/angle-down.svg'
          alt='Icon'
          className='-rotate-90 object-contain w-auto h-auto'
          width={12}
          height={8}
          priority
        />
      </div>
    </>
  );
};
const ButtonActionExtract = ({
  text,
  src,
  route,
  permission = true,
  onClick,
  target = '_self'
}: ButtonActionExtractProps) => {
  if (!permission) return null;
  return (
    <>
      {route ? (
        <CustomLink
          className={'flex cursor-pointer h-18  bg-transparent w-full flex-row items-center rounded    max-md:bg-transparent md:mb-3 focus:outline-none md:text-left border-b-white border-b-1 '}
          target={target}
          href={route}>
          <Content text={text} src={src} />
        </CustomLink>
      ) : (
        <button
          className={'flex cursor-pointer h-18  bg-transparent w-full flex-row items-center rounded  max-md:bg-transparent md:mb-3 focus:outline-none border-b-white border-b-1 '}
          onClick={onClick}>
          <Content text={text} src={src} />
        </button>
      )}
    </>
  );
};

export default ButtonActionExtract;
