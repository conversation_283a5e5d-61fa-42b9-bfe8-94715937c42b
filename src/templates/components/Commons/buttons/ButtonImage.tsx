/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';

import { MouseEventHandler } from 'react';
import CustomLink from './CustomLink';

interface ButtonImageProps {
  text: string;
  src: string;
  route?: string;
  onClick?: MouseEventHandler<HTMLButtonElement>;
  permission?: boolean;
}

const Content = ({ src, text, onClick, permission = true }: ButtonImageProps) => {
  if (!permission) return null;
  return (
    <button
      className='cursor-pointer flex h-13 mb-2.5 bg-white w-full flex-row items-center rounded focus:outline-none'
      onClick={onClick}>
      <div className='w-12 min-w-12 h-full bg-neutral-blue flex flex-row items-center justify-center rounded-tl rounded-bl focus:outline-none'>
        <Image
          src={src}
          alt='Icon'
          width={28}
          height={28}
          priority
          className='w-auto h-auto max-w-[28px] min-w-[28px]'
        />
      </div>
      <div className='flex flex-row justify-between p-2.5 w-full'>
        <p className='tracking-wide font-medium text-sm text-navy-blue text-left limited-lines'>
          {text}
        </p>
      </div>
    </button>
  );
};

const ButtonImage = ({ text, src, route, onClick, permission = true }: ButtonImageProps) => {
  if (!permission) return null;
  return route ? (
    <CustomLink href={route}>
      <Content text={text} src={src} />
    </CustomLink>
  ) : (
    <Content text={text} src={src} onClick={onClick} />
  );
};

export default ButtonImage;
