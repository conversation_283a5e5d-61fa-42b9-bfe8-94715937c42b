/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { useMediaQuery } from '@/hooks';
import {
  ContactWhatsapp,
  FooterInformations,
  IamNeighbor,
  RelationshipCenter,
  SocialMedia,
  WantToBuyProperty
} from './contents';

type FooterDeslogadoProps = {
  openModalCookieModal: () => void;
};

const FooterDeslogado = ({ openModalCookieModal }: FooterDeslogadoProps) => {
  const { windowWidth } = useMediaQuery();

  return (
    <footer className='w-full  flex flex-col justify-center mt-10 relative z-100  b-0'>
      <div className='w-full bg-white p-4 flex'>
        <IamNeighbor directionIcon={'row'} />
        {windowWidth >= 1025 && <WantToBuyProperty directionIcon={'row'} />}

        {windowWidth >= 501 && (
          <RelationshipCenter showNumbersStates={false} directionIcon={'row'} />
        )}
        {windowWidth >= 769 && <SocialMedia directionIcon={'row'} />}

        <ContactWhatsapp />
      </div>
      <div className='w-full bg-[##e6e7ea] md:bg-white p-4 flex flex-col items-center'>
        <FooterInformations openModalCookieModal={openModalCookieModal} />
      </div>
    </footer>
  );
};
FooterDeslogado.displayName = 'FooterDeslogado';
export default FooterDeslogado;
