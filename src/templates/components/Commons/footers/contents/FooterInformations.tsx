/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Link from 'next/link';

type FooterInformationsProps = {
  openModalCookieModal: () => void;
};

const FooterInformations = ({ openModalCookieModal }: Readonly<FooterInformationsProps>) => {
  return (
    <>
      <p className='text-navy-blue text-[11px] md:xs:text-sm text-center tracking-wide mb-2 md:w-[70%] md:max-w-[600px]'>
        Central de Relacionamento: SP (11) 3117-1300, RJ (21) 3543- 6887 - somente
        fixo interior e litoral de SP, de segunda-feira a sexta-feira das 09h às 16h.
      </p>
      <p className='text-navy-blue text-[10px] md:xs:text-sm text-center tracking-wide'>
        <span>Confira nossa</span>{' '}
        <Link
          href='https://cury.net/politica-de-privacidade'
          target='_blank'
          rel='noopener noreferrer'>
          Política de Privacidade
        </Link>{' '}
        <span>e</span> <button onClick={() => openModalCookieModal()}>Política de Cookies</button>
      </p>
    </>
  );
};

FooterInformations.displayName = 'FooterInformations';

export { FooterInformations };
