/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';
import CustomLink from '../../buttons/CustomLink';

interface IamNeighborProps {
  directionIcon?: string;
}
const IamNeighbor = ({ directionIcon = 'col' }: IamNeighborProps) => {
  const classFinal = `flex justify-center items-center w-11/12 ${directionIcon === 'col' ? 'flex-col mb-3' : 'flex-row'}`;
  return (
    <CustomLink href='/sou-vizinho' className={classFinal}>
      <Image
        src='/images/ico-sou-vizinho.png'
        alt='Icon'
        className='mb-1 w-auto h-auto'
        width={40}
        height={40}
        priority
      />
      <div className='ml-2 text-center'>
        <small className='tracking-1 text-navy-blue flex text-sm'>Sou Vizinho</small>

        <small className='tracking-1 text-navy-blue flex text-sm'>de uma obra</small>
      </div>
    </CustomLink>
  );
};

IamNeighbor.displayName = 'IamNeighbor ';
export { IamNeighbor };
