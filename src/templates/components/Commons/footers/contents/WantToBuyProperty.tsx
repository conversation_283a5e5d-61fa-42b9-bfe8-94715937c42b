/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';
import Link from 'next/link';

interface WantToBuyPropertyProps {
  directionIcon?: string;
}
const WantToBuyProperty = ({ directionIcon = 'col' }: WantToBuyPropertyProps) => {
  const classFinal = `flex justify-center items-center w-11/12 ${directionIcon === 'col' ? 'flex-col mb-3' : 'flex-row'}`;
  return (
    <Link href='https://cury.net' target='_blank' className={classFinal}>
      <Image
        src='/images/ico-quero-comprar-imovel.png'
        alt='Icon'
        className='max-md:hidden mb-1  w-auto h-auto'
        width={40}
        height={40}
        priority
      />
      <div className='ml-2 text-center'>
        <small className='tracking-1 text-navy-blue flex text-sm'>Quero comprar</small>

        <small className='tracking-1 text-navy-blue flex text-sm'>um imóvel</small>
      </div>
    </Link>
  );
};
WantToBuyProperty.displayName = 'WantToBuyProperty';

export { WantToBuyProperty };
