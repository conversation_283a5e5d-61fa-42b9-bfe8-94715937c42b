/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';

interface RelationshipCenterProps {
  showNumbersStates?: boolean;
  directionIcon?: string;
}
const RelationshipCenter = ({
  showNumbersStates = true,
  directionIcon = 'col'
}: RelationshipCenterProps) => {
  const classFinal = `flex justify-center items-center w-11/12 ${directionIcon === 'col' ? 'flex-col mb-3' : 'flex-row'}`;
  return (
    <div className={classFinal}>
      <Image
        src='/images/central.png'
        alt='Icon'
        className='max-md:hidden mb-1'
        width={40}
        height={40}
        priority
      />

      {showNumbersStates && (
        <>
          <div className='ml-2 text-center mt-3'>
            <small className='tracking-1 text-navy-blue flex justify-center text-xs'>
              São Paulo
            </small>
            <a href='tel:1131171300'>
              <p className='tracking-1 text-navy-blue font-bold text-lg leading-4'>(11) 3117-1300</p>
            </a>
          </div>
          <div className='ml-2 text-center mt-3'>
            <small className='tracking-1 text-navy-blue flex justify-center text-xs'>
              Rio de Janeiro
            </small>
            <a href='tel:2135436887'>
              <p className='tracking-1 text-navy-blue font-bold text-lg leading-4'>(21) 3543-6887</p>
            </a>
          </div>
        </>
      )}
    </div>
  );
};
RelationshipCenter.displayName = 'RelationshipCenter';
export { RelationshipCenter };
