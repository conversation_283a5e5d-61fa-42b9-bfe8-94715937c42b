/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { WHATS_NUMBER } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { BrowserUtils } from '@/utils/BrowserUtils';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

const ContactWhatsapp = () => {
  // const [isMobile, setIsMobile] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const { isMobile } = useAppContext();
  useEffect(() => {
    setIsClient(true);
    // setIsMobile(DeviceUtils.isMobileApp() === true);
  }, []);

  const classFinal = 'flex justify-center items-center w-11/12 flex-row';

  if (!isClient) return null;

  return (
    <div className={classFinal}>
      {isMobile ? (
        <button
          className='flex flex-row'
          onClick={() =>
            BrowserUtils.ActionReactNative({
              type: 'openUrlBrowser',
              url: WHATS_NUMBER,
              data: {}
            })
          }>
          <Image
            src='/images/whats-2.svg'
            alt='Icon'
            className='max-md:w-8'
            width={25}
            height={25}
            priority
          />
          <div className='ml-2 text-center'>
            <small className='tracking-1 text-navy-blue flex text-[12px] md:xs:text-sm '>
              Contate-nos
            </small>
            <small className='tracking-1 text-navy-blue flex text-[12px] md:xs:text-sm '>
              pelo WhatsApp
            </small>
          </div>
        </button>
      ) : (
        <>
          <Link href={WHATS_NUMBER} target='_blank'>
            <Image
              src='/images/whats-2.svg'
              alt='Icon'
              className='max-md:w-8'
              width={25}
              height={25}
              priority
            />
          </Link>
          <Link href={WHATS_NUMBER} target='_blank'>
            <div className='ml-2 text-center'>
              <small className='tracking-1 text-navy-blue flex text-[12px] md:xs:text-sm '>
                Contate-nos
              </small>
              <small className='tracking-1 text-navy-blue flex text-[12px] md:xs:text-sm '>
                pelo WhatsApp
              </small>
            </div>
          </Link>
        </>
      )}
    </div>
  );
};

ContactWhatsapp.displayName = 'ContactWhatsapp';
export { ContactWhatsapp };
