/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';
import Link from 'next/link';

interface SocialMediaProps {
  directionIcon?: string;
}
const SocialMedia = ({ directionIcon = 'col' }: SocialMediaProps) => {
  const classFinal = `flex justify-center items-center w-11/12 ${directionIcon === 'col' ? 'flex-col mb-3' : 'flex-row'}`;
  return (
    <div className={classFinal}>
      <div className='ml-2 text-left '>
        <p className='tracking-1 leading-tight text-navy-blue text-left text-xs'>
          Siga-nos em<br></br>nossas redes sociais:
        </p>
      </div>
      <div className='flex'>
        <Link href='https://www.facebook.com/CuryConstrutora?fref=ts' target='_blank'>
          <Image
            src='/images/ico-facebook.svg'
            alt='Icon'
            className='mr-2 ml-1 md:ml-2'
            width={36}
            height={36}
            priority
          />
        </Link>
        <Link href='https://www.instagram.com/curyconstrutora/' target='_blank'>
          <Image
            src='/images/ico-instagram.svg'
            alt='Icon'
            className='mr-2'
            width={36}
            height={36}
            priority
          />
        </Link>
        <Link href='https://x.com/curyconstrutora' target='_blank'>
          <Image
            src='/images/ico-twitter.svg'
            alt='Icon'
            className='mr-2'
            width={36}
            height={36}
            priority
          />
        </Link>
      </div>
    </div>
  );
};
SocialMedia.displayName = 'SocialMedia';
export { SocialMedia };
