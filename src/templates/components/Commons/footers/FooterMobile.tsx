/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import Image from 'next/image';

import { useCallback, useLayoutEffect, useState } from 'react';

import { WHATS_NUMBER } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { useMessage } from '@/templates/Messages/hooks/useMessage';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import CustomLink from '../buttons/CustomLink';

const FooterMobile = () => {
  const { user, avatar } = useAppContext();
  const { getCountMessagesUnread } = useMessage();
  const [total, setTotal] = useState(0);

  const getTotalMessage = useCallback(async () => {
    setTotal(await getCountMessagesUnread(user?.AccountId as string));
  }, []);

  useLayoutEffect(() => {
    getTotalMessage()
      .then(() => {})
      .catch((error) => {
        console.error('Failed to get total messages:', error);
      });
  }, []);

  return (
    <div className='w-full bg-white p-3 flex flex-row justify-around mt-10 drop-shadow-blue fixed bottom-0 z-10 pb-6 md:pb-0'>
      <CustomLink href={`/${user?.AccountId}/home`}>
        <Image
          // src='/images/home.png'
          src='/images/home.avif'
          alt='Icon'
          className='max-md:w-8'
          width={25}
          height={25}
          priority
        />
      </CustomLink>
      <div className='relative'>
        <CustomLink href={`/${user?.AccountId}/mensagens`}>
          <Image
            src='/images/email-2.svg'
            alt='E-mail'
            className='max-md:w-8'
            width={36}
            height={36}
            priority
          />
          {total > 0 && (
            <div className='absolute top-0.5 right-0 w-2.5 h-2.5 bg-salmon rounded-full'></div>
          )}
        </CustomLink>
      </div>
      <div className='relative aspect-square overflow-hidden rounded-full flex justify-center items-center'>
        {DeviceUtils.isMobileApp() ? (
          <button
            onClick={() =>
              BrowserUtils.ActionReactNative({
                type: 'openUrlBrowser',
                url: WHATS_NUMBER,
                data: {}
              })
            }>
            <Image
              src='/images/whats-2.svg'
              alt='Icon'
              className='max-md:w-8'
              width={25}
              height={25}
              priority
            />
          </button>
        ) : (
          <CustomLink href={WHATS_NUMBER} target='_blank'>
            <Image
              src='/images/whats-2.svg'
              alt='Icon'
              className='max-md:w-8'
              width={25}
              height={25}
              priority
            />
          </CustomLink>
        )}
      </div>
      <div className='relative aspect-square overflow-hidden rounded-full flex justify-center items-center'>
        <CustomLink href={`/${user?.AccountId}/meus-dados`}>
          <Image
            src={avatar}
            alt='Icon'
            className='max-md:w-8 rounded-full '
            width={36}
            height={36}
            priority
          />
        </CustomLink>
      </div>
    </div>
  );
};

export default FooterMobile;
