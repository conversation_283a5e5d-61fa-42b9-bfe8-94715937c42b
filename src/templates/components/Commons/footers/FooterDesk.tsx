/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { FooterInformations } from './contents';

interface FooterProps {
  openModalCookieModal: () => void;
}

const FooterDesk = ({ openModalCookieModal }: FooterProps) => {
  return (
    <>
      <footer className='w-full  flex flex-col justify-center mt-2 relative z-[1]'>
        <div className='w-full bg-white p-4 flex flex-col items-center justify-center'>
          <FooterInformations openModalCookieModal={openModalCookieModal} />
        </div>
      </footer>
    </>
  );
};

export default FooterDesk;
