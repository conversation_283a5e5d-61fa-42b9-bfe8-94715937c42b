/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { useAppContext } from '@/contexts/AppContext';

import FooterDesk from '@/templates/components/Commons/footers/FooterDesk';
import FooterDeslogado from '@/templates/components/Commons/footers/FooterDeslogado';
import FooterMobile from '@/templates/components/Commons/footers/FooterMobile';
import { Show } from '@/utils/components/Show';

const Footer = () => {
  const { user, setModalCookieVisible, isMobile } = useAppContext();
  return (
    <div className='w-full h-full flex flex-col justify-end shrink-0 '>
      {user ? (
        <>
          <Show when={isMobile}>
            <FooterMobile />
          </Show>
          <Show when={!isMobile}>
            <FooterDesk openModalCookieModal={() => setModalCookieVisible(true)} />
          </Show>
        </>
      ) : (
        <FooterDeslogado openModalCookieModal={() => setModalCookieVisible(true)} />
      )}
    </div>
  );
};

export default Footer;
