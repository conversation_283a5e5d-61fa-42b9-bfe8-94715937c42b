/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { useModal } from '@/contexts/useModal';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { AnimatePresence, motion } from "motion/react"
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { Buttonblue } from '../buttons/Buttonblue';
import SpinnerLoading from '../Loading/SpinnerLoading';

// Component for the Progress Bar
const ProgressBar = ({ progress }: { progress: string }) => (
  <div className='w-[80%] h-2 bg-blue-secondary mt-4'>
    <div className='h-2 bg-navy-blue' style={{ width: `${progress}%` }}></div>
  </div>
);

// Component for the Definitive Password button and countdown
const DefinitivePasswordSection = ({
  time,
  redirectToDefinitivePassword,
  classeText
}: {
  time: number;
  redirectToDefinitivePassword: () => void;
  classeText: string;
}) => (
  <>
    <div className='w-[250px] md:w-[300px]'>
      <Buttonblue
        background='navy-blue'
        color='white'
        text='Clique aqui e crie senha definitiva.'
        onClick={redirectToDefinitivePassword}
      />
    </div>
    <p className={`${classeText} mt-8`}>Você será redirecionado em {time} segundos...</p>
  </>
);

// Close button component
const CloseButton = ({ onClick }: { onClick: () => void }) => (
  <div
    onClick={onClick}
    style={{
      position: 'absolute',
      top: '5px',
      right: '10px',
      background: 'none',
      border: 'none',
      cursor: 'pointer',
      fontSize: '14px'
    }}>
    X
  </div>
);

const Modal = () => {
  const {
    openModal,
    toggleModal,
    textInfoModal,
    params = { noClose: true, showButtonDefinitivePassword: false }
  } = useModal();
  const router = useRouter();
  const [time, setTime] = useState(10);
  const [progress, setProgress] = useState('0');

  // Memoize the redirect function to avoid recreating it on each render
  const redirectToDefinitivePassword = useCallback(() => {
    router.replace('/senha-temporaria');
  }, [router]);

  // Handle the countdown and redirect timer
  // useEffect(() => {
  //   if (!openModal || !params?.showButtonDefinitivePassword) return;

  //   const timeToRedirect = setInterval(() => {
  //     // Update progress if provided
  //     if (params?.progress) {
  //       const p = params.progress.toString();
  //       setProgress(p);
  //     }

  //     // Decrement the timer
  //     setTime((prevTime) => (prevTime <= 1 ? 0 : prevTime - 1));

  //     // Redirect when timer reaches 0
  //     if (time <= 1) {
  //       clearInterval(timeToRedirect);
  //       redirectToDefinitivePassword();
  //     }
  //   }, 1000);

  //   // Cleanup interval on component unmount or when dependencies change
  //   return () => clearInterval(timeToRedirect);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [params]);

  useEffect(() => {
    let timeToRedirect: ReturnType<typeof setInterval>;
    if (params && Object.keys(params).length > 0 && params.showButtonDefinitivePassword) {
      let time = 10;
      timeToRedirect = setInterval(() => {
        if (params?.progress) {
          const p = params && params?.progress ? params?.progress.toString() : '0';
          setProgress(p);
        }
        setTime((prevTime) => {
          if (prevTime <= 1) {
            return 0;
          }
          return prevTime - 1;
        });
        time -= 1;
        if (time === 0) {
          clearInterval(timeToRedirect);
          router.replace('/senha-temporaria');
        }
      }, 1000);
    }
    return () => clearInterval(timeToRedirect);
  }, [params]);

  // Early return if modal is not open
  if (!openModal) return null;

  // Handle body scroll
  BrowserUtils.handleBodyScroll(false);

  // Determine text styling based on params
  // if (params?.style) {
  const style = params?.style as { fontSize?: number; fontColor?: string };
  // fontSize = style?.fontSize ? style?.fontSize : 26;
  // fontColor = style?.fontColor ? style?.fontColor : 'navy-blue';
  // }
  // const style = params?.style || {};
  const fontSize = style ? style.fontSize : 26;
  const fontColor = style ? style.fontColor : 'navy-blue';
  const classeText = `text-center w-full text-[${fontSize}px] text-${fontColor}`;

  // Determine if close button should be shown
  const showCloseButton = !params.noClose || Object.keys(params).length === 0 || params.noClose;

  // Handle modal close
  const onClose = () => {
    if (!showCloseButton) return;

    BrowserUtils.handleBodyScroll(true);

    if (typeof params.callback === 'function') {
      params.callback();
    }

    toggleModal({ params: { noClose: true } });

    if (params.showButtonDefinitivePassword) {
      redirectToDefinitivePassword();
    }
  };

  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key='loading'
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
        className='absolute z-[100099900] w-full h-full flex flex-col justify-center items-center bg-[rgba(255,255,255,0.8)]'
        onClick={onClose}>
        <div
          style={{
            position: 'absolute',
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            flexDirection: 'column',
            justifyItems: 'center',
            alignItems: 'center',
            width: '80%',
            height: '50%',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'white',
            padding: '20px',
            zIndex: 1000,
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            borderRadius: '5px',
            border: '5px solid rgb(98 130 154)'
          }}>
          {/* eslint-disable-next-line react/no-danger */}
          <p className={classeText} dangerouslySetInnerHTML={{ __html: textInfoModal }} />

          {params?.showSpinner && <SpinnerLoading />}

          {params?.progress && progress !== '0' && <ProgressBar progress={progress} />}

          {showCloseButton && <CloseButton onClick={onClose} />}

          {params.showButtonDefinitivePassword && (
            <DefinitivePasswordSection
              time={time}
              redirectToDefinitivePassword={redirectToDefinitivePassword}
              classeText={classeText}
            />
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default Modal;
