/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
import { SubmitHandler, useForm } from 'react-hook-form';

import { useCase, useLoading } from '@/hooks';

interface IFormInput {
  cpf: string;
  name: string;
  email: string;
  state: string;
  telefone: string;
  message: string;
  EmpreendimentoId: string;
  EmpreendimentoName: string;
}

export const useIamNeighbor = () => {
  const { loading } = useLoading();
  const { sendCase } = useCase();
  const {
    setValue,
    register,
    formState: { errors },
    handleSubmit,
    reset
  } = useForm<IFormInput>();

  const handleSubmitForm: SubmitHandler<IFormInput> = async (data) => {
    const messageVizinho = `DADOS VIZINHO:
    CPF: ${data.cpf};
    Nome: ${data.name}
    Telefone: ${data.telefone}
    E-mail: ${data.email}
    Estado: ${data.state}
    Messagem: ${data.message}
    Empreendimento: ${data.EmpreendimentoName}
    `;

    await sendCase({
      SuppliedName: data.name,
      SuppliedEmail: data.email,
      SuppliedPhone: data.telefone,
      tipoAtendimento: 'Solicitação',
      classificacao: 'Empreendimento',
      EmpreendimentoId: data.EmpreendimentoId,
      recordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente',
      assunto: 'Vizinho de Obra',
      description: messageVizinho,
      textLoadingCase: 'Estamos enviando sua solicitação...'
    });

    reset();
  };

  const handle = handleSubmit(handleSubmitForm);

  return { register, errors, handle, loading, setValue };
};
