/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';
import Link from 'next/link';

import { useEffect, useLayoutEffect, useRef, useState } from 'react';

import { useApi } from '@/hooks/useApi';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import CustomDropdown, {
  CustomDropdownHandles
} from '@/templates/components/Commons/inputs/CustomDropdown';
import Input from '@/templates/components/Commons/inputs/Input';

import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';

import { useMediaQuery } from '@/hooks';

import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useIamNeighbor } from './hooks/useIamNeighbor';

interface EmpreendimentoVizinho {
  id: number;
  value: string;
  name: string;
  Regional__c: string;
}

export default function IamNeighborScreen() {
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();
  const { register, errors, handle, loading, setValue } = useIamNeighbor();
  const [stateFilter, setStateFilter] = useState('São Paulo');
  const [empreendimentosFilter, setEmpreendimentoFilter] = useState<EmpreendimentoVizinho[]>([]);
  const [empreendimentos, setEmpreendimentos] = useState<EmpreendimentoVizinho[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const stateDropdownRef = useRef<CustomDropdownHandles>(null);
  const empreendimentoDropdownRef = useRef<CustomDropdownHandles>(null);
  const { getFetch } = useApi();

  // Function to fetch empreendimentos
  const fetchEmpreendimentos = async () => {
    try {
      setIsLoading(true);
      const response = await getFetch<{ data: { empreendimentos: EmpreendimentoVizinho[] } }>({
        method: 'GET',
        route: 'empreendimento/filterToVizinho'
      });

      if (response && response.data) {
        setEmpreendimentos(response.data.empreendimentos);
      }
    } catch (error) {
      console.error('Error fetching empreendimentos:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch empreendimentos on component mount
  useEffect(() => {
    fetchEmpreendimentos();
  }, []);

  const closeAll = () => {
    if (stateDropdownRef.current) {
      stateDropdownRef.current.closeDropdown();
    }
    if (empreendimentoDropdownRef.current) {
      empreendimentoDropdownRef.current.closeDropdown();
    }
  };

  useEffect(() => {
    if (empreendimentos.length > 0) {
      const filteredEmpreendimentos = empreendimentos.filter(
        (empreendimento: EmpreendimentoVizinho) => empreendimento.Regional__c === stateFilter
      );

      const empreendimentosComDefault = [
        { id: 0, value: '', name: 'Selecione o empreendimento' },
        ...filteredEmpreendimentos
      ];

      setEmpreendimentoFilter(empreendimentosComDefault as EmpreendimentoVizinho[]);

      if (empreendimentoDropdownRef.current !== null) {
        empreendimentoDropdownRef.current.setNewOption(
          empreendimentosComDefault as EmpreendimentoVizinho[]
        );
      }
    }
  }, [empreendimentos, stateFilter]);

  const { setShowSidebar } = useAppContext();

  useLayoutEffect(() => {
    setShowSidebar(false);
  }, [setShowSidebar]);

  const conditions = [isMobile === null, isLoading];
  if (!canBeRender({ conditions })) return null;

  function onChangeState(value: string | number): void {
    setValue('state', value.toString());
    setStateFilter(value.toString());

    closeAll();
  }
  function onChangeEmpreendimento(value: string | number): void {
    const empreendimentoName = empreendimentos.filter(
      (empreendimento: EmpreendimentoVizinho) => empreendimento.value === value.toString()
    );

    setValue('EmpreendimentoId', value.toString());
    if (empreendimentoName && empreendimentoName.length > 0) {
      setValue('EmpreendimentoName', empreendimentoName[0].name);
    }

    closeAll();
  }

  return (
    <>
      <div className='flex flex-col justify-center max-md:w-full'>
        <Link href='/login'>
          <Image
            src='/images/logo.svg'
            alt='Icon'
            className='w-1/3 flex m-auto max-md:w-2/3'
            width={65}
            height={65}
            priority
          />
        </Link>
        <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
          Sou vizinho de uma obra
        </h3>

        <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 pb-4'>
          <div className='w-2/3 max-md:w-full'>
            <form onSubmit={handle}>
              <Input
                mask='999.999.999-99'
                {...register('cpf', { required: 'CPF obrigatório' })}
                // name='cpf'
                type='textmask'
                label='CPF/CNPJ (somente números)'
                errors={errors.cpf ? errors.cpf.message : null}
              />

              <Input
                {...register('name', {
                  required: 'Seu nome é obrigatório. '
                })}
                errors={errors.name ? errors.name.message : null}
                label='Digite seu nome*:'
                type='text'
              />
              <Input
                {...register('email', {
                  required: 'Seu e-mail é obrigatório. '
                })}
                errors={errors.email ? errors.email.message : null}
                label='Seu E-mail*'
                type='text'
              />
              <Input
                {...register('telefone', {
                  required: 'Seu telefone é obrigatório. '
                })}
                errors={errors.telefone ? errors.telefone.message : null}
                label='Seu telefone*'
                type='text'
              />
              <CustomDropdown
                ref={stateDropdownRef}
                label={'Estado:'}
                optionsExternal={[
                  { id: 0, value: '', name: 'Selecione o estado' },
                  { id: 1, value: 'São Paulo', name: 'São Paulo' },
                  { id: 2, value: 'Rio de Janeiro', name: 'Rio de Janeiro' }
                ]}
                onChange={(value) => onChangeState(value as string | number)}
                defaultValue='Selecione o estado'
                onClose={closeAll}
              />

              <CustomDropdown
                ref={empreendimentoDropdownRef}
                label={'Empreendimento:'}
                optionsExternal={empreendimentosFilter}
                onChange={(value) => onChangeEmpreendimento(value as string | number)}
                defaultValue='Empreendimentos:'
                onClose={closeAll}
              />

              <Input
                {...register('message')}
                type='textarea'
                label='Deixe seu comentário:'
                rows={4}
              />
              <div className='flex  md:flex-row-reverse justify-around items-center'>
                <Buttonblue
                  disabled={loading}
                  text='Enviar'
                  background='navy-blue'
                  color='white'
                  type='submit'
                  classExtra={'!w-[160px] md:!w-[200px]'}
                />
                <Buttontransparent
                  text='Voltar'
                  color='navy-blue'
                  route={'/'}
                  classExtra={'!w-[160px] md:!w-[200px] '}
                />
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
