/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimateFadeIn, AnimateFadeInTransitions } from '@/constants/ANIMATIONS';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"

// import FinancialTable from '../Financial/StatementReport/components/FinancialTable';
// import { useFinancialData } from '../Financial/StatementReport/hooks/useFinancialData';
import { useAppContext } from '@/contexts/AppContext';
import ListAndamento from '../WorkInProgress/components/ListAndamento';
// import ExtractActionsTable from '../Financial/StatementReport/components/ExtractActionsTable';

export default function HomeMoradorScreen() {
  const { user } = useAppContext();
  // const { financials } = useFinancialData();
  if (user === null) return null;
  // if (financials === null) return null;
  return (
    <>
      <hr className='border-t-1 border-white mb-4 mt-4 w-full' />
      <div className='flex flex-row justify-between max-[991px]:hidden'>
        <motion.div
          initial='collapsed'
          animate={'openWithDelay1'}
          variants={AnimateFadeIn}
          transition={AnimateFadeInTransitions.openWithDelay1}
          className='w-full p-5 bg-white rounded mt-6'>
          {/* className='w-7/12 p-5 bg-white rounded mt-6'> */}
          <h3 className='tracking-widest font-bold text-navy-blue mb-2.5'>Andamento de obra</h3>
          <ListAndamento />
          <div className='flex justify-center'>
            <div className='w-2/3'>
              <Buttonblue
                text='Acessar Andamento de obra'
                background='navy-blue'
                color='white'
                route={`/${user?.AccountId}/andamento-de-obra`}
              />
            </div>
          </div>
        </motion.div>
        {/* <motion.div
          initial='collapsed'
          animate={'openWithDelay1'}
          variants={AnimateFadeIn}
          className='w-39 p-5 bg-white rounded mt-6'>
          <h3 className='tracking-widest font-bold text-navy-blue mb-2.5'>Financeiro</h3>
          <ExtractActionsTable />
          {financials && <FinancialTable />}
        </motion.div> */}
      </div>
    </>
  );
}
