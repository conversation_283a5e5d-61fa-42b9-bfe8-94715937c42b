/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { Case } from '@/@types/cases';
import { useAppContext } from '@/contexts/AppContext';
import { fetchCases } from '@/server/actions/content';
import { useEffect, useState } from 'react';
import TrackRequestsContent from '../TalkToCury/content/TrackRequestsContent';

export default function HomeSindicoScreen() {
  const { setShowSidebar, content, isMobile } = useAppContext();
  const [cases, setCases] = useState<Case[]>([]);

  useEffect(() => {
    const fetchCasesFromServer = async () => {
      const cases: Case[] = await fetchCases('sindico');
      setCases(cases);
    };
    fetchCasesFromServer();
    setShowSidebar(!isMobile);
  }, [content]);

  return <TrackRequestsContent cases={cases} />;
}
