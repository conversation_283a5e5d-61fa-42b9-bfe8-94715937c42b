/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { SITE_URL } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import ButtonSidebarFormSindico from '@/templates/components/Commons/buttons/ButtonSidebarFormSindico';
import ButtonSidebar from '../../../components/Commons/buttons/ButtonSidebar';
import MenuContactDesk from './MenuContactsDesk';

const MenuSindico = () => {
  const { user, content, setIsModalVisible, isMobile } = useAppContext();

  if (content === null || content === undefined || user === undefined || user === null) return null;
  const codEmpreendimento = content.Empreendimento ? content.Empreendimento.CodigoSienge__c : '';

  return (
    <>
      <ButtonSidebar
        src='/images/solicitar-atendimento.png'
        srcMobile='/images/solicitar-atendimento-m.png'
        text='Atendimento'
        route={`/${user.AccountId}/sindico/atendimento`}
      />
      <ButtonSidebar
        src='/images/acompanhar-atendimento.png'
        srcMobile='/images/acompanhar-atendimento-m.png'
        text='Acompanhar Atendimentos'
        route={`/${user.AccountId}/sindico/acompanhar-atendimento`}
      />
      <ButtonSidebar
        src='/images/documentos.png'
        srcMobile='/images/documentos-m.png'
        text='Documentos'
        route={`/${user.AccountId}/sindico/documentos`}
      />

      {content.Empreendimento?.Manual__c && (
        <ButtonSidebarFormSindico
          text='Pasta do Síndico'
          src='/images/pasta-do-sindico.png'
          srcMobile='/images/pasta-do-sindico-m.png'
          url='https://manual1.com.br/manuais/api/cury/access_man.php'
          urlMobile={`${SITE_URL}/redirect?type=pasta&codEmpreendimento=${codEmpreendimento}`}
        />
      )}
      {content.Empreendimento?.Sigma__c && (
        <ButtonSidebarFormSindico
          text='Gestão de Manutenção'
          src='/images/gestao-de-manutencao.png'
          srcMobile='/images/gestao-de-manutencao-m.png'
          url='https://www.sigmacivil.com.br/us/input_cury.asp'
          urlMobile={`${SITE_URL}/redirect?type=gestao&codEmpreendimento=${codEmpreendimento}`}
        />
      )}
      <ButtonSidebar
        src='/images/dicas.png'
        srcMobile='/images/dicas-m.png'
        text='Dicas'
        route={`/${user.AccountId}/sindico/dicas`}
      />
      <ButtonSidebar
        src='/images/videos-e-tutoriais.png'
        srcMobile='/images/videos-e-tutoriais-m.png'
        text='Videos e Tutoriais'
        route={`/${user.AccountId}/sindico/videos-e-tutoriais`}
      />
      {isMobile ? (
        <>
          <div className='flex justify-center'>
            <ButtonSidebar
              srcMobile='/images/canais-de-atendimentos-m.png'
              text='Canais de atendimento'
              onClick={() => setIsModalVisible(true)}
            />
          </div>
          <div className='flex justify-center'>
            <ButtonSidebar
              srcMobile='/images/fale-com-a-clara-m.png'
              text='Fale com a Clara'
              route={`/${user.AccountId}/sindico/clara`}
            />
          </div>
        </>
      ) : (
        <MenuContactDesk />
      )}
    </>
  );
};

export default MenuSindico;
