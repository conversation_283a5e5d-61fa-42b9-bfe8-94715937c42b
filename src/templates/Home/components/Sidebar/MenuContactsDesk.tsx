/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { WHATS_NUMBER } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { RelationshipCenter } from '@/templates/components/Commons/footers/contents';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { Show } from '@/utils/components/Show';
import { DeviceUtils } from '@/utils/DeviceUtils';
import ButtonSidebar from '../../../components/Commons/buttons/ButtonSidebar';

const MenuContactDesk = () => {
  const { user, content } = useAppContext();
  if (content === null || user === null) return null;
  return (
    <>
      <hr className='border-t-1 border-white mb-6 mt-3.5 w-11/12' />
      <div className='flex md:flex-col md:items-start items-center w-full'>
        {DeviceUtils.isMobileApp() ? (
          <ButtonSidebar
            src='/images/whatsapp.png'
            srcMobile='/images/whatsapp.png'
            text='Contate pelo WhatsApp'
            onClick={() =>
              BrowserUtils.ActionReactNative({
                type: 'openUrlBrowser',
                url: WHATS_NUMBER,
                data: {}
              })
            }
            target='_blank'
          />
        ) : (
          <ButtonSidebar
            src='/images/whatsapp.png'
            srcMobile='/images/whatsapp.png'
            text='Contate pelo WhatsApp'
            route={WHATS_NUMBER}
            target='_blank'
          />
        )}
        <Show when={content?.type === 'morador'}>
          <ButtonSidebar
            src='/images/solicitar-atendimento.png'
            srcMobile='/images/solicitar-atendimento.png'
            text='Solicitar Atendimento'
            route={`/${user.AccountId}/fale-com-a-cury/solicitar-atendimento`}
          />
        </Show>
        <Show when={content?.type === 'morador'}>
          <ButtonSidebar
            src='/images/acompanhar-solicitacoes.png'
            srcMobile='/images/acompanhar-solicitacoes.png'
            text='Acompanhar Atendimentos'
            route={`/${user.AccountId}/fale-com-a-cury/acompanhar-solicitacoes`}
          />
        </Show>
      </div>
      <Show when={content?.type === 'morador'}>
        <hr className='border-t-1 border-white mb-6 mt-6 w-11/12' />
        <RelationshipCenter />
      </Show>
    </>
  );
};

export default MenuContactDesk;
