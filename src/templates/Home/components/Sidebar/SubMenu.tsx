/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimateSubmenu } from '@/constants/ANIMATIONS';
import { useAppContext } from '@/contexts/AppContext';
import { useInforme } from '@/contexts/useInforme';
import { usePermissions } from '@/hooks/usePermissions';
import ButtonSubMenu from '@/templates/components/Commons/buttons/ButtonSubMenu';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import { usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

interface SubMenuProps {
  showSubMenu: boolean;
}

const SubMenu = ({ showSubMenu }: SubMenuProps) => {
  const { setShowInforme } = useInforme();
  const { user, content } = useAppContext();
  const pathname = usePathname();
  const [internalShowSubMenu, setInternalShowSubMenu] = useState(showSubMenu);
  const [thisPath, setThisPath] = useState('');
  const motionDivRef = useRef(null);
  const [animate, setAnimate] = useState(false);
  const { getPermission } = usePermissions(content);
  const canViewChegaMais = getPermission('Chega Mais');
  const canViewInforme = true; // getPermission('Informe de pagamentos');

  function onStart() {
    setAnimate(true);
  }
  function onComplete() {
    setAnimate(false);
  }

  const dynamicStyle = {
    display: animate || showSubMenu ? 'block' : 'none'
  };

  const ir = () => {
    setShowInforme(true);
  };

  useEffect(() => {
    if (thisPath !== pathname) {
      setThisPath(pathname);
      setInternalShowSubMenu(false);
    }
  }, [pathname]);
  useEffect(() => {
    setInternalShowSubMenu(showSubMenu);
  }, [showSubMenu]);

  if ((!showSubMenu && animate) || user === null) return null;

  return (
    <motion.div
      key={pathname}
      animate={internalShowSubMenu ? 'open' : 'collapsed'}
      variants={AnimateSubmenu}
      className='relative z-[10000000]'
      onAnimationStart={onStart}
      onAnimationComplete={onComplete}
      style={dynamicStyle}
      ref={motionDivRef}>
      <div
        // className={`absolute -top-14 -right-62  bg-white rounded shadow-white ${internalShowSubMenu ? 'w-auto h-auto' : 'w-[1px] h-[1px]'}`}
        className={'absolute top-[-3.7rem] right-[-217px]  bg-white rounded shadow-white }'}>
        <ButtonSubMenu
          src='/images/2via-boleto-b.png'
          text='2º via de Boleto'
          border='border'
          route={`/${user?.AccountId}/financeiro/2via-de-boleto`}
          disabled={!internalShowSubMenu}
        />
        <ButtonSubMenu
          src='/images/relatorio-de-extrato-b.png'
          text='Relatório de Extrato'
          border='border'
          route={`/${user?.AccountId}/financeiro/relatorio-de-extrato`}
          disabled={!internalShowSubMenu}
        />
        <ButtonSubMenu
          src='/images/negocie-suas-parcelas-b.png'
          text='Negocie suas parcelas'
          border='border'
          route={`/${user?.AccountId}/financeiro/negocie-suas-parcelas`}
          disabled={!internalShowSubMenu}
        />
        <ButtonSubMenu
          src='/images/cury-chega-mais-b.png'
          text='Cury Chega Mais'
          border='border'
          route={`/${user?.AccountId}/financeiro/cury-chega-mais`}
          disabled={!internalShowSubMenu}
          permission={canViewChegaMais}
        />
        <ButtonSubMenu
          src='/images/informe-de-pagamentos-b.png'
          text='Informe de Pagamentos'
          disabled={!internalShowSubMenu}
          onClick={ir}
          permission={canViewInforme}
        />
      </div>
    </motion.div>
  );
};

export default SubMenu;
