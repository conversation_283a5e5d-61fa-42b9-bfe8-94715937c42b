/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import ButtonSidebar from '@/templates/components/Commons/buttons/ButtonSidebar';
import { Show } from '@/utils/components/Show';
import MenuClient from './MenuClient';
import MenuContactDesk from './MenuContactsDesk';
import MenuSindico from './MenuSindico';

const Sidebar = () => {
  const { user, content, showSidebar, isMobile , showBoletoAto} = useAppContext();

  if (
    !showSidebar ||
    isMobile === null ||
    isMobile === undefined ||
    content === null ||
    content === undefined ||
    showBoletoAto
  ) {
    return null;
  }

  return (
    <>
      <div className='md:w-3/12 max-md:mt-6'>
        <div className='max-md:flex max-md:justify-center max-md:items-center'>
          <div className='flex flex-col max-md:grid max-md:grid-cols-3 max-md:justify-center max-md:items-center max-md:w-[95%] max-md:mb-20'>
            {/* {content.type === 'morador' ? <MenuClient /> : <MenuSindico />} */}

            <Show when={content.type === 'morador'} fallback={<MenuSindico />}>
              <MenuClient />

              <Show when={isMobile} fallback={<MenuContactDesk />}>
                <ButtonSidebar
                  srcMobile='/images/icon-blue.png'
                  text='Fale com a Cury'
                  route={`/${user.AccountId}/fale-com-a-cury`}
                />
              </Show>
            </Show>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
