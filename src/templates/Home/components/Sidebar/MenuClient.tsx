/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useEffect, useState } from 'react';

import { useAppContext } from '@/contexts/AppContext';
import { usePermissions } from '@/hooks/usePermissions';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { usePathname } from 'next/navigation';
import ButtonSidebar from '../../../components/Commons/buttons/ButtonSidebar';
import SubMenu from './SubMenu';

const MenuClient = () => {
  const { user, content, isMobile } = useAppContext();
  const pathname = usePathname();
  const [showSubMenu, setShowSubMenu] = useState(false);
  const { getPermission } = usePermissions(content);
  const canViewFinanciamento = getPermission('financiamento');
  const isMobileApp = DeviceUtils.isMobileApp();
  useEffect(() => {
    setShowSubMenu(false);
  }, [pathname]);
  if (isMobile === null || user === null) return null;

  return (
    <>
      <ButtonSidebar
        src='/images/meu-imovel.png'
        srcMobile='/images/meu-imovel-m.png'
        text='Meu Imóvel'
        route={`/${user.AccountId}/meu-imovel`}
      />
      <ButtonSidebar
        src='/images/andamento-de-obra.png'
        srcMobile='/images/andamento-de-obra-m.png'
        text='Andamento de obra'
        route={`/${user.AccountId}/andamento-de-obra`}
      />
      <ButtonSidebar
        src='/images/financeiro.png'
        srcMobile='/images/financeiro-m.png'
        text='Financeiro'
        route={isMobile ? `/${user.AccountId}/financeiro` : null}
        onClick={!isMobile ? () => setShowSubMenu(!showSubMenu) : undefined}
      />
      {!isMobile && <SubMenu showSubMenu={showSubMenu} />}
      <ButtonSidebar
        src='/images/financiamento.png'
        srcMobile='/images/financiamento-m.png'
        text='Financiamento'
        route={`/${user.AccountId}/financiamento`}
        permission={canViewFinanciamento}
        // permission={true}
      />
      <ButtonSidebar
        src='/images/dicas.png'
        srcMobile='/images/dicas-m.png'
        text='Dicas'
        route={`/${user.AccountId}/dicas`}
      />

      <ButtonSidebar
        src='/images/agenda.png'
        srcMobile='/images/agenda-m.png'
        text='Agenda'
        route={`/${user.AccountId}/agenda`}
      />
      {/* {isMobileApp ? ( */}
      <ButtonSidebar
        src='/images/cury-vida.png'
        srcMobile='/images/cury-vida-m.png'
        text='Cury + Vida - SP'
        onClick={() => {
          return isMobileApp
            ? BrowserUtils.ActionReactNative({
                type: 'openUrlBrowser',
                url: 'https://calculadorasp.fly.dev/',
                data: {}
              })
            : window.open('https://calculadorasp.fly.dev/', '_blank');
        }}
        target='_blank'
      />
      {/* ) : (
        <ButtonSidebar
          src='/images/cury-vida.png'
          srcMobile='/images/cury-vida-m.png'
          text='Cury + Vida - SP'
          route={'https://calculadorasp.fly.dev/'}
          target='_blank'
        />
      )} */}
      {/* {isMobileApp ? ( */}
      <ButtonSidebar
        src='/images/cury-vida.png'
        srcMobile='/images/cury-vida-m.png'
        text='Cury + Vida - RJ'
        onClick={() => {
          return isMobileApp
            ? BrowserUtils.ActionReactNative({
                type: 'openUrlBrowser',
                url: 'https://calculadorario.fly.dev/',
                data: {}
              })
            : window.open('https://calculadorario.fly.dev/', '_blank');
        }}
        target='_blank'
      />
      {/* ) : (
        <ButtonSidebar
          src='/images/cury-vida.png'
          srcMobile='/images/cury-vida-m.png'
          text='Cury + Vida - RJ'
          route={'https://calculadorario.fly.dev/'}
          target='_blank'
        />
      )} */}
      <ButtonSidebar
        src='/images/duvidas-frequentes.png'
        srcMobile='/images/duvidas-frequentes-m.png'
        text='Dúvidas Frequentes'
        route={`/${user.AccountId}/duvidas-frequentes`}
      />

      {/* <ButtonSidebar
          src="/images/sobre-a-regiao.png"
          srcMobile="/images/sobre-a-regiao-m.png"
          text="Sobre a Região"
          route={() => (window.location.href = "/sobre-a-regiao")}
        /> */}
    </>
  );
};

export default MenuClient;
