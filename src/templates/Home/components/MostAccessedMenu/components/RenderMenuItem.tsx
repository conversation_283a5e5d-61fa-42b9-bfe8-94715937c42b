/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { SwiperSlide } from 'swiper/react';

import Button from '@/templates/components/Commons/buttons/Button';

export const RenderMenuItem = (canView: boolean, src: string, text: string, href: string) => {
  if (!canView) return null;
  return (
    <SwiperSlide className='!max-md:w-[40px] !max-w-[130px]' key={src}>
      <Button classCustom={'!max-md:w-[40px] !max-w-[130px]'} src={src} text={text} href={href} />
    </SwiperSlide>
  );
};
