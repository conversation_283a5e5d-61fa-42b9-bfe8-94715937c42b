/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimateFadeIn, AnimateFadeInTransitions } from '@/constants/ANIMATIONS';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import { memo } from 'react';

import 'swiper/css';
import { Swiper } from 'swiper/react';

import { useAppContext } from '@/contexts/AppContext';
import { usePermissions } from '@/hooks/usePermissions';
import { RenderMenuItem } from './components/RenderMenuItem';

const MostAccessedMenu = () => {
  const { user, content, isMobile } = useAppContext();
  const { getPermission } = usePermissions(content);
  const canViewSegundaVia = getPermission('2a via boleto');
  const canViewNegocie = getPermission('Negocie suas parcelas');
  const canViewRelatorioDeExtrato = getPermission('Relatório de Extrato');
  const canViewfinanciamento = getPermission('financiamento');
  const canViewdocumentos = getPermission('documentos');
  const canViewAndamento = getPermission('Andamento');

  if (content?.type !== 'morador' || isMobile === null) return null;

  return (
    <>
      <motion.div
        initial='collapsed'
        animate={'openWithDelay1'}
        variants={AnimateFadeIn}
        transition={AnimateFadeInTransitions.openWithDelay1}
        id='MostAccessedMenu'
        className=' max-md:w-auto mt-[20px]'>
        <Swiper
          className='mySwiper'
          slidesPerView={isMobile ? 3.8 : 5.5}
          spaceBetween={isMobile ? 0 : 10}>
          <>
            {RenderMenuItem(
              canViewSegundaVia,
              '/images/2via-boleto.png',
              '2ª via de Boleto',
              `/${user?.AccountId}/financeiro/2via-de-boleto`
            )}
          </>
          <>
            {RenderMenuItem(
              canViewNegocie,
              '/images/negocie-suas-parcelas.png',
              'Negocie suas parcelas',
              `/${user?.AccountId}/financeiro/negocie-suas-parcelas`
            )}
          </>
          <>
            {RenderMenuItem(
              canViewRelatorioDeExtrato,
              '/images/relatorio-de-extrato.png',
              'Relatório de extrato',
              `/${user?.AccountId}/financeiro/relatorio-de-extrato`
            )}
          </>
          <>
            {RenderMenuItem(
              canViewfinanciamento,
              '/images/financiamento-big.png',
              'Financiamento',
              `/${user?.AccountId}/financiamento`
            )}
          </>
          <>
            {RenderMenuItem(
              canViewAndamento,
              '/images/andamento-de-obra-big.png',
              'Andamento de obra',
              `/${user?.AccountId}/andamento-de-obra`
            )}
          </>
          <>
            {RenderMenuItem(
              canViewdocumentos,
              '/images/meus-documentos.png',
              'Meus documentos',
              `/${user?.AccountId}/meu-imovel/documentos`
            )}
          </>
        </Swiper>
      </motion.div>
    </>
  );
};

export default memo(MostAccessedMenu);
