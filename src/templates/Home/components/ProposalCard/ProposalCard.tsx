/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useEffect, useState } from 'react';

import { useAppContext } from '@/contexts/AppContext';
import { useInstallments } from '@/hooks/Installments/useInstallments';
import Boleto from '@/templates/components/Boleto/Boleto';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';

const BOLETO_GERADO = 'boleto-gerado';

export default function BoletoAtoCard() {
  const { user, content } = useAppContext();
  const { getInstallments, installments, urlBoleto, digitableNumberBoleto, getBoleto } =
    useInstallments();
  const [showBoleto, setShowBoleto] = useState(false);
  const toggleBoleto = () => {
    setShowBoleto((prevShowBoleto) => !prevShowBoleto);
  };

  useEffect(() => {
    const loadInstallments = async () => {
      await getInstallments(user?.CodigoSienge__c as string, content?.UnidadeAtivoName as string);
    };
    loadInstallments();
  }, []);
  useEffect(() => {
    if (installments) {
      // const installment = installments.data.installments?.data[0];
      const installment = installments?.data[0];
      getBoleto(0, installment?.billReceivableId, content?.UnidadeAtivoName as string);
    }
  }, [installments?.data[0]?.installments]);
  // }, [installments?.data?.installments?.data[0]?.installments]);
  let textBoleto = 'O boleto ainda está sendo gerado, tente novamente em um outro momento.';
  let buttonBg = 'navy-blue';
  let buttonColor = 'white';
  if (!content) return null;

  if (content?.statusBoletoAto === BOLETO_GERADO) {
    textBoleto = 'O boleto foi gerado, Pague agora mesmo.';
    buttonBg = 'light-gray';
    buttonColor = 'navy-blue';
  }
  const password = user?.CPF__c
    ? user?.CPF__c.replace('.', '').replace('-', '')
    : user?.CNPJ__c.replace('.', '').replace('-', '').replace('/', '');
  return (
    <>
      <div className=' flex flex-col w-12/12 mb-10 mt-6   '>
        <div className=' flex flex-col w-full justify-center items-center p-5  bg-[#fff] '>
          <h3 className='text-md font-medium text-navy-blue tracking-1 leading-5 mt-4 text-center'>
            Boleto Ato
          </h3>
          <p className='text-sm text-navy-blue tracking-1 leading-5 mt-4 text-center'>
            {textBoleto}
          </p>
          {content?.statusBoletoAto === BOLETO_GERADO && (
            <>
              <Buttonblue
                text='Gerar Boleto Ato'
                background={buttonBg}
                color={buttonColor}
                disabled={content.statusBoletoAto !== BOLETO_GERADO}
                onClick={toggleBoleto}
              />
              <Boleto
                filename={`boleto-ato-${password}.pdf`}
                urlBoleto={urlBoleto}
                digitableNumberBoleto={digitableNumberBoleto}
                // password={password}
                show={showBoleto}
                toggleBoleto={toggleBoleto}
              />
            </>
          )}
        </div>
      </div>
    </>
  );
}
