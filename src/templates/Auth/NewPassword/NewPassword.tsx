'use client';
import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import Input from '@/templates/components/Commons/inputs/Input';
import Logo from '@/templates/components/Commons/Logo';
import { useEffect, useLayoutEffect } from 'react';
import ButtonsActionsAuth from '../components/ButtonsActionsAuth';
import { useNewPassword } from '../hooks/useNewPassword';

export default function NewPasswordScreen() {
  const { register, errors, handle, loading, password, validatePassword } = useNewPassword();

  const { setShowSidebar } = useAppContext();

  useLayoutEffect(() => {
    setShowSidebar(false);
  }, [setShowSidebar]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  return (
    <div className='w-full flex items-center justify-centers flex-col'>
      <Logo />
      <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
        Redefinição de senha
      </h3>

      <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 w-full md:w-[80%]'>
        <div className='w-full md:px-8'>
          <form onSubmit={handle} className={'flex flex-col items-center w-full'}>
            <div className='w-full mb-4'>
              <h4 className='text-gray-700 text-sm font-medium mb-2'>Requisitos de senha:</h4>
              <ul className='text-xs text-gray-600 list-disc pl-5'>
                <li>Mínimo de 8 caracteres</li>
                <li>Pelo menos 1 letra minúscula</li>
                <li>Pelo menos 1 letra maiúscula</li>
                <li>Pelo menos 1 número</li>
                <li>Caracteres especiais permitidos: ! $ *</li>
              </ul>
            </div>

            <Input
              className={'w-full'}
              {...register('password', {
                required: 'Senha obrigatória',
                validate: validatePassword
              })}
              type='password'
              name='password'
              key='password'
              label='Nova senha'
              showSavePass={false}
              errors={errors.password ? errors.password.message : null}
            />
            <Input
              className={'w-full'}
              {...register('passwordConfirm', {
                required: 'Confirmação de senha obrigatória',
                validate: (value) => value === password || 'As senhas não coincidem'
              })}
              type='password'
              name='passwordConfirm'
              key='passwordConfirm'
              label='Confirmar nova senha'
              showSavePass={false}
              errors={errors.passwordConfirm ? errors.passwordConfirm.message : null}
            />

            <ButtonsActionsAuth loading={loading} />
          </form>
        </div>
      </div>
    </div>
  );
}
