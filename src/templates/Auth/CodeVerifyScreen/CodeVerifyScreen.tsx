/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import React, {
  ChangeEvent,
  ClipboardEvent,
  CSSProperties,
  FormEvent,
  KeyboardEvent,
  MouseEvent,
  useEffect,
  useLayoutEffect,
  useRef,
  useState
} from 'react';

// import { useAuth } from '@/contexts/AuthContext';
import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks/useLoading';
import { useVerificationCode } from '@/hooks/useVerificationCode';
import { verifyCodeAction } from '@/server/actions/auth';
import { getAuthCookies } from '@/server/services/cookiesService';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import CustomLoading from '@/templates/components/Commons/Loading/CustomLoading';
import Logo from '@/templates/components/Commons/Logo';
import { StringUtils } from '@/utils/StringUtils';
import { useRouter } from 'next/navigation';
import { useForgotPassword } from '../hooks/useForgotPassword';

// interface InputCodeProps {
//   id: string;
//   value: string;
//   onChange: (event: ChangeEvent<HTMLInputElement>) => void;
//   handlePaste: (event: ClipboardEvent<HTMLInputElement>) => void;
//   indicatorStyles: CSSProperties;
// }

// const InputCode: React.FC<InputCodeProps> = ({
//   id,
//   value,
//   onChange,
//   handlePaste,
//   indicatorStyles
// }) => {
//   return (
//     <input
//       id={id}
//       type='text'
//       value={value}
//       onChange={onChange}
//       onPaste={handlePaste}
//       className='rounded border-1 bg-[#ECF3F9] h-12 px-3 text-sm text-navy-blue tracking-1 w-[36px]'
//       style={indicatorStyles}
//       max={1}
//       autoComplete='off'
//       role='presentation'
//       maxLength={1}
//       required
//       autoFocus
//       // aria-autocomplete='none'
//     />
//   );
// };

interface InputCodeProps {
  id: string;
  value: string;
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
  handlePaste: (event: ClipboardEvent<HTMLInputElement>) => void;
  onKeyDown: (event: KeyboardEvent<HTMLInputElement>) => void;
  isValid: boolean | null;
  autoFocus?: boolean;
}

const InputCode: React.FC<InputCodeProps> = ({
  id,
  value,
  onChange,
  handlePaste,
  onKeyDown,
  isValid,
  autoFocus = false
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Focar automaticamente no campo quando solicitado
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  return (
    <input
      ref={inputRef}
      id={id}
      type='text'
      value={value}
      onChange={onChange}
      onPaste={handlePaste}
      onKeyDown={onKeyDown}
      className={`rounded border-1 bg-[#ECF3F9] h-12 px-3 text-center text-sm text-navy-blue tracking-1 w-[36px] focus:outline-none ${
        isValid === false ? 'border-[#FC124D]' : 'border-[#2473B6]'
      }`}
      maxLength={1}
      autoComplete='off'
      aria-label={`Código de verificação dígito ${parseInt(id) + 1}`}
      required
    />
  );
};

const CodeVerifyScreen: React.FC = () => {
  const [codeValid, setCodeValid] = useState<boolean | null>(null);
  const { loading, setLoadingInfo } = useLoading();
  const { setShowSidebar } = useAppContext();
  const { resendResetPassword } = useForgotPassword();

  const [cookiesAuth, setCookiesAuth] = useState<string[]>([]);

  useEffect(() => {
    getAuthCookies().then((cookies) => {
      setCookiesAuth(cookies);
    });
  }, []);

  const cpf = cookiesAuth[0];
  const cnpj = cookiesAuth[1];
  const email = cookiesAuth[2];
  const userLogin = cpf && cpf !== 'undefined' ? cpf : cnpj && cnpj !== 'undefined' ? cnpj : '';
  const router = useRouter();

  const {
    inputs,
    isCodeValid,
    isSubmitting,
    errorMessage,
    setIsSubmitting,
    setIsCodeValid,
    setError,
    handleChange,
    handleKeyDown,
    handlePaste,
    resetCode,
    getCode
  } = useVerificationCode(6);

  // Timer para reenvio
  const [countdown, setCountdown] = useState<number>(0);
  const [canResend, setCanResend] = useState<boolean>(true);

  useLayoutEffect(() => {
    setShowSidebar(false);
  }, []);

  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  // Gerenciar countdown para reenvio
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
  }, [countdown, canResend]);

  if (!email && !userLogin) return <CustomLoading />;

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const code = getCode();
    // Validar se todos os campos foram preenchidos
    if (code.length !== 6) {
      setError('Por favor, preencha todos os campos do código');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await verifyCodeAction(code);
      if (result.success) {
        setIsCodeValid(true);
        setTimeout(() => {
          router.push(result.redirectTo || '/nova-senha');
        }, 500);
      } else {
        setError(result.error || 'Código de verificação inválido');
        resetCode();
        // Focar no primeiro campo após erro
        const firstInput = document.getElementById('0');
        if (firstInput) {
          firstInput.focus();
        }
      }
    } catch (error) {
      console.error('Erro ao verificar código:', error);
      setError('Ocorreu um erro ao verificar o código. Tente novamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Manipular reenvio do código
  const handleResend = async (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();

    if (!canResend) return;

    setCanResend(false);
    setCountdown(60); // 60 segundos até poder reenviar novamente

    try {
      const result = await resendResetPassword(userLogin);
      if (result.success) {
        // Mostrar mensagem de sucesso
        // toast.success('Código reenviado com sucesso!');
        resetCode();
      } else {
        // toast.error(result.error || 'Erro ao reenviar o código');
        setCanResend(true);
        setCountdown(0);
      }
    } catch (error) {
      console.error('Erro ao reenviar código:', error);
      // toast.error('Ocorreu um erro ao reenviar o código');
      setCanResend(true);
      setCountdown(0);
    }
  };

  const resend = async (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    await resendResetPassword(userLogin);
  };

  const indicatorStyles: CSSProperties = {
    borderColor: codeValid === false ? '#FC124D' : '#2473B6'
  };

  return (
    <div className='w-full flex items-center justify-centers flex-col'>
      <Logo />
      <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
        Verifique seu E-Mail
      </h3>
      <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 w-full md:w-[80%]'>
        <div className='w-full md:px-8'>
          <p className='text-navy-blue text-lg text-center mb-4 font-bold tracking-1 leading-5 mt-8'>
            Código de Verificação
          </p>
          <p className='text-navy-blue text-base text-center mb-4 tracking-1 leading-5 mt-4'>
            Digite abaixo o código que foi enviado para o seu e-mail cadastrado{' '}
            {email ? StringUtils.maskEmail(email) : ''}
          </p>
          <form
            autoComplete='off'
            onSubmit={handleSubmit}
            className='flex flex-col items-center justify-center w-full mt-8'>
            <div className='flex justify-around w-[80%] m-auto'>
              {inputs.map((value, idx) => (
                <InputCode
                  key={`input-code-${idx}`}
                  id={idx.toString()}
                  value={value}
                  onChange={handleChange}
                  onKeyDown={handleKeyDown}
                  handlePaste={handlePaste}
                  isValid={isCodeValid}
                  autoFocus={idx === 0}
                />
              ))}
            </div>

            {errorMessage && (
              <div className='w-full mt-4 text-[#FC124D] text-[14px] text-center'>
                {errorMessage}
              </div>
            )}

            <Buttonblue
              disabled={loading || isSubmitting}
              text={isSubmitting ? 'Verificando...' : 'Avançar'}
              background='navy-blue'
              color='white'
              type='submit'
              classExtra='mt-6'
            />
            {/*
            <button
              type='button'
              className={`text-[12px] mt-3 ${canResend ? 'underline text-[#444444]' : 'text-[#999999]'}`}
              onClick={handleResend}
              disabled={!canResend}>
              {canResend ? 'Reenviar código por e-mail' : `Reenviar código (${countdown}s)`}
            </button> */}
          </form>
        </div>
      </div>
    </div>
  );
};

export default CodeVerifyScreen;
