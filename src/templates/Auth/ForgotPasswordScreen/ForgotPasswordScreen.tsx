/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import Input from '@/templates/components/Commons/inputs/Input';
import Logo from '@/templates/components/Commons/Logo';
import { useEffect, useLayoutEffect } from 'react';
import ButtonsActionsAuth from '../components/ButtonsActionsAuth';
import { useForgotPassword } from '../hooks/useForgotPassword';

export default function ForgotPasswordScreen() {
  const { register, errors, handle, loading, handleDocumentChange } = useForgotPassword();

  const { setShowSidebar } = useAppContext();

  useLayoutEffect(() => {
    setShowSidebar(false);
  }, [setShowSidebar]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  return (
    // <div className=' w-full max-w-[90%] md:max-w-[700px] flex justify-center pt-8  flex-col'>
    <div className='w-full flex items-center justify-centers flex-col'>
      <Logo />
      <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
        Esqueci minha senha
      </h3>
      <p className='text-navy-blue text-base text-center tracking-1 leading-5'>
        Digite o número do seu CPF/CNPJ e será enviado um e-mail para redefinir a sua senha no seu
        e-mail cadastrado.
      </p>
      <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 w-full md:w-[80%]'>
        <div className='w-full md:px-8'>
          <form className={'flex flex-col items-center w-full'} onSubmit={handle}>
            <Input
              {...register('userLogin')}
              label='CPF/CNPJ (somente números)'
              name='userLogin'
              type='text'
              className='w-full'
              onChange={handleDocumentChange}
              errors={errors.userLogin ? errors.userLogin.message : null}
            />

            <ButtonsActionsAuth loading={loading} />
          </form>
        </div>
      </div>
    </div>
  );
}
