/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useData } from '@/hooks/useData';
import Input from '@/templates/components/Commons/inputs/Input';
import Logo from '@/templates/components/Commons/Logo';
import { useEffect, useLayoutEffect } from 'react';
import ButtonsActionsAuth from '../components/ButtonsActionsAuth';
import { useFirstLogin } from '../hooks/useFirstLogin';

export default function FirstLoginScreen() {
  const { register, errors, handle, loading, handleDocumentChange } = useFirstLogin();
  const { getCookie, createData } = useData();
  const thisIsFirstLogin = getCookie<boolean>('thisIsFirstLogin');

  const { setShowSidebar } = useAppContext();

  useLayoutEffect(() => {
    setShowSidebar(false);

    if (!thisIsFirstLogin) {
      createData<boolean>({
        id: 'thisIsFirstLogin',
        value: true,
        isSetCookie: true
      });
    }
  }, [setShowSidebar]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  return (
    // <div className=' w-full max-w-[90%] md:max-w-[700px] flex justify-center pt-8  flex-col'>
    <div className='w-full flex items-center justify-centers flex-col'>
      <Logo />
      <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
        Bem-vindo ao Novo App Cury Cliente
      </h3>
      <p className='text-navy-blue text-base text-center tracking-1 leading-5'>
        Estamos animados em tê-lo conosco!
        <br></br>
        <br></br>
        Para começar sua experiência, por favor, insira seu CPF ou CNPJ abaixo.
        <br></br>
        Enviaremos um e-mail de boas-vindas com sua senha provisória para acesso.
      </p>
      <p className='text-neutral-blue text-base text-center tracking-1 leading-5 mt-5 mb-5'>
        Após receber o e-mail, use a senha provisória para fazer seu primeiro login<br></br>e
        personalize sua experiência no app Cury Cliente.
      </p>
      <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 w-full md:w-[80%]'>
        <div className='w-full md:px-8'>
          <form className={'flex flex-col items-center w-full'} onSubmit={handle}>
            <Input
              label='CPF/CNPJ (somente números)'
              name='userLogin'
              className={'w-full'}
              type='text'
              // value={userLogin}
              onChange={handleDocumentChange}
              required
              // mask='999.999.999-99'
            />
            {/* <Input
              className={'w-full'}
              type='text'
              label='CPF/CNPJ (somente números)'
              // mask={maskCpfCnpj}
              {...register('userLogin', {
                required: 'CPF ou CNPJ obrigatório'
              })}
              onChange={handleDocumentChange}
              errors={errors.userLogin ? errors.userLogin.message : null}
            /> */}

            <ButtonsActionsAuth loading={loading} />
          </form>
        </div>
      </div>
    </div>
  );
}
