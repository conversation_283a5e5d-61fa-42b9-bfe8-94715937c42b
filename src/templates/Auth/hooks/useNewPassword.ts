/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { TokenType } from '@/@types/user';
import { fetchCreateNewPassword } from '@/server/actions/content';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useModal } from '../../../contexts/useModal';
import { useApi } from '../../../hooks/useApi';
import { useData } from '../../../hooks/useData';
import { useLoading } from '../../../hooks/useLoading';

interface CreatePasswordData {
  password: string;
}

interface IFormInput {
  password: string;
  passwordConfirm: string;
}

export const useNewPassword = () => {
  const { getFetch } = useApi();
  const { getData, getCookie } = useData();
  const token = getData<TokenType>({ id: 'user.temp-token' });
  const email = getCookie<string>('user.temp-email');
  const cnpj = getData<string>({ id: 'user.temp-cnpj' });
  const cpf = getData<string>({ id: 'user.temp-cpf' });
  const userLogin = cpf ?? cnpj;
  const { loading, setLoadingInfo } = useLoading();
  const { toggleModal } = useModal();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    formState: { errors },
    handleSubmit,
    watch
  } = useForm<IFormInput>();

  const password = watch('password');

  // Caracteres especiais permitidos
  const allowedSpecialChars = '!$%*()-_+=.,:;';

  // Função para validar a senha com os novos requisitos
  const validatePassword = (value: string) => {
    // Verificar tamanho mínimo
    if (value.length < 8) {
      return 'A senha deve ter no mínimo 8 caracteres';
    }

    // Verificar se contém pelo menos uma letra minúscula
    if (!/[a-z]/.test(value)) {
      return 'A senha deve conter pelo menos uma letra minúscula';
    }

    // Verificar se contém pelo menos uma letra maiúscula
    if (!/[A-Z]/.test(value)) {
      return 'A senha deve conter pelo menos uma letra maiúscula';
    }

    // Verificar se contém pelo menos um número
    if (!/[0-9]/.test(value)) {
      return 'A senha deve conter pelo menos um número';
    }

    // Verificar caracteres não permitidos
    for (let i = 0; i < value.length; i++) {
      const char = value[i];
      if (!(/[a-zA-Z0-9]/.test(char) || allowedSpecialChars.includes(char))) {
        return `O caractere "${char}" não é permitido. Use apenas letras, números ou ${allowedSpecialChars}`;
      }
    }

    return true;
  };

  const createNewPassword = async (passwordData: CreatePasswordData) => {
    try {
      setLoadingInfo(true, 'Verificando dados...');
      setIsSubmitting(true);

      const result = await fetchCreateNewPassword(
        passwordData.password,
        passwordData.password,
        DeviceUtils.deviceInfo()
      );

      if (result && result.success) {
        toggleModal({
          text: 'Sua senha foi alterada com sucesso. Você será redirecionado.'
        });
        setLoadingInfo(false);
        router.push(`${result.redirectTo}`);
      } else {
        // Handle missing user or token
        setLoadingInfo(false);
        toggleModal({ text: 'Dados de login incompletos. Tente novamente.' });
      }
    } catch (error) {
      // Handle error
      toggleModal({
        text: 'Sua nova senha não pode ser criada, tente novamente.'
      });
      setLoadingInfo(false);
      console.log(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitForm: SubmitHandler<IFormInput> = async (data) => {
    await createNewPassword({
      password: data.password.trim()
    });
  };

  const handle = handleSubmit(handleSubmitForm);

  return {
    register,
    errors,
    handle,
    loading: loading || isSubmitting,
    password,
    validatePassword
  };
};
