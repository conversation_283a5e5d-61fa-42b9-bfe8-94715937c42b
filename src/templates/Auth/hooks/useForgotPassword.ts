/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useRouter } from 'next/navigation';
import { ChangeEvent, useState } from 'react';
import { useForm } from 'react-hook-form';

import { ForgotPasswordFormData, SendResetPasswordResponse } from '@/@types/auth';
import { useData } from '@/hooks/useData';
import { saveTempData } from '@/server/actions/auth';
import { apiPost } from '@/server/services/api';
import { DeviceUtils } from '@/utils/DeviceUtils';

interface IFormInput {
  userLogin: string;
}
export function useForgotPassword() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const { createData } = useData();
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm<ForgotPasswordFormData>();

  const handleDocumentChange = (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, '');
    value = value.slice(0, 14);
    value =
      value.length <= 11
        ? value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, '$1.$2.$3-$4')
        : value.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g, '$1.$2.$3/$4-$5');

    setValue('userLogin', value);
    e.target.value = value;
  };

  // Enviar solicitação de reset de senha
  const handle = handleSubmit(async (data) => {
    setLoading(true);
    try {
      const response = await apiPost<SendResetPasswordResponse>(
        'resetPassword',
        {
          userLogin: data.userLogin,
          deviceinfo: DeviceUtils.deviceInfo()
        },
        undefined,
        {
          requiresAuth: false
        }
      );
      if (response.data) {
        await saveTempData(
          response.data.user.CNPJ__c as string,
          response.data.user.CPF__c as string,
          response.data.user.Email__c as string,
          DeviceUtils.deviceInfo()
        );
        setSuccess(true);
        reset();
        setTimeout(() => {
          router.push('/verifica-codigo');
        }, 500);
      }
    } catch (error: any) {
      // Tratamento específico de erros
      if (error.response?.data?.message) {
        console.error(error.response.data.message);
        // toast.error(error.response.data.message);
      } else {
        console.error('Erro ao solicitar redefinição de senha');
        // toast.error('Erro ao solicitar redefinição de senha');
      }
      console.error('Erro no reset de senha:', error);
    } finally {
      setLoading(false);
    }
  });

  // Reenviar código
  const resendResetPassword = async (
    userLogin: string
  ): Promise<{ success: boolean; error?: string }> => {
    setLoading(true);
    try {
      const response = await apiPost<SendResetPasswordResponse>(
        'reset-password',
        {
          userLogin,
          deviceinfo: DeviceUtils.deviceInfo()
        },
        undefined,
        {
          requiresAuth: false
        }
      );

      if (response.success) {
        return { success: true };
      } else {
        return { success: false, error: response.message || 'Erro ao reenviar código' };
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao reenviar código';
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  return {
    register,
    errors,
    loading,
    success,
    handle,
    handleDocumentChange,
    resendResetPassword
  };
}
