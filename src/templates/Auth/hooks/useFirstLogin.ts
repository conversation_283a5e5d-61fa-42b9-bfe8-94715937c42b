/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
// @typescript-eslint/no-unnecessary-type-assertion
import { useRouter } from 'next/navigation';
import { ChangeEvent, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';

import { useLoading } from '@/hooks';
import { DeviceUtils } from '@/utils/DeviceUtils';

import { StringUtils } from '@/utils/StringUtils';
import { useModal } from '../../../contexts/useModal';
import { useApi } from '../../../hooks/useApi';

interface IFormInput {
  userLogin: string;
}

export const useFirstLogin = () => {
  const { getFetch } = useApi();
  const { loading, setLoadingInfo } = useLoading();
  const { toggleModal } = useModal();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue
  } = useForm<IFormInput>();

  const handleDocumentChange = (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, '');
    value = value.slice(0, 14);
    value =
      value.length <= 11
        ? value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, '$1.$2.$3-$4')
        : value.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g, '$1.$2.$3/$4-$5');

    setValue('userLogin', value);
    e.target.value = value;
  };

  const resendWelcomeEmail = async (userLogin: string) => {
    try {
      setLoadingInfo(true, 'Verificando dados...');
      setIsSubmitting(true);

      const result = await getFetch<{
        success: boolean;
        message: string;
      }>({
        method: 'POST',
        route: 'resendWelcomeMail',
        body: { userLogin, deviceinfo: DeviceUtils.deviceInfo() }
      });

      if (result) {
        toggleModal({
          text: result.message,
          params: {
            callback: () => {
              if (result.success) router.push('/login');
            }
          }
        });
      }
    } catch (error) {
      console.log(error);
      toggleModal({
        text: 'Estamos com dificuldades técnicas. Tente novamente em breve.'
      });
    } finally {
      setLoadingInfo(false);
      setIsSubmitting(false);
    }

    return true;
  };

  const handleSubmitForm: SubmitHandler<IFormInput> = async (data) => {
    await resendResetPassword(StringUtils.cleanData(data.userLogin));
    return true;
  };

  const resendResetPassword = async (userLogin: string) => {
    return resendWelcomeEmail(userLogin);
  };

  const handle = handleSubmit(handleSubmitForm);

  return {
    register,
    errors,
    handle,
    loading: loading || isSubmitting,
    resendResetPassword,
    handleDocumentChange
  };
};
