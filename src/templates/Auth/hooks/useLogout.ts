/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
/**
 * Hook para gerenciar o logout do usuário e monitorar os dispositivos conectados
 */

'use client';

import { useModal } from '@/hooks';
import { useData } from '@/hooks/useData';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Importar a server action de logout
import { WhereTokens } from '@/@types/whereTokens';
import { logoutFromDevice } from '@/server/actions/auth';
import { getConnectedDevices } from '@/server/actions/user';
import { clearAuthCookies } from '@/server/services/cookiesService';

export function useLogout() {
  const router = useRouter();
  const { toggleModal } = useModal();
  // const { getFetch } = useApi();
  const { destroyData } = useData();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [tokens, setTokens] = useState<WhereTokens[]>([]);
  const [isLoadingTokens, setIsLoadingTokens] = useState(false);

  /**
   * Busca os dispositivos onde o usuário está logado
   */
  const fetchWhereLogged = async () => {
    const currentDeviceInfo = DeviceUtils.deviceInfo();
    const { devices } = await getConnectedDevices(currentDeviceInfo);
    try {
      setIsLoadingTokens(true);
      if (!devices) return null;
      setTokens(devices);
      return devices;
    } catch (error) {
      console.error('Erro ao buscar dispositivos conectados:', error);
      return { data: { tokens: [] } };
    } finally {
      setIsLoadingTokens(false);
    }
  };

  // Buscar os tokens ao montar o componente
  useEffect(() => {
    fetchWhereLogged();
  }, []);

  /**
   * Realiza o logout do usuário, opcionalmente de um dispositivo específico
   */
  const logout = async ({ deviceinfoname = '' }: { deviceinfoname?: string } = {}) => {
    try {
      setIsLoggingOut(true);
      const actualDevice = deviceinfoname === DeviceUtils.deviceInfo();
      await logoutFromDevice(deviceinfoname);
      if (actualDevice) {
        destroyData();
        toggleModal({
          text: 'Você foi deslogado desse dispositivo e será redirecionado para tela de login em 3 segundos.'
        });
        clearAuthCookies();
        setTimeout(() => {
          router.push('/login');
        }, 1200);
      } else {
        toggleModal({
          text: `Você foi deslogado do dispositivo ${deviceinfoname}.`
        });
        fetchWhereLogged();
      }
    } catch (error) {
      console.error('Erro ao realizar logout:', error);
      toggleModal({
        text: 'Ocorreu um erro ao realizar o logout. Tente novamente.'
      });
    } finally {
      setIsLoggingOut(false);
    }
  };

  return {
    logout,
    isLoggingOut,
    tokens,
    isLoadingTokens,
    refreshTokens: fetchWhereLogged
  };
}
