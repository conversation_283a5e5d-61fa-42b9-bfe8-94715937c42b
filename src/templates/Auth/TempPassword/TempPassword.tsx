/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import Input from '@/templates/components/Commons/inputs/Input';
import Logo from '@/templates/components/Commons/Logo';
import { useEffect, useLayoutEffect } from 'react';
import ButtonsActionsAuth from '../components/ButtonsActionsAuth';
import { useNewPassword } from '../hooks/useNewPassword';

export default function TempPassword() {
  const { register, errors, handle, loading, password } = useNewPassword();

  const { setShowSidebar } = useAppContext();

  useLayoutEffect(() => {
    setShowSidebar(false);
  }, [setShowSidebar]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  return (
    <>
      <div className='w-full flex items-center justify-centers flex-col'>
        <Logo />
        <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
          Senha temporária
        </h3>
        <p className='text-navy-blue text-base text-center tracking-1 leading-5'>
          Identificamos que esta é sua senha temporária. <br />
          Favor inserir sua nova senha nos campos abaixo.
        </p>
        <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 w-full md:w-[80%]'>
          <div className='w-full md:px-8'>
            <form onSubmit={handle} className={'flex flex-col items-center w-full'}>
              <Input
                className={'w-full'}
                {...register('password', { required: 'Senha obrigatória' })}
                type='password'
                label='Nova senha'
                showSavePass={false}
                errors={errors.password ? errors.password.message : null}
              />
              <Input
                className={'w-full'}
                {...register('passwordConfirm', {
                  required: 'Confirmação de senha obrigatória',
                  validate: (value) => value === password || 'As senhas não coincidem'
                })}
                type='password'
                label='Confirmar nova senha'
                showSavePass={false}
                errors={errors.passwordConfirm ? errors.passwordConfirm.message : null}
              />
              <ButtonsActionsAuth loading={loading} />
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
