/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useModal } from '@/contexts/useModal';
import { useLoading } from '@/hooks';
import { loginAction } from '@/server/actions/auth';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
import SubmitButton from '@/templates/components/Commons/buttons/SubmitButton';
import Input from '@/templates/components/Commons/inputs/Input';
import Logo from '@/templates/components/Commons/Logo';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import Form from 'next/form';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useActionState, useCallback, useEffect, useState } from 'react';

export default function LoginScreen() {
  const router = useRouter();
  const { toggleModal } = useModal();
  const { setLoadingInfo } = useLoading();

  const initialState = {
    success: false,
    error: '',
    isFirstLogin: false,
    needsVerification: false,
    redirectTo: '',
    tokenData: null
  };

  const [state, formAction, isPending] = useActionState(loginAction, initialState);
  const [userLogin, setUserLogin] = useState('');

  const handleDocumentChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, '').slice(0, 14);
    if (value.length <= 11) value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    else value = value.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    setUserLogin(value);
    e.target.value = value;
  }, []);

  useEffect(() => {
    setLoadingInfo(false);
    BrowserUtils.ActionReactNative({ type: 'checkBiometricAccess' });
  }, []);

  useEffect(() => {
    if (state.error) {
      toggleModal({
        text: `${state.error}<br>Se é seu primeiro acesso, <a href="/primeiro-acesso">clique aqui</a>.`
      });
      setLoadingInfo(false);
    } else if (state.success) {
      setLoadingInfo(true);
      if (state.tokenData && DeviceUtils.isMobileApp() && window.ReactNativeWebView) {
        BrowserUtils.ActionReactNative({
          type: 'saveToken',
          data: JSON.stringify(state.tokenData)
        });
      }

      if (state.isFirstLogin) {
        toggleModal({
          text: 'Notamos que é seu primeiro acesso.<br>Estamos felizes de te ver por aqui.<br>Aguarde enquanto atualizamos seus dados.',
          params: { onClose: false, showSpinner: true }
        });
        setTimeout(() => state.redirectTo && router.push(state.redirectTo), 300);
      } else if (state.needsVerification) {
        setTimeout(() => {
          toggleModal({
            text: 'Seus dados foram atualizados com sucesso.',
            params: { onClose: true, showButtonDefinitivePassword: true, showSpinner: false }
          });
          if (state.redirectTo) router.push(state.redirectTo);
        }, 200);
      } else if (state.redirectTo) {
        router.push(state.redirectTo);
      }
    }
  }, [state]);

  return (
    <div className='w-full flex items-center justify-center flex-col'>
      <Logo />
      <h3 className='text-navy-blue text-2xl text-center tracking-wide font-bold mt-6 mb-6'>
        Olá, seja bem-vindo.
      </h3>
      <div className='bg-white rounded flex justify-center px-4 py-4 mt-4 w-full md:w-[80%]'>
        <div className='w-full md:px-8'>
          <Form action={formAction}>
            <input
              type='hidden'
              name='deviceinfo'
              value={JSON.stringify(DeviceUtils.deviceInfo())}
            />
            <input
              type='hidden'
              name='rememberMe'
              // value={getData({ id: 'savePass' }) ? 'true' : 'false'}
              value={'false'}
            />
            <Input
              label='CPF/CNPJ (somente números)'
              name='userLogin'
              type='text'
              value={userLogin}
              onChange={handleDocumentChange}
              required
            />
            <Input type='password' name='password' label='Senha' required showSavePass={true} />
            <Link
              prefetch={false}
              href={`${process.env.NEXT_PUBLIC_SITE_URL}/esqueci-minha-senha`}
              className='mt-4 text-blue-secondary underline tracking-wide text-sm flex my-5 -mb-3'>
              Esqueci minha senha
            </Link>
            <div className='flex flex-col items-center justify-center gap-2 mt-4'>
              <div className='w-[300px] max-md:w-full flex justify-center'>
                <SubmitButton isPending={isPending} />
              </div>
              <div className='w-[300px] max-md:w-full flex justify-center items-center'>
                <Buttontransparent
                  text='Primeiro acesso'
                  color='navy-blue'
                  route={`${process.env.NEXT_PUBLIC_SITE_URL}/primeiro-acesso`}
                  classExtra='w-full flex justify-center items-center md:justify-start md:items-start'
                />
              </div>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}
