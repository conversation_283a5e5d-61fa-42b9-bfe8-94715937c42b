/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';

interface ButtonsActionsAuthProps {
  loading: boolean;
}

export default function ButtonsActionsAuth({ loading }: ButtonsActionsAuthProps) {
  return (
    <div className='w-full flex justify-between items-center'>
      <div className='w-[45%] flex justify-center items-center'>
        <Buttontransparent
          classExtra='w-full max-w-[370px]'
          text='Voltar'
          color='navy-blue'
          route={'/login'}
        />
      </div>
      <div className='w-[45%] flex justify-center items-center'>
        <Buttonblue
          disabled={loading}
          text='Enviar'
          background='navy-blue'
          color='white'
          type='submit'
        />
      </div>
    </div>
  );
}
