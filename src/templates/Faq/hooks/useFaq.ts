/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use server';

import { ArticlesResponse } from '@/@types/articles';
import { apiGet } from '@/server/services/api';

/**
 * Busca os artigos de FAQ da API
 * @returns {Promise<ArticlesResponse>} Resposta contendo os artigos
 */
export const FetchFaq = async (): Promise<ArticlesResponse> => {
  return apiGet('knowledge/Assuntos');
};
