/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimatePresence, motion } from "motion/react"
import Image from 'next/image';

import { AnimateFadeInOpenHeight } from '@/constants/ANIMATIONS';
import { useLoading } from '@/hooks';

import { Article } from '@/@types/articles';
import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { Suspense, useEffect, useLayoutEffect, useState } from 'react';
import HtmlExternalComponent from '../components/Commons/external/HtmlExternalComponent';
import SpinnerLoading from '../components/Commons/Loading/SpinnerLoading';
import TitlePage from '../components/Commons/TitlePage';
import { FetchFaq } from './hooks/useFaq';

export default function FaqScreen() {
  const { canBeRender } = useCanBeRender();
  const [articles, setArticles] = useState<Article[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showQuestion, setShowQuestion] = useState(-1);

  const { setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Dúvidas Frequentes');
    setShowSidebar(!isMobile);
  }, [isMobile, setSubtitleHeaderMobile, setShowSidebar]);

  const { setLoadingInfo } = useLoading();

  useEffect(() => {
    const loadFaqData = async () => {
      try {
        setIsLoading(true);
        const response = await FetchFaq();
        // setArticles(response.data as Article[]);
        setArticles(response.data);
      } catch (error) {
        console.error('Erro ao carregar FAQ:', error);
      } finally {
        setIsLoading(false);
        setLoadingInfo(false);
      }
    };

    loadFaqData();
  }, []);

  const conditions = [isMobile === null, isLoading];
  if (!canBeRender({ conditions })) return null;
  if (isLoading) return <SpinnerLoading />;
  if (!articles) return null;

  return (
    <>
      <Suspense fallback={<SpinnerLoading />}>
        <TitlePage text='Dúvidas Frequentes' />
        {articles.map((article, index) => (
          <div className='w-full' key={article.KnowledgeArticleId}>
            <button
              type='button'
              className='cursor-pointer w-full h-12 py-8 bg-white border-l-15 border-neutral-blue rounded flex flex-row items-center justify-between px-4 mt-3'
              onClick={() => setShowQuestion(showQuestion !== -1 ? -1 : index)}>
              <p className='text-base text-navy-blue tracking-1 leading-4 text-left pr-5'>
                {article.Title}
              </p>
              {showQuestion === index ? (
                <Image
                  src='/images/less.svg'
                  alt='Icon'
                  className='rounded-full'
                  width={17}
                  height={17}
                  priority
                />
              ) : (
                <Image
                  src='/images/more.svg'
                  alt='Icon'
                  className='rounded-full'
                  width={17}
                  height={17}
                  priority
                />
              )}
            </button>
            <AnimatePresence>
              {showQuestion === index && (
                <motion.div
                  initial='collapsed'
                  animate={showQuestion === index ? 'open' : 'collapsed'}
                  variants={AnimateFadeInOpenHeight}
                  exit='exit'
                  transition={{ duration: 0.3, ease: 'easeInOut' }}>
                  <div className='bg-white pt-4 px-4 pb-6 rounded'>
                    <HtmlExternalComponent
                      html={article.Assunto__c}
                      className='text-sm text-navy-blue tracking-1 mb-1 mt-4'
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </Suspense>
    </>
  );
}
