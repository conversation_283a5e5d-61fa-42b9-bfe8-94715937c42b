/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { SubmitHandler, useForm } from 'react-hook-form';

import { useAppContext } from '@/contexts/AppContext';
import { useModal } from '@/contexts/useModal';
import { useApi } from '@/hooks/useApi';
import { useLoading } from '@/hooks/useLoading';
import { apiPost } from '@/server/services/api';

interface IFormInput {
  username: string;
  usermail: string;
  userphone: string;
  nome: string;
  email: string;
  telefone: string;
  estadoInteresse: string;
  cidadeInteresse: string;
  message: string;
}
export const useSendReferAFriend = () => {
  const { user } = useAppContext();
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset
  } = useForm<IFormInput>({
    values: {
      username: user?.Name as string,
      usermail: user?.Email__c as string,
      userphone: user?.TelefoneCelular__c as string,
      nome: '',
      email: '',
      telefone: '',
      estadoInteresse: '',
      cidadeInteresse: '',
      message: ''
    }
  });
  const { toggleModal } = useModal();
  const { setLoadingInfo } = useLoading();
  // const { getFetch } = useApi();  

  const handleSubmitForm: SubmitHandler<IFormInput> = async (data) => {
    setLoadingInfo(true, 'Indicando seu amigo... Aguarde.');

    try {
      const response = await apiPost<{ data: { success: boolean } }>('sendReferAFriend', data);
      // const response = await getFetch<{ data: { success: boolean } }>({
      //   method: 'POST',
      //   route: 'sendReferAFriend',
      //   body: data as unknown as Record<string, string | number | boolean | object>
      // });
      setLoadingInfo(false);

      if (response?.data) {
        toggleModal({ text: 'Amigo indicado com sucesso!' });
        reset();
      } else {
        toggleModal({
          text: 'Ops... Algo de errado aconteceu. Tente novamente mais tarde.'
        });
      }
      return response?.data;
    } catch (err: unknown) {
      setLoadingInfo(false);
      return false;
    }
  };

  return {
    register,
    handleSubmit,
    handleSubmitForm,
    setValue,
    errors
  };
};
