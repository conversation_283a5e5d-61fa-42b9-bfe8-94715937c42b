/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useEffect, useLayoutEffect } from 'react';
import ReferAFriendContent from '../content/ReferAFriendContent';

export default function ReferAFriendScreen() {
  const { isMobile, setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Indique um amigo');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (isMobile === null) return null;
  return <ReferAFriendContent />;
}
