/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import CustomDropdown from '@/templates/components/Commons/inputs/CustomDropdown';
import Input from '@/templates/components/Commons/inputs/Input';
import { useSendReferAFriend } from '../ReferAFriend.tsx/hooks/useSendReferAFriend';

export default function ReferAFriendContent() {
  const { handleSubmit, register, handleSubmitForm, setValue, errors } = useSendReferAFriend();

  function onChangeState(value: string | number): void {
    setValue('estadoInteresse', value.toString());
  }

  return (
    <>
      <p className='text-sm text-navy-blue tracking-1 mb-4'>
        Compartilhe essa realização e indique seus amigos:
      </p>
      <hr className='border-t-1 border-white mt-4 mb-5 w-full' />
      <form onSubmit={handleSubmit(handleSubmitForm)}>
        <div className='md:grid md:grid-cols-3 md:grid-rows-2 md:gap-3'>
          <Input
            {...register('nome', {
              required: 'O Nome do seu amigo é obrigatório. '
            })}
            errors={errors.nome ? errors.nome.message : null}
            label='Nome do amigo*:'
            type='text'
          />
          <Input
            {...register('email', {
              required: 'O e-mail do seu amigo é obrigatório. '
            })}
            errors={errors.email ? errors.email.message : null}
            label='E-mail do amigo*'
            type='text'
          />

          <Input
            {...register('telefone', {
              required: 'O telefone do seu amigo é obrigatório. '
            })}
            errors={errors.telefone ? errors.telefone.message : null}
            type='number'
            label='Telefone do amigo*:'
          />

          <CustomDropdown
            label={'Estado de interesse:'}
            optionsExternal={[
              { id: 0, value: '', name: 'Estado de interesse:' },
              { id: 1, value: 'São Paulo', name: 'São Paulo' },
              { id: 2, value: 'Rio de Janeiro', name: 'Rio de Janeiro' }
            ]}
            onChange={(value) => onChangeState(value.toString())}
            defaultValue='Estado de interesse:'
          />
          <Input {...register('cidadeInteresse')} type='text' label='Cidade de interesse:' />
        </div>

        <Input {...register('message')} type='textarea' label='Mensagem:' rows={4} />

        <div className='-mt-3 mb-7  flex justify-center'>
          <Buttonblue background='navy-blue' color='white' type='submit' text='Enviar' />
        </div>
      </form>
    </>
  );
}
