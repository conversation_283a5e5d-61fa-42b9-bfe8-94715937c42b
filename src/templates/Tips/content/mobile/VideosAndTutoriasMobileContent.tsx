/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import CustomDropdown from '@/templates/components/Commons/inputs/CustomDropdown';
import React, { useEffect } from 'react';
import { useVideos } from '../../hooks/useVideos';
import { useLoading } from '@/hooks';
import 'swiper/css';
import 'swiper/css/navigation';

export default function VideosAndTutoriasMobileContent() {
  const { error, filterVideoCategoria, videosFiltrados, optionsCategorias } = useVideos();

  if (error) {
    console.error('Error:', error.message);
  }

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  return (
    <>
      {error && <p>{error.message}</p>}
      <div className='mb-7 select-container'>
        <p className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3'>Categorias</p>
        {optionsCategorias.length > 0 ? (
          <CustomDropdown
            optionsExternal={optionsCategorias}
            onChange={(value) => filterVideoCategoria(value as string)}
          />
        ) : null}
      </div>
      <div className='relative'>
        <div key={'Swipervideos'} className='mySwiper-videos md:w-[85%]'>
          {videosFiltrados &&
            videosFiltrados.map((item) => (
              <React.Fragment key={item.id}>
                {item.videos.map((video) => (
                  <div className='min-h-[200px]' key={video.UrlEmbutida__c}>
                    <iframe
                      width='100%'
                      height='180'
                      src={video.UrlEmbutida__c}
                      title={video.Name}
                      allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'></iframe>
                  </div>
                ))}
              </React.Fragment>
            ))}
        </div>
      </div>
    </>
  );
}
