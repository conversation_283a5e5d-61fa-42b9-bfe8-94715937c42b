/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import 'swiper/css';
import 'swiper/css/navigation';

import { useLoading } from '@/hooks';

import { useTips } from '../../hooks/useTips';

import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import CustomLink from '@/templates/components/Commons/buttons/CustomLink';
import { Suspense, useEffect } from 'react';

export default function TipsMobileContent() {
  const { user, content } = useAppContext();
  const { canBeRender } = useCanBeRender();

  const { error, articles } = useTips();

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [content === null];

  if (!canBeRender({ conditions })) return null;

  const linkBase =
    content?.type === 'morador'
      ? `/${user?.AccountId}/dicas/`
      : `/${user?.AccountId}/sindico/dicas/`;

  return (
    <>
      {error && <p>{error.message}</p>}
      <Suspense fallback={<p>Carregando ...</p>}>
        <p className='text-sm text-navy-blue tracking-1 mb-4'>
          Dicas Rápidas Para Facilitar Sua Vida
        </p>
        <div className='relative w-full mt-6 md:mt-0'>
          <div className='mySwiper-tips md:w-[85%] md:w-max-[85%]'>
            {articles.map((item, index) => (
              <div className='min-h-[200px]' key={item.Assunto__c}>
                <CustomLink
                  href={`${linkBase}${index}`}
                  className='w-full h-full min-h-[150px] md:min-h-[250px] flex justify-center items-center p-y-5 px-10 bg-gray-50 text-center rounded-md text-navy-blue'>
                  {item.Title}
                </CustomLink>
              </div>
            ))}
          </div>
        </div>
      </Suspense>
    </>
  );
}
