/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useLoading, useMediaQuery } from '@/hooks';
import { useInfoApps } from '../../hooks/useInfoApps';

import { FILES_URL } from '@/constants';
import ImageLoad from '@/templates/components/Commons/ImageLoad';
import dynamic from 'next/dynamic';
import { useEffect } from 'react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/scrollbar';
const InfoAppDetails = dynamic(() => import('../InfoAppDetails'));

export default function InfoAppsMobileContent() {
  const { showFoto, toggleFoto, linkApp, error, articles } = useInfoApps();
  const { isMobile } = useMediaQuery();

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (isMobile === null) return null;
  return (
    <>
      {error && <p className='text-red-600'>{error.message}</p>}
      <p className='text-sm text-navy-blue tracking-1 mb-4'>
        Descubra aplicativos selecionados especialmente para você!
      </p>

      <div key={'divapps'} className='  w-full md:w-[85%] h-auto min-h-auto relative flex flex-col'>
        {articles.map((article, index) => {
          const urlimg = article.Imagem__c
            ? `${FILES_URL}${article.Imagem__c}.webp`
            : '/images/apppadrao.webp';
          return (
            <div
              key={`apps_${urlimg}`}
              className='flex justify-between items-center w-full h-full min-h-[auto] flex-col mb-4'>
              <button
                className={' w-full h-full  bg-white  p-4 rounded flex-col flex md:h-[240px] md:overflow-hidden justify-between items-center'}
                onClick={() => toggleFoto(index)}>
                <ImageLoad
                  imageUrl={urlimg}
                  key={urlimg}
                  customClass='relative w-full h-full object-contain opacity-100'
                />
              </button>
              <p className='flex w-full justify-center items-center text-center text-sm text-navy-blue tracking-1 mt-2'>
                {article.Title}
              </p>
            </div>
          );
        })}
      </div>

      <InfoAppDetails
        showFoto={showFoto}
        toggleFoto={toggleFoto}
        linkApp={linkApp as string}
        article={articles[showFoto]}
      />
    </>
  );
}
