/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Article } from '@/@types/articles';
import { FILES_URL } from '@/constants';
import HtmlExternalComponent from '@/templates/components/Commons/external/HtmlExternalComponent';
// import { API_URL } from '@/constants';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import Image from 'next/image';

import 'swiper/css';
import 'swiper/css/navigation';

interface InfoAppDetailsProps {
  showFoto: number;
  toggleFoto: (value: number) => void;
  linkApp: string;
  article: Article;
}

export default function InfoAppDetails({
  showFoto,
  toggleFoto,
  linkApp,
  article
}: InfoAppDetailsProps) {
  if (article === null) return null;
  return (
    <>
      {showFoto !== -1 && (
        <motion.div
          key='loading'
          initial={{ opacity: showFoto !== -1 ? 0 : 1 }}
          animate={{ opacity: showFoto !== -1 ? 1 : 0 }}
          exit={{ opacity: showFoto !== -1 ? 0 : 1 }}
          transition={{
            duration: 0.4,
            ease: 'easeInOut'
          }}
          className='absolute  bg-navy-blue bg-opacity-90 w-full h-full  inset-0 p-4 flex flex-col z-[1000000000000000] justify-center items-center overflow-auto'>
          {/* <div className='bg-navy-blue bg-opacity-90 w-full h-full fixed inset-0 p-4 flex flex-col z-[1000000000000000] justify-center items-center'> */}

          <div className=' w-full h-auto max-w-[1000px] relative inset-0 p-4 flex flex-col items-center justify-center'>
            <div className='bg-white rounded relative p-10 flex flex-col items-center justify-center'>
              <button
                className='bg-transparent absolute top-1 right-1 flex justify-center items-center'
                onClick={() => toggleFoto(-1)}>
                <Image
                  src='/images/close.png'
                  alt='Icon'
                  className='w-auto h-auto'
                  width={30}
                  height={30}
                  priority
                />
              </button>
              <h3 className='font-bold text-3xl text-navy-blue tracking-1 mb-8  text-center'>
                {article.Title}
              </h3>
              <Image
                src={
                  article.Imagem__c
                    ? `${FILES_URL}${article.Imagem__c}.webp`
                    : '/images/apppadrao.webp'
                }
                alt='Foto'
                className='w-full max-w-[400px] rounded'
                width={300}
                height={300}
                priority
              />
              {/* <p
                className={`text-sm text-navy-blue tracking-1 mt-8 text-center w-full md:w-[70%]`}
                dangerouslySetInnerHTML={{
                  __html: article.Assunto__c
                }}
              /> */}
              <HtmlExternalComponent
                html={article.Assunto__c}
                className='text-sm text-navy-blue tracking-1 mt-8 text-center w-full md:w-[70%]'
              />
              {linkApp ? (
                <Buttontransparent
                  text='Ir para a página'
                  color='navy-blue'
                  route={linkApp}
                  target='_blank'
                />
              ) : null}
            </div>
          </div>
        </motion.div>
      )}
    </>
  );
}
