/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useLoading, useMediaQuery } from '@/hooks';
import CustomDropdown from '@/templates/components/Commons/inputs/CustomDropdown';
import React, { useEffect, useState } from 'react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Controller, Navigation } from 'swiper/modules';
import { Swiper, SwiperClass, SwiperSlide } from 'swiper/react';
import { useVideos } from '../../hooks/useVideos';

export default function VideosAndTutoriasDeskContent() {
  const { error, filterVideoCategoria, videosFiltrados, optionsCategorias } = useVideos();
  const { isMobile } = useMediaQuery();
  const [videoSwiper, setVideoSwiper] = useState<SwiperClass | null>(null);

  if (error) {
    console.error('Error:', error.message);
  }

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (isMobile === null) return null;

  return (
    <>
      {error && <p>{error.message}</p>}
      <div className='mb-7 select-container'>
        <p className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3'>Categorias</p>
        {optionsCategorias.length > 0 ? (
          <CustomDropdown
            optionsExternal={optionsCategorias}
            onChange={(value) => filterVideoCategoria(value as string | number)}
          />
        ) : null}
      </div>
      <div className='relative'>
        <Swiper
          key={'Swipervideos'}
          modules={[Navigation, Controller]}
          // onSwiper={setVideoSwiper}
          onSwiper={(swiper) => setVideoSwiper(swiper)}
          controller={{ control: videoSwiper }}
          navigation={{
            nextEl: '.swiper-button-next-videos',
            prevEl: '.swiper-button-prev-videos'
          }}
          direction={isMobile ? 'vertical' : 'horizontal'}
          className='mySwiper-videos md:w-[85%]'
          slidesPerView={isMobile ? 10 : 3}
          spaceBetween={20}>
          {videosFiltrados &&
            videosFiltrados.map((item) => (
              <React.Fragment key={item.id}>
                {item.videos.map((video) => (
                  <SwiperSlide className='min-h-[200px]' key={video.UrlEmbutida__c}>
                    <iframe
                      width='100%'
                      height='180'
                      src={video.UrlEmbutida__c}
                      title={video.Name}
                      allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'></iframe>
                  </SwiperSlide>
                ))}
              </React.Fragment>
            ))}
        </Swiper>
        <div className='swiper-button-next swiper-button-next-videos text-navy-blue bg-white p-6 custom-swiper-button'></div>
        <div className='swiper-button-prev swiper-button-prev-videos text-navy-blue bg-white p-6 custom-swiper-button'></div>
      </div>
    </>
  );
}
