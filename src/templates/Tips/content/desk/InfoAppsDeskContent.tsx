/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Controller, Navigation } from 'swiper/modules';
// import type { SwiperOptions, Swiper as SwiperClass } from './types/index.d.ts';
import { useEffect, useState } from 'react';
import { Swiper, SwiperClass, SwiperSlide } from 'swiper/react';

import { useLoading, useMediaQuery } from '@/hooks';
import ImageLoad from '@/templates/components/Commons/ImageLoad';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/scrollbar';
import { useInfoApps } from '../../hooks/useInfoApps';

import { FILES_URL } from '@/constants';
import InfoAppDetails from '../InfoAppDetails';

export default function InfoAppsDeskContent() {
  const { showFoto, toggleFoto, linkApp, error, articles } = useInfoApps();
  const { isMobile } = useMediaQuery();
  const [infoAppsSwiper, setInfoAppsSwiper] = useState<SwiperClass | null>(null);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (isMobile === null) return null;
  return (
    <>
      {error && <p className='text-red-600'>{error.message}</p>}
      <p className='text-sm text-navy-blue tracking-1 mb-4'>
        Descubra aplicativos selecionados especialmente para você!
      </p>

      <div className='relative'>
        <Swiper
          key={'Swiperapps'}
          modules={[Navigation, Controller]}
          onSwiper={(swiper) => setInfoAppsSwiper(swiper)}
          controller={{ control: infoAppsSwiper }}
          navigation={{
            nextEl: '.swiper-button-next-infoapps',
            prevEl: '.swiper-button-prev-infoapps'
          }}
          direction={isMobile ? 'vertical' : 'horizontal'}
          className='mySwiper-apps w-full md:w-[85%] h-auto  flex justify-center items-center'
          slidesPerView={isMobile ? 20 : 3}
          spaceBetween={20}>
          {articles.map((article, index) => {
            const urlimg = article.Imagem__c
              ? `${FILES_URL}${article.Imagem__c}.webp`
              : '/images/apppadrao.webp';
            return (
              <SwiperSlide
                key={`apps_${urlimg}`}
                className='!flex flex-col !justify-center !items-center w-full h-full min-h-[350px] '>
                <button
                  className={' w-full h-full  bg-white  p-4 rounded flex md:h-[240px]  justify-between items-center'}
                  onClick={() => toggleFoto(index)}>
                  <ImageLoad
                    imageUrl={urlimg}
                    key={urlimg}
                    customClass='relative w-full h-auto object-contain  max-w-full max-h-full opacity-100 flex justify-center items-center'
                  />
                </button>
                <p className='flex w-full justify-center items-center text-center text-sm text-navy-blue tracking-1 mt-2'>
                  {article.Title}
                </p>
              </SwiperSlide>
            );
          })}
        </Swiper>
        <div className='swiper-button-next swiper-button-next-infoapps text-navy-blue bg-white p-6 custom-swiper-button'></div>
        <div className='swiper-button-prev swiper-button-prev-infoapps  text-navy-blue bg-white p-6 custom-swiper-button'></div>
      </div>

      <InfoAppDetails
        showFoto={showFoto}
        toggleFoto={toggleFoto}
        linkApp={linkApp as string}
        article={articles[showFoto]}
      />
    </>
  );
}
