/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import 'swiper/css';
import 'swiper/css/navigation';

import { useLoading, useMediaQuery } from '@/hooks';

import { useTips } from '../../hooks/useTips';

import { Swiper, SwiperClass, SwiperSlide } from 'swiper/react';

import { useAppContext } from '@/contexts/AppContext';
import CustomLink from '@/templates/components/Commons/buttons/CustomLink';
import { Suspense, useEffect, useState } from 'react';
import { Controller, Navigation } from 'swiper/modules';

export default function TipsDeskContent() {
  const [tipsSwiper, setTipsSwiper] = useState<SwiperClass | null>(null);
  const { user, content } = useAppContext();
  const linkBase =
    content?.type === 'morador'
      ? `/${user?.AccountId}/dicas/`
      : `/${user?.AccountId}/sindico/dicas/`;
  const { error, articles } = useTips();

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const { isMobile } = useMediaQuery();
  if (isMobile === null) return null;
  return (
    <>
      {error && <p>{error.message}</p>}
      <Suspense fallback={<p>Carregando ...</p>}>
        <div className='relative w-full mt-6 md:mt-0'>
          <Swiper
            modules={[Navigation, Controller]}
            // onSwiper={setTipsSwiper}
            onSwiper={(swiper) => setTipsSwiper(swiper)}
            controller={{ control: tipsSwiper }}
            navigation={{
              nextEl: '.swiper-button-next-tips',
              prevEl: '.swiper-button-prev-tips'
            }}
            watchSlidesProgress={true}
            direction={isMobile ? 'vertical' : 'horizontal'}
            className='mySwiper-tips md:w-[85%]'
            slidesPerView={isMobile ? 10 : 3}
            spaceBetween={isMobile ? 40 : 20}>
            {articles.map((item, index) => (
              <SwiperSlide className='min-h-[200px]' key={item.Assunto__c}>
                <CustomLink
                  href={`${linkBase}${index}`}
                  className='w-full h-full min-h-[150px] md:min-h-[250px] flex justify-center items-center p-y-5 px-10 bg-gray-50 text-center rounded-md text-navy-blue'>
                  {item.Title}
                </CustomLink>
              </SwiperSlide>
            ))}
          </Swiper>
          <div className='swiper-button-next swiper-button-next-tips text-navy-blue bg-white p-6 custom-swiper-button'></div>
          <div className='swiper-button-prev swiper-button-prev-tips  text-navy-blue bg-white p-6 custom-swiper-button'></div>
        </div>
      </Suspense>
    </>
  );
}
