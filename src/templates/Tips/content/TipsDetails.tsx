/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useLoading, useMediaQuery } from '@/hooks';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';

import { FILES_URL } from '@/constants';

import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import HtmlExternalComponent from '@/templates/components/Commons/external/HtmlExternalComponent';
import ImageLoad from '@/templates/components/Commons/ImageLoad';
import { useEffect, useLayoutEffect } from 'react';
import { useTips } from '../hooks/useTips';

interface MessageScreenProps {
  id: number;
}
export default function TipsDetails({ id }: Readonly<MessageScreenProps>) {
  const { user, setSubtitleHeaderMobile, setShowSidebar, content } = useAppContext();
  const { canBeRender } = useCanBeRender();
  const { articles } = useTips();
  const { isMobile } = useMediaQuery();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Dicas');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const article = articles[id];
  const conditions = [
    isMobile === null,
    article === null,
    article === undefined,
    articles === null,
    content === null
  ];

  if (!canBeRender({ conditions })) return null;

  const linkBase =
    content?.type === 'morador'
      ? `/${user?.AccountId}/dicas/`
      : `/${user?.AccountId}/sindico/dicas/`;

  return (
    <>
      <div
        className={' flex-row items-center px-5 rounded py-3 flex bg-white w-full md:w-max-full mb-10'}>
        <div className='flex flex-col items-center w-full md:w-max-full'>
          <h3 className='text-navy-blue text-2xl text-center tracking-1 font-bold mt-7 mb-4'>
            {article.Title}
          </h3>
          {article.Imagem__c && (
            <div className='w-full'>
              <ImageLoad
                imageUrl={`${FILES_URL}${article.Imagem__c}.webp`}
                customClass={'w-full'}
              />
            </div>
          )}
          {/* <p
            className={`flex border-t border-light-gray pt-4 mt-1 px-5 text-navy-blue font-medium tracking-1 text-base mb-2 flex-col w-full max-w-full`}
            dangerouslySetInnerHTML={{ __html: article.Assunto__c }}
          /> */}
          <HtmlExternalComponent
            html={article.Assunto__c}
            className={'flex border-t border-light-gray pt-4 mt-1 px-5 text-navy-blue font-medium tracking-1 text-base mb-2 flex-col w-full max-w-full'}
          />
          <div className='flex justify-center w-full md:w-1/4 items-center'>
            <Buttonblue
              text='Voltar'
              background='navy-blue'
              color='white'
              classExtra='w-full'
              route={linkBase}
            />
          </div>
        </div>
      </div>
    </>
  );
}
