 
/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useCallback, useEffect, useState } from 'react';

import { Article, ArticlesResponse } from '@/@types/articles';
import { useApi } from '@/hooks/useApi';
import { apiGet } from '@/server/services/api';

export const useTips = () => {
  const [error] = useState<Error | null>(null);

  const [articles, setArticles] = useState<Article[]>([]);
  // const { getFetch } = useApi();

  const loadData = useCallback(async () => {
    const response = await apiGet<ArticlesResponse>('knowledge/Dicas');
    // const response = await getFetch<ArticlesResponse>({  
    //   method: 'GET',
    //   route: 'knowledge/Dicas'
    // });

    if (!response?.data) {
      return response;
    } else {
      setArticles(response.data);
    }

    return true;
  }, []);

  useEffect(() => {
    loadData();
  }, []);

  return {
    error,
    articles
  };
};
