 
/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useEffect, useState } from 'react';

import { Article, ArticlesResponse } from '@/@types/articles';
import { useApi } from '@/hooks/useApi';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { apiGet } from '@/server/services/api';

export const useInfoApps = () => {
  const [showFoto, setShowFoto] = useState(-1);
  const [deviceType, setDeviceType] = useState('');
  const [linkApp, setLinkApp] = useState<string | null>('');
  const [error, setError] = useState<Error | null>(null);
  const [articles, setArticles] = useState<Article[]>([]);
  const { getFetch } = useApi();

  useEffect(() => {
    const loadData = async () => {
      const response = await apiGet<ArticlesResponse>('knowledge/Informações de Apps') ;
      // const response = await apiGet<ArticlesResponse>({
      //   method: 'GET',
      //   route: 'knowledge/Informações de Apps'
      // });
      // const response = await getFetch<ArticlesResponse>({
      //   method: 'GET',
      //   route: 'knowledge/Informações de Apps'
      // });
      if (!response?.data) {
        // setError(response);
        return response;
      } else {
        setArticles(response.data);
      }
      return response.data;
    };

    loadData();

    setDeviceType(DeviceUtils.isIphone() ? 'ios' : 'android');
  }, []);

  useEffect(() => {
    let link = null;
    setLinkApp(link);
    if (showFoto === -1) return;
    if (deviceType === 'ios') {
      link = articles[showFoto].Link_Apple__c ? articles[showFoto].Link_Apple__c : null;
    } else {
      link = articles[showFoto].Link_Google__c ? articles[showFoto].Link_Google__c : null;
    }
    setLinkApp(link);
  }, [showFoto, articles, deviceType]);
  const toggleFoto = (value: number) => {
    if (value === -1) {
      BrowserUtils.handleBodyScroll(true);
    } else {
      BrowserUtils.handleBodyScroll(false);
    }
    setShowFoto(value);
  };
  return {
    showFoto,
    toggleFoto,
    deviceType,
    setDeviceType,
    linkApp,
    setLinkApp,
    error,
    setError,
    articles,
    setArticles
  };
};
