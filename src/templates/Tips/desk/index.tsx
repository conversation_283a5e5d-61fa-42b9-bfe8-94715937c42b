/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading, useMediaQuery } from '@/hooks';
import TitlePage from '@/templates/components/Commons/TitlePage';
import { useEffect } from 'react';
import ReferAFriendContent from '../content/ReferAFriendContent';
import InfoAppsDeskContent from '../content/desk/InfoAppsDeskContent';
import TipsContent from '../content/desk/TipsDeskContent';
import VideosAndTutoriasContent from '../content/desk/VideosAndTutoriasDeskContent';

export default function TipsMenuDeskScreen() {
  const { content, setShowSidebar } = useAppContext();
  const { isMobile } = useMediaQuery();

  useEffect(() => {
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (isMobile === null || content === null) return null;
  return (
    <>
      <div className='max-md:hidden'>
        <TitlePage text='Dicas' />

        <div className='w-full mt-10'>
          <TipsContent />
        </div>
        {content?.type === 'morador' ? (
          <>
            <div className='w-2/3 mt-10'>
              <h4 className='text-navy-blue font-medium text-xl tracking-1 max-md:hidden'>
                Vídeos e tutoriais
              </h4>
            </div>

            <div className='w-full'>
              <VideosAndTutoriasContent />
            </div>
            <div className='w-2/3 mt-10'>
              <h4 className='text-navy-blue font-medium text-xl tracking-1 max-md:hidden'>
                Informações de Apps
              </h4>
            </div>
            <div className='w-full'>
              <InfoAppsDeskContent />
            </div>

            <div className='w-2/3 mt-10'>
              <h4 className='text-navy-blue font-medium text-xl tracking-1 max-md:hidden'>
                Indique um amigo
              </h4>
            </div>
            <div className='w-full'>
              <ReferAFriendContent />
            </div>
          </>
        ) : null}
      </div>
    </>
  );
}
