/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import ButtonImage from '@/templates/components/Commons/buttons/ButtonImage';

export default function TipsMenuMobileScreen() {
  const { user } = useAppContext();
  if (user === null) return null;
  return (
    <>
      <div className='h-auto'>
        <ButtonImage
          text='Dicas'
          src='/images/dicas.png'
          route={`/${user?.AccountId}/dicas/dicas-m`}
        />
        <ButtonImage
          text='Vídeos e Tutoriais'
          src='/images/video.png'
          route={`/${user?.AccountId}/dicas/videos-e-tutoriais`}
        />
        <ButtonImage
          text='Indique um Amigo'
          src='/images/indique-amigo.png'
          route={`/${user?.AccountId}/dicas/indique-um-amigo`}
        />
        <ButtonImage
          text='Informações de Apps'
          src='/images/apps.png'
          route={`/${user?.AccountId}/dicas/informacoes-de-apps`}
        />
      </div>
    </>
  );
}
