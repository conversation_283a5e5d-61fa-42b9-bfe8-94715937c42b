/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import cep from 'cep-promise';
import { useState } from 'react';
import { SubmitHandler } from 'react-hook-form';

import { AvatarResponse, User } from '@/@types/user';
import { FILES_URL } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { useModal } from '@/contexts/useModal';
import { useLoading } from '@/hooks/useLoading';
import { updateUserProfile } from '@/server/actions/user';
import { apiPost } from '@/server/services/api';

interface AddressData {
  neighborhood: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
}

interface UserUpdateResponse {
  alternative_name: string;
  Name: string;
  FirstName: string;
  LastName: string;
  Email__c: string;
  TelefoneCelular__c: string;
  TelefoneCelular2__c: string;
  TelefoneComercial__c: string;
  TelefoneFixo__c: string;
  CPF__c: string;
  CNPJ__c: string;
  EmailAlternativo__c: string;
  ShippingPostalCode__c: string;
  ShippingNeighborhood__c: string;
  ShippingStreet__c: string;
  ShippingCity__c: string;
  ShippingNumber__c: string;
  ShippingState__c: string;
  ShippingComplement__c: string;
}

export const useUserData = () => {
  const { user, setUser, nameFinal } = useAppContext();
  const { setLoadingInfo } = useLoading();
  const [error, setError] = useState<Error | null>(null);
  const [avatar, setAvatar] = useState<string>(
    user?.avatar ? `${FILES_URL}${user?.avatar}` : '/images/perfil-default.svg'
  );
  const { toggleModal } = useModal();

  const handleUpdateUserDetails: SubmitHandler<UserUpdateResponse> = async (
    data
  ): Promise<User> => {
    console.log('handleUpdateUserDetails');
    console.log(data);
    console.log(data.TelefoneCelular2__c);
    const payload = {
      userid: user?.AccountId as string,
      data: {
        alternative_name: data.alternative_name
      },
      dataSales: {
        Email__c: data.Email__c,
        EmailAlternativo__c: data.EmailAlternativo__c,
        TelefoneCelular__c: data.TelefoneCelular__c,
        TelefoneCelular2__c: data.TelefoneCelular2__c,
        TelefoneComercial__c: data.TelefoneComercial__c,
        TelefoneFixo__c: data.TelefoneFixo__c,
        ShippingPostalCode__c: data.ShippingPostalCode__c,
        ShippingNeighborhood__c: data.ShippingNeighborhood__c,
        ShippingStreet__c: data.ShippingStreet__c,
        ShippingCity__c: data.ShippingCity__c,
        ShippingNumber__c: data.ShippingNumber__c,
        ShippingState__c: data.ShippingState__c,
        ShippingComplement__c: data.ShippingComplement__c,
        ShippingCountry__c: 'Brasil'
      }
    };
    console.log('payload', payload);

    const response = await updateUserProfile(user?.AccountId as string, payload as Partial<User>);
    console.log('response', response);
    setLoadingInfo(true, 'Salvando alterações...');
    if (response?.data) {
      setLoadingInfo(false);
      setUser({ ...user, ...response.data });
      return response.data;
    } else {
      setLoadingInfo(false);
      return user as User;
    }
  };

  const handleUpdateAvatar = async (file: File) => {
    try {
      setLoadingInfo(true, 'Salvando imagem.');

      const response = await apiPost<AvatarResponse>(
        'updateAvatar',
        {
          AccountId: user?.AccountId as string
        },
        undefined,
        {
          file
        }
      );
      setLoadingInfo(false);
      if (response?.data) {
        const updatedDetails = {
          avatar: response.data.avatar
        };
        if (user) {
          setUser({ ...user, ...updatedDetails, id: user.id ?? 0 });
        }

        setAvatar(response.data.avatar);
        return response.data.avatar;
      } else {
        return user?.avatar;
      }
    } catch (error: unknown) {
      setError(error as Error);
      return user?.avatar;
    }
  };

  const handleCepChange = async (cepValue: string): Promise<AddressData | null> => {
    const cepfinal = cepValue.replace('-', '').replace('_', '');
    if (cepfinal.length < 8) return null;

    setLoadingInfo(true, 'Buscando CEP...');
    try {
      const data = await cep(cepfinal);
      setLoadingInfo(false);
      return {
        neighborhood: data.neighborhood,
        street: data.street,
        city: data.city,
        state: data.state,
        postalCode: cepValue
      };
    } catch (error) {
      console.error('Erro ao buscar o CEP:', error);
      setLoadingInfo(false);
      toggleModal({
        text: '<p>CEP não encontrado.</p><p>Verifique o número informado.</p><p>Obrigado.</p>'
      });
      return null;
    }
  };

  return {
    error,
    user,
    nameFinal,
    avatar,
    setAvatar,
    handleUpdateUserDetails,
    handleUpdateAvatar,
    handleCepChange
  };
};
