/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';

export default function Submit() {
  const { user } = useAppContext();
  if (user === null) return null;
  return (
    <div className='flex justify-center items-center flex-row'>
      <div className='w-1/2 mr-1 flex justify-center md:justify-end max-w-[160px]'>
        <Buttontransparent color='navy-blue' text='Cancelar' route={`/${user.AccountId}/home`} />
      </div>
      <div className='w-1/2 ml-1 flex justify-center md:justify-start max-w-[160px]'>
        <Buttonblue
          text='Salvar'
          background='navy-blue'
          color='white'
          disabled={false}
          type='submit'
          classExtra={'max-w-[160px]'}
        />
      </div>
    </div>
  );
}
