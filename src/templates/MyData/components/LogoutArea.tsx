/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLogout } from '@/templates/Auth/hooks/useLogout';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { useEffect } from 'react';

export default function LogoutArea() {
  const { refreshTokens, logout, tokens } = useLogout();
  const { user } = useAppContext();

  useEffect(() => {
    refreshTokens();
  }, []);
  if (user === null || tokens === null || tokens === undefined) return null;
  const showWhereLogged = Object.keys(tokens).length > 1;

  return (
    <div className='flex mb-14'>
      <div className='w-full flex justify-center'>
        {showWhereLogged ? (
          <Buttontransparent
            color='navy-blue'
            text='Deslogar'
            route={`/${user.AccountId}/meus-dados/onde-estou-logado`}
          />
        ) : (
          <Buttontransparent
            color='navy-blue'
            text='Sair'
            onClick={() => logout({ deviceinfoname: DeviceUtils.deviceInfo() })}
          />
        )}
      </div>
    </div>
  );
}
