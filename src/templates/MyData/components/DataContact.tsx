/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimatePresence, motion } from "motion/react"
import Image from 'next/image';
import { Dispatch, SetStateAction } from 'react';
import { FieldErrors, UseFormRegister } from 'react-hook-form';

import { User } from '@/@types/user';
import { AnimateFadeInOpenHeight } from '@/constants/ANIMATIONS';
import Input from '@/templates/components/Commons/inputs/Input';

import { useAppContext } from '@/contexts/AppContext';

interface Props {
  register: UseFormRegister<User>;
  errors: FieldErrors<User>;
  showContact: boolean;
  setShowContact: Dispatch<SetStateAction<boolean>>;
  showUploadImage: boolean;
  setShowUploadImage: Dispatch<SetStateAction<boolean>>;
}

// Componente para o avatar e nome do usuário
const UserHeader: React.FC<{
  avatar: string;
  nameFinal: string;
  showUploadImage: boolean;
  setShowUploadImage: Dispatch<SetStateAction<boolean>>;
}> = ({ avatar, nameFinal, showUploadImage, setShowUploadImage }) => (
  <button
    className='flex flex-row bg-white rounded px-4 items-center py-4'
    onClick={() => setShowUploadImage(!showUploadImage)}
    type='button'>
    <div className='relative aspect-square overflow-hidden rounded-full flex justify-center items-center max-w-[65px] max-h-[65px] mx-4'>
      <Image src={avatar} alt='Icon' className='rounded-full' width={65} height={65} priority />
    </div>

    <div className='ml-3'>
      <p className='text-left text-sm text-navy-blue tracking-wider leading-4 mb-0.5'>
        Nome da conta:
      </p>
      <p className='text-base text-left font-bold text-navy-blue tracking-1 leading-5'>
        {nameFinal}
      </p>
    </div>
  </button>
);

// Componente para o botão de expandir/contrair os dados de contato
const ContactToggleButton: React.FC<{
  showContact: boolean;
  setShowContact: Dispatch<SetStateAction<boolean>>;
}> = ({ showContact, setShowContact }) => (
  <button
    className='h-12 bg-white border-l-15 border-neutral-blue rounded flex flex-row items-center justify-between px-4 mt-5'
    onClick={() => setShowContact(!showContact)}
    type='button'>
    <p className='text-base text-navy-blue tracking-1 leading-4'>Dados de Contato</p>
    {showContact ? (
      <Image
        src='/images/less.svg'
        alt='Icon'
        className='rounded-full'
        width={17}
        height={17}
        priority
      />
    ) : (
      <Image
        src='/images/more.svg'
        alt='Icon'
        className='rounded-full'
        width={17}
        height={17}
        priority
      />
    )}
  </button>
);

// Componente para os campos de formulário de contato
const ContactFormFields: React.FC<{
  register: UseFormRegister<User>;
  errors: FieldErrors<User>;
  user: User;
}> = ({ register, errors, user }) => (
  <div className='bg-white pt-1 px-4 pb-6 rounded'>
    <Input
      errors={errors.FirstName ? errors.FirstName.message : null}
      label='Nome:'
      name='FirstName'
      type='text'
      disabled={true}
    />

    <Input
      errors={errors.LastName ? errors.LastName.message : null}
      label='Sobrenome:'
      name='LastName'
      type='text'
      disabled={true}
    />

    <Input
      {...register('Email__c', {
        required: 'Seu e-mail é obrigatório. '
      })}
      errors={errors.Email__c ? errors.Email__c.message : null}
      label='E-mail:'
      name='Email__c'
      type='email'
    />

    <Input
      {...register('EmailAlternativo__c')}
      label='E-mail alternativo:'
      name='EmailAlternativo__c'
      type='email'
    />

    <Input
      type='textmask'
      mask='(99) 99999-9999'
      label='Telefone celular:'
      defaultValue={user?.TelefoneCelular__c ?? ''}
      {...register('TelefoneCelular__c', {
        required: 'Você precisa ter pelo menos um telefone cadastrado.'
      })}
    />

    <Input
      mask='(99) 99999-9999'
      defaultValue={user?.TelefoneCelular2__c ?? ''}
      {...register('TelefoneCelular2__c')}
      label='Telefone celular 2:'
      name='TelefoneCelular2__c'
      type='textmask'
    />

    <Input
      {...register('TelefoneFixo__c')}
      label='Telefone residencial:'
      name='TelefoneFixo__c'
      type='textmask'
      mask='(99) 9999-9999'
      defaultValue={user?.TelefoneFixo__c ?? ''}
    />

    <Input
      {...register('TelefoneComercial__c')}
      label='Telefone comercial:'
      name='TelefoneComercial__c'
      type='textmask'
      mask='(99) 9999-9999'
      defaultValue={user?.TelefoneComercial__c ?? ''}
    />
  </div>
);

export default function DataContact({
  register,
  errors,
  showContact,
  setShowContact,
  showUploadImage,
  setShowUploadImage
}: Readonly<Props>) {
  const { user, nameFinal, avatar } = useAppContext();

  // Early return para verificações de dados
  if (!user || !nameFinal) return null;
  if (user.AccountId === null) return null;

  return (
    <>
      <UserHeader
        avatar={avatar}
        nameFinal={nameFinal}
        showUploadImage={showUploadImage}
        setShowUploadImage={setShowUploadImage}
      />

      <div>
        <Input {...register('alternative_name')} type='text' label='Como deseja ser chamado(a)?' />
      </div>

      <ContactToggleButton showContact={showContact} setShowContact={setShowContact} />

      <AnimatePresence>
        {showContact && (
          <motion.div
            initial='collapsed'
            animate={showContact ? 'open' : 'collapsed'}
            variants={AnimateFadeInOpenHeight}
            exit='exit'
            transition={{ duration: 0.3, ease: 'easeInOut' }}>
            <ContactFormFields register={register} errors={errors} user={user} />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
