/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Suspense, useEffect, useLayoutEffect } from 'react';

import { WhereTokens } from '@/@types/whereTokens';
import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { Show } from '@/utils/components/Show';
import { useLogout } from '../Auth/hooks/useLogout';
import Buttontransparent from '../components/Commons/buttons/Buttontransparent';
import SpinnerLoading from '../components/Commons/Loading/SpinnerLoading';
import TitlePage from '../components/Commons/TitlePage';

export default function WhereISLogged() {
  const { setLoadingInfo } = useLoading();
  const { refreshTokens, tokens, logout } = useLogout();
  const { user, setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();
  const { canBeRender } = useCanBeRender();
  const thisDevice = Object.values(tokens).find((token) => token.isCurrent) as WhereTokens;
  const othersDevice = Object.values(tokens).filter((token) => !token.isCurrent);
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Aonde estou logado?');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  useEffect(() => {
    setLoadingInfo(false);
    refreshTokens();
  }, []);

  const conditions = [
    isMobile === null,
    tokens === null,
    tokens === undefined,
    thisDevice === undefined,
    user === null
  ];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <Suspense fallback={<SpinnerLoading />}>
        <TitlePage text='Onde estou logado?' />
        <div className='flex flex-col gap-6  justify-center '>
          <h5 className='text-navy-blue font-bold tracking-1 text-md p-2'>Esse dispositivo:</h5>
          <Show when={thisDevice}>
            {infoDevices(thisDevice, 0, () => logout({ deviceinfoname: thisDevice.name }))}
          </Show>
        </div>
        <Show when={othersDevice.length > 0}>
          <div className='flex flex-col gap-6 mt-8 justify-center '>
            <h5 className='text-navy-blue font-bold tracking-1 text-md p-2'>
              Outros dispositivos que você está logado:
            </h5>
            {othersDevice.map((token: WhereTokens, index: number) =>
              infoDevices(token, index, () => logout({ deviceinfoname: token.name }))
            )}
          </div>
        </Show>
        <div className='flex w-full justify-center'>
          <Buttontransparent
            color='navy-blue'
            text='Voltar'
            classExtra='mt-[8px]'
            route={`/${user?.AccountId}/meus-dados`}
          />
        </div>
      </Suspense>
    </>
  );
}

function infoDevices(token: WhereTokens, index: number, logout: () => void) {
  return (
    <div key={`${token.name}_${index}`} className=''>
      <div
        key={`${token.name}_${index}`}
        className={`grid max-md:grid-rows-3 md:grid-rows-0 md:grid-cols-3 justify-center md:justify-normal items-center p-4 px-8 rounded-lg shadow-md ${index % 2 === 0 ? 'bg-gray-300' : 'bg-white'}`}>
        <p className='text-lg font-bold text-navy-blue text-center'>{token.name}</p>
        <div className='text-sm text-neutral-blue'>
          <p>
            <span>Último uso: </span>
            <strong>{new Date(token.last_used_at).toLocaleString()}</strong>
          </p>
          <p className='text-sm'>
            <span>Criado em: </span>
            <strong>{new Date(token.created_at).toLocaleString()}</strong>
          </p>
        </div>
        <Buttontransparent
          color='navy-blue'
          text='Deslogar desse local'
          classExtra='mt-[8px]'
          onClick={logout}
        />
      </div>
    </div>
  );
}
