/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { User } from '@/@types/user';
import { useModal } from '@/contexts/useModal';
import { useLoading } from '@/hooks';

import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import AdressInfo from './components/AdressInfo';
import DataContact from './components/DataContact';
import LogoutArea from './components/LogoutArea';
import SendAvatarComp from './components/SendAvatarComp';
import Submit from './components/Submit';
import { useUserData } from './hooks/useUserData';

export default function MyData() {
  const { toggleModal } = useModal();
  const [showContact, setShowContact] = useState(false);
  const [showAddress, setShowAddress] = useState(false);
  const [showUploadImage, setShowUploadImage] = useState(false);
  const { user, setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();
  const { handleUpdateUserDetails, error } = useUserData();
  const { canBeRender } = useCanBeRender();
  const { setLoadingInfo } = useLoading();
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<User>({ values: user a  s User });

  const onSubmit = async (data: any) => {
    setShowContact(false);
    setShowAddress(false);
    // await setUser(data);
    (await handleUpdateUserDetails(data)) as User;
    toggleModal({ text: 'Dados atualizados com sucesso.' });
  };

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Meus Dados');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [user === null, isMobile === null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <SendAvatarComp showUploadImage={showUploadImage} setShowUploadImage={setShowUploadImage} />
      {error && <p className='text-red-600'>{error.message}</p>}
      {!showUploadImage && (
        <div className='z-0  w-full'>
          {/* <div className='z-0 absolute w-[92%] md:w-[66%] max-w-[1140px]'> */}
          <form className='flex flex-col' onSubmit={handleSubmit(onSubmit)}>
            <DataContact
              register={register}
              errors={errors}
              showContact={showContact}
              setShowContact={setShowContact}
              showUploadImage={showUploadImage}
              setShowUploadImage={setShowUploadImage}
            />
            <AdressInfo
              register={register}
              errors={errors}
              watch={watch}
              setValue={setValue}
              showAddress={showAddress}
              setShowAddress={setShowAddress}
            />
            <Submit />
          </form>

          <LogoutArea />
        </div>
      )}
    </>
  );
}
