/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useEffect, useMemo } from 'react';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import ButtonImage from '@/templates/components/Commons/buttons/ButtonImage';
import { DateUtils } from '@/utils/DateUtils';
import TitlePage from '../components/Commons/TitlePage';
import ListAndamento from './components/ListAndamento';

// Interfaces for our data structures
interface Empreendimento {
  StatusMacro__c: string;
  DataEntregaContratualCury__c?: string;
  Fotos__c?: string[];
  Videos__c?: string[];
  Video_Tour__c?: string[];
}

// Props for component interfaces
interface ProjectStatusInfoProps {
  statusMacro: string;
  deliveryDate: string;
}

interface MediaButtonsProps {
  empreendimento: Empreendimento;
  accountId?: string;
}

// Component for showing project status info
const ProjectStatusInfo: React.FC<ProjectStatusInfoProps> = ({ statusMacro, deliveryDate }) => (
  <>
    <p className='text-sm text-navy-blue tracking-1'>
      <span>Status do Empreendimento: </span>
      <span className='text-sm font-bold text-blue-secondary tracking-1 mb-4'>{statusMacro}</span>
    </p>
    <p className='text-sm text-navy-blue tracking-1 mb-4'>
      <span>Data Prevista para Término de Obra: </span>
      <span className='text-sm font-bold text-blue-secondary tracking-1 mb-4'>{deliveryDate}</span>
    </p>
  </>
);

// Component for media buttons (photos, videos, virtual tour)
const MediaButtons: React.FC<MediaButtonsProps> = ({ empreendimento, accountId }) => {
  const { Fotos__c, Videos__c, Video_Tour__c } = empreendimento;

  // Only render this component if any media is available
  if (!Fotos__c && !Videos__c && !Video_Tour__c) return null;

  return (
    <div className='grid grid-cols-2 gap-4 max-md:grid-cols-none max-md:gap-0'>
      {Fotos__c && Fotos__c.length > 0 && (
        <ButtonImage
          text='Fotos'
          src='/images/fotos.png'
          route={`/${accountId}/andamento-de-obra/fotos-do-imovel`}
        />
      )}

      {Videos__c && Videos__c.length > 0 && (
        <ButtonImage
          text='Vídeo'
          src='/images/video.png'
          route={`/${accountId}/andamento-de-obra/videos-do-imovel`}
        />
      )}

      {Video_Tour__c && Video_Tour__c.length > 0 && (
        <ButtonImage
          text='Tour Virtual'
          src='/images/video.png'
          route={`/${accountId}/andamento-de-obra/tour-virtual`}
        />
      )}
    </div>
  );
};

const WorkInProgressScreen: React.FC = () => {
  const { content, user, setSubtitleHeaderMobile, isMobile } = useAppContext();
  const { setLoadingInfo } = useLoading();

  useEffect(() => {
    setSubtitleHeaderMobile('Andamento de Obra');
    setLoadingInfo(false);
  }, []);

  const formattedDeliveryDate = useMemo((): string => {
    return content?.Empreendimento.DataEntregaContratualCury__c
      ? DateUtils.formatDate(content.Empreendimento.DataEntregaContratualCury__c)
      : 'EM BREVE';
  }, [content?.Empreendimento.DataEntregaContratualCury__c]);
  if (!content || isMobile === null) return null;
  return (
    <div className='w-full mb-14' key={`andamento-de-obra-${Date.now()}`}>
      <TitlePage text='Andamento de obra' />
      <ProjectStatusInfo
        statusMacro={content.Empreendimento.StatusMacro__c}
        deliveryDate={formattedDeliveryDate}
      />
      <MediaButtons empreendimento={content.Empreendimento} accountId={user?.AccountId} />
      <ListAndamento />
    </div>
  );
};

export default WorkInProgressScreen;
