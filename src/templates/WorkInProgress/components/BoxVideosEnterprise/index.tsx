/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { BoxVideosEnterpriseProps } from '@/@types/videos';
import CardEnterpriseVideos from '../CardEnterpriseVideos';

const BoxVideosEnterprise = ({ videos, setShowVideos }: BoxVideosEnterpriseProps) => {
  const videosCards = Array.isArray(videos) ? videos : JSON.parse(videos || '[]');

  return (
    <div className='grid grid-cols-3 gap-10 max-md:grid-cols-2 max-md:gap-4 w-full'>
      {videosCards.map((item: string, index: number) => (
        <CardEnterpriseVideos key={item} src={item} onClick={() => setShowVideos(index)} />
      ))}
    </div>
  );
};

export default BoxVideosEnterprise;
