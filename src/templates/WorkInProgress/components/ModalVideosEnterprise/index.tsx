/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { useAppContext } from '@/contexts/AppContext';
import SliderLightboxVideos from '@/templates/components/SliderLightBoxVideos';
import { Show } from '@/utils/components/Show';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import Image from 'next/image';
import { useEffect } from 'react';

const ModalVideosEnterprise = () => {
  const { videosToSlider, showVideos, setShowVideos } = useAppContext();
  useEffect(() => {
    return () => {
      setShowVideos(null);
    };
  }, []);
  if (showVideos === null || showVideos === undefined) return null;
  return (
    <>
      <Show when={showVideos !== -1}>
        <motion.div
          className={`bg-navy-blue bg-opacity-90 w-[100vw] h-[100vh] fixed flex flex-col justify-start items-center ${
            showVideos !== -1 ? 'z-[4000000000000]' : 'z-0'
          }`}
          initial='collapsed'
          animate={showVideos !== -1 ? 'open' : 'collapsed'}
          variants={AnimateFadeIn}
          exit='exit'
          transition={{
            duration: 0.3,
            ease: 'easeInOut'
          }}>
          <div className=' w-full max-w-[1024px] h-[100%] relative p-4 flex flex-col '>
            <div className='bg-white w-full  max-w-[1000px] p-10 h-[100%] relative  flex flex-col '>
              <div className='bg-gray-400-800 rounded h-[100%] relative justify-center items-center '>
                <button
                  type='button'
                  className='BT_CLOSE absolute -top-8 -right-8 z-[41] cursor-pointer bg-white'
                  onClick={() => setShowVideos(-1)}>
                  <Image
                    src='/images/close.png'
                    alt='Icon'
                    className='w-auto h-auto'
                    width={30}
                    height={30}
                    priority
                  />
                </button>
                <SliderLightboxVideos videos={videosToSlider as string[]} startIndex={showVideos} />
              </div>
            </div>
          </div>
        </motion.div>
      </Show>
    </>
  );
};

export default ModalVideosEnterprise;
