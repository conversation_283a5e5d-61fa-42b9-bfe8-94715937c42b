/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import Andamento from '@/templates/components/Andamento';

export default function ListAndamento() {
  const { content } = useAppContext();

  const {
    Fundacao__c,
    Estrutura__c,
    Alvenaria__c,
    InstalacoesEletricas__c,
    InstalacoesHidraulicas__c,
    AcabamentoInterno__c,
    AcabamentoExterno__c,
    ServicosComplementares__c,
    Pintura__c
  } = content?.Empreendimento ?? {};

  if (!content) return null;

  const statusObra = [
    { key: 'Fundacao', nome: 'Fundação', porcentagem: Fundacao__c ?? '0' },
    { key: 'Estrutura', nome: 'Estrutura', porcentagem: Estrutura__c ?? '0' },
    { key: 'Alvenaria', nome: 'Alvenaria', porcentagem: Alvenaria__c ?? '0' },
    {
      key: 'Instalações Elétricas',
      nome: 'Instalações Elétricas',
      porcentagem: InstalacoesEletricas__c ?? '0'
    },
    {
      key: 'Instalações Hidráulicas',
      nome: 'Instalações Hidráulicas',
      porcentagem: InstalacoesHidraulicas__c ?? '0'
    },
    {
      key: 'Acabamento Interno',
      nome: 'Acabamento Interno',
      porcentagem: AcabamentoInterno__c ?? '0'
    },
    {
      key: 'Acabamento Externo',
      nome: 'Acabamento Externo',
      porcentagem: AcabamentoExterno__c ?? '0'
    },
    {
      key: 'Serviços Complementares',
      nome: 'Serviços Complementares',
      porcentagem: ServicosComplementares__c ?? '0'
    },
    { key: 'Pintura', nome: 'Pintura', porcentagem: Pintura__c ?? '0' }
  ];

  return (
    <div className='mt-7 bg-white rounded p-5 max-md:bg-transparent'>
      {statusObra.map((item) => (
        <Andamento key={item.key} nome={item.nome} porcentagem={item.porcentagem} />
      ))}
    </div>
  );
}
