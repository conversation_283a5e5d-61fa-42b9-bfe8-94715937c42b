/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import type { BoxCardsEnterpriseProps } from '@/@types/Empreendimento';
import CardEnterpriseImage from '../CardEnterpriseImage';

const BoxCardsEnterprise = ({ imagesCards, setShowFoto }: BoxCardsEnterpriseProps) => {
  if (imagesCards.length === 0) return null;
  return (
    <div className='grid grid-cols-3 gap-4 max-md:grid-cols-2  w-full'>
      {imagesCards.map((item: string, index: number) => (
        <CardEnterpriseImage
          key={item}
          src={item}
          onClick={() => {
            setShowFoto(index);
          }}
        />
      ))}
    </div>
  );
};

export default BoxCardsEnterprise;
