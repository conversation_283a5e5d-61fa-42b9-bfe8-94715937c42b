/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';

import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { DateUtils } from '@/utils/DateUtils';
import { StringUtils } from '@/utils/StringUtils';
import { useEffect, useLayoutEffect } from 'react';
import TitlePage from '../components/Commons/TitlePage';
import BoxVideosEnterprise from './components/BoxVideosEnterprise';

export default function VideosTourImmobileScreen() {
  const { canBeRender } = useCanBeRender();
  const {
    content,
    setSubtitleHeaderMobile,
    setShowSidebar,
    setShowVideos,
    setVideosToSlider,
    videosToSlider,
    isMobile
  } = useAppContext();
  const { setLoadingInfo } = useLoading();

  const videos = StringUtils.ParselStringToArray(content?.Empreendimento.Video_Tour__c);

  const data_Last_Data_Tour__c = content?.Empreendimento?.Last_Data_Tour__c
    ? DateUtils.formatDate(content?.Empreendimento?.Last_Data_Tour__c)
    : 'EM BREVE';

  useLayoutEffect(() => {
    if (isMobile !== null) {
      setSubtitleHeaderMobile('Vídeo Tour');
      setShowSidebar(!isMobile);
    }
  }, [isMobile]);

  useLayoutEffect(() => {
    if (videos && videos.length > 0 && !videosToSlider) {
      setVideosToSlider(videos);
    }
  }, [videosToSlider, videos]);

  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (!canBeRender({ conditions: [isMobile === null] })) return null;

  return (
    <div className='mb-14'>
      <TitlePage text='Andamento de obra - Vídeos Tour' />
      <p className='text-sm text-navy-blue tracking-1 mb-4'>
        <span>Última atualização: </span>
        <span className='text-sm font-bold text-blue-secondary tracking-1 mb-4'>
          {data_Last_Data_Tour__c}
        </span>
      </p>
      <BoxVideosEnterprise videos={videos} setShowVideos={setShowVideos} />
    </div>
  );
}
