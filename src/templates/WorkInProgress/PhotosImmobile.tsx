/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { DateUtils } from '@/utils/DateUtils';
import { StringUtils } from '@/utils/StringUtils';
import { useEffect, useLayoutEffect } from 'react';
import TitlePage from '../components/Commons/TitlePage';
import BoxCardsEnterprise from './components/BoxCardsEnterprise';

export default function PhotosImmobileScreen() {
  const { canBeRender } = useCanBeRender();
  const {
    showFoto,
    setSubtitleHeaderMobile,
    setShowSidebar,
    setShowFoto,
    setImagesToSlider,
    content,
    imagesToSlider,
    isMobile
  } = useAppContext();
  const { setLoadingInfo } = useLoading();

  const images = StringUtils.ParselStringToArray(content?.Empreendimento.Fotos__c);

  const dataUltimaAtualizacaoMidia = content?.Empreendimento?.DataUltimaAtualizacaoMidia__c
    ? DateUtils.formatDate(content?.Empreendimento?.DataUltimaAtualizacaoMidia__c)
    : 'EM BREVE';
  useLayoutEffect(() => {
    if (isMobile !== null) {
      setSubtitleHeaderMobile('Fotos do Imóvel');
      setShowSidebar(!isMobile);
    }
  }, [isMobile]);

  useLayoutEffect(() => {
    if (images && images.length > 0 && !imagesToSlider) {
      setImagesToSlider(images);
    }
  }, [images, imagesToSlider]);

  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (!canBeRender({ conditions: [isMobile === null] })) return null;

  return (
    <div className='mb-14'>
      <TitlePage text='Andamento de obra - Fotos' />
      <p className='text-sm text-navy-blue tracking-1 mb-4'>
        <span>Última atualização: </span>
        <span className='text-sm font-bold text-blue-secondary tracking-1 mb-4'>
          {dataUltimaAtualizacaoMidia}
        </span>
      </p>
      <BoxCardsEnterprise imagesCards={images} setShowFoto={setShowFoto} />
    </div>
  );
}
