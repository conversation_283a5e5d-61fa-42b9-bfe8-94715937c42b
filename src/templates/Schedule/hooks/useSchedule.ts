/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useCallback, useEffect, useState } from 'react';

import { Schedules } from '@/@types/Schedule';
import { useApi } from '@/hooks/useApi';
import { apiGet } from '@/server/services/api';
import { DateUtils } from '@/utils/DateUtils';

interface GetScheduleProps {
  EmpreendimentoId: string;
}

export const useSchedule = ({ EmpreendimentoId }: GetScheduleProps) => {
  const [data, setData] = useState<Schedules | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const { month, year } = DateUtils.getTodayDate();
  const [monthSelected, setMonthSelected] = useState<number>();
  const [yearSelected, setYearSelected] = useState<number>();
  const [dataName, setDataName] = useState('');
  const [timimg, setTimimg] = useState(true);
  const { getFetch } = useApi();
  useEffect(() => {
    setMonthSelected(month as number);
    setYearSelected(year);
  }, [month, year]);
  const fetchSchedule = useCallback(async () => {
    if (!monthSelected || !yearSelected) return false;
    try {
      // const response_bkp = await getFetch<{
      //   data: Schedules;
      // }>({
      //   method: 'GET',
      //   route: `schedule?empreendimentoId=${EmpreendimentoId}&month=${monthSelected}&year=${yearSelected}`
      // });

      const response = await apiGet(
        `schedule?empreendimentoId=${EmpreendimentoId}&month=${monthSelected}&year=${yearSelected}`,
        {
          cache: 'force-cache',
          revalidateTime: 600
        }
      );

      if (response) setData(response.data);
      setError(null);
      return response;
    } catch (err: unknown) {
      setError(err as Error);
      return err;
    }
  }, [EmpreendimentoId, monthSelected, yearSelected]);

  useEffect(() => {
    fetchSchedule();

    setDataName(`${DateUtils.getMonthName(monthSelected ?? 0)} / ${yearSelected}`);
  }, [EmpreendimentoId, monthSelected, yearSelected]);

  const calcMonth = (direction: string) => {
    if (!yearSelected || !monthSelected) return null;

    let newMonth = monthSelected;
    let newYear = yearSelected;

    if (direction === '+') {
      newMonth++;
    } else if (direction === '-') {
      newMonth--;
    }

    if (newMonth > 12) {
      newMonth = 1;
      newYear++;
    }
    if (newMonth < 1) {
      newMonth = 12;
      newYear--;
    }

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // getMonth() retorna 0-11

    if (newYear > currentYear || (newYear === currentYear && newMonth >= currentMonth)) {
      setTimimg(true);
    } else {
      setTimimg(false);
    }

    setMonthSelected(newMonth);
    setYearSelected(newYear);

    return newMonth;
  };

  return { data, error, calcMonth, dataName, timimg };
};
