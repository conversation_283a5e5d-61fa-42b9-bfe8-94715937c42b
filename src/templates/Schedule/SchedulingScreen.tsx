/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import Image from 'next/image';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { useCase, useLoading } from '@/hooks';

import { useAppContext } from '@/contexts/AppContext';
import { useModal } from '@/contexts/useModal';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import CustomDropdown, {
  CustomDropdownHandles,
  Option
} from '@/templates/components/Commons/inputs/CustomDropdown';
import Textarea from '@/templates/components/Commons/inputs/Textarea';
import CustomCalendar, { Value } from '@/templates/components/Schedule/CustomCalendar';
import { useScheduleOptions } from '@/templates/Schedule/hooks/useScheduleOptions';
import { Show } from '@/utils/components/Show';
import { DateUtils } from '@/utils/DateUtils';
import TitlePage from '../components/Commons/TitlePage';
import { itensDefault } from './mocks/content';

export default function SchedulingScreen() {
  const [selectDateValue, setSelectDateValue] = useState<Value | null>(null);
  const [showCalendar, setShowCalendar] = useState<boolean>(false);
  // const [message, setMessage] = useState<string>('');
  const messageRef = useRef<string>(null);
  const { canBeRender } = useCanBeRender();
  const [optionsHours, setOptionsHours] = useState<Option[]>([{ id: 0, name: '', value: '' }]);
  const [itens, setItens] = useState<string[]>(itensDefault);
  const [selectItens, setSelectItens] = useState<string[]>([]);
  const [finalDate, setFinalDate] = useState<string>('');
  const subDropdownRef = useRef<CustomDropdownHandles>(null);
  const { user, content, setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  const { isMobile } = useMediaQuery();
  const { sendCase, checkCaseToday } = useCase();
  const { setLoadingInfo } = useLoading();
  const { toggleModal } = useModal();

  const {
    setDateSeleted,
    dateSeleted,
    error,
    allowedDaysOfWeek,
    getStartTimesByDay,
    dayOfWeek,
    setDayOfWeek,
    holidays,
    busyDays,
    getStartTimes
  } = useScheduleOptions({
    EmpreendimentoId: content?.Empreendimento?.EmpreendimentoId as string,
    isActive: content?.Empreendimento?.TerritorioServicoIsActive as boolean
  });

  const selectHour = (value: string | number) => {
    const finalDateSelected = `${dateSeleted} - ${value}`;
    setFinalDate(finalDateSelected);
  };

  const openCalendar = useCallback(() => {
    setShowCalendar(true);
    if (subDropdownRef.current) {
      subDropdownRef.current.closeDropdown();
    }
  }, [subDropdownRef]);
  const sendSchedule = useCallback(async () => {
    if (selectItens.length === 0) {
      toggleModal({ text: 'Por favor, selecione pelo menos um item (obrigatório).' });
      return;
    }

    if (allowedDaysOfWeek.size > 0 && (!finalDate || finalDate === '')) {
      toggleModal({ text: 'Por favor, selecione uma data e horário (obrigatório).' });
      return;
    }

    if (!messageRef.current || messageRef.current.trim() === '') {
      toggleModal({ text: 'Por favor, preencha o campo de mensagem (obrigatório).' });
      return;
    }

    const finalDateGntDateFinal = finalDate.slice(0, -5);
    const hour = parseInt(finalDate.slice(-5, -3)) - 3;
    const formattedTimeGtm = finalDateGntDateFinal + hour.toString() + finalDate.slice(-3);
    return sendCase({
      user,
      content,
      tipoAtendimento: 'Solicitação',
      type: 'case/insertCaseAgendamento',
      classificacao: 'Assistência Técnica - Faixa 2',
      assunto: 'Assistência Técnica - Unidade',
      description: `${messageRef.current} \n\nPedido de agendamento para ${formattedTimeGtm}`,
      textLoadingCase: 'Estamos enviando seu agendamento...',
      OrigemSolicitacaoAreaPrivativa__c: selectItens.join(';'),
      DataAgendamento__c: DateUtils.formatDateToScheduling(finalDate),
      DataAgendamentoFormatada__c: formattedTimeGtm
    });
  }, [content, user, finalDate, messageRef, selectItens, toggleModal]);

  const setCheckCaseToday = useCallback(async () => {
    await checkCaseToday({
      AccountId: user?.AccountId as string,
      filter: 'agendamento'
    });
  }, [checkCaseToday, user?.AccountId]);
  useEffect(() => {
    setCheckCaseToday();
    setSubtitleHeaderMobile('Agendamento');
    setShowSidebar(!isMobile);
    setLoadingInfo(false);
  }, []);

  useEffect(() => {
    const startTimesByDay = getStartTimesByDay(dateSeleted);
    setOptionsHours(startTimesByDay);
    const finalDateSelected =
      startTimesByDay.length > 0 ? `${dateSeleted} - ${startTimesByDay[0].value}` : '';
    setFinalDate(finalDateSelected);
    if (subDropdownRef.current) {
      subDropdownRef.current.setNewOption(startTimesByDay);
    }
    getStartTimes();
  }, [dayOfWeek]);

  const conditions = useMemo(() => [isMobile === null, content === null], [isMobile, content]);
  if (!canBeRender({ conditions })) return null;
  if (content?.type !== 'morador') return null;

  return (
    <>
      {error && <p>{error.message}</p>}

      <TitlePage text='Agenda' />
      <p className='font-bold text-base text-navy-blue tracking-1 mt-4 mb-3 max-md:hidden'>
        Agende sua assistência técnica conosco!
      </p>
      <p className='text-sm text-navy-blue tracking-1 mb-4'>
        Selecione a(s) área(s) privativa(s) para a qual deseja suporte, escolha a data mais
        conveniente para você e preencha os campos abaixo.
        <br></br>
        <br></br>
        Caso queira, deixe uma mensagem para nossa equipe.
        <br></br>Estamos à disposição para atendê-lo da melhor forma possível.
      </p>
      <hr className='border-t-1 border-white mt-4 mb-5 w-full' />

      <p className='text-sm text-navy-blue tracking-widest mb-4'>
        Origem da solicitação - Área privativa:
      </p>

      <div className='grid grid-cols-2 gap-7 w-2/3 max-md:w-full'>
        <div className='relative'>
          <button className='absolute -right-5 top-16 bg-transparent'>
            <Image
              src='/images/angle-down.svg'
              alt='Icon'
              className='-rotate-90 object-contain w-auto h-auto'
              width={12}
              height={8}
              priority
            />
          </button>
          <button className='absolute -right-[18px] bottom-14 bg-transparent'>
            <Image
              src='/images/angle-down.svg'
              alt='Icon'
              className='rotate-90 object-contain w-auto h-auto'
              width={12}
              height={8}
              priority
            />
          </button>
          <p className='text-sm text-navy-blue tracking-widest mb-1'>Disponíveis</p>
          <div className='h-[250px] border-neutral-blue border-1 rounded bg-white overflow-auto no-scrollbar'>
            {itens
              .sort((a, b) => a.localeCompare(b))
              .map((item) => (
                <button
                  onClick={() => {
                    setSelectItens((prevItems) => [...prevItems, item]);
                    setItens(itens.filter((itemS) => itemS !== item));
                  }}
                  key={`selectedItens_${item}`}
                  className='flex w-full text-left text-navy-blue opacity-50 text-sm tracking-1 p-2 hover:bg-[#62829A] hover:text-white hover:opacity-100 border-b-[#62829A] border-b-1 border-b-solid'>
                  {item}
                </button>
              ))}
          </div>
        </div>
        <div>
          <p className='text-sm text-navy-blue tracking-widest mb-1'>Escolhidos</p>
          <div className='h-[250px] border-neutral-blue border-1 rounded py-1 bg-white overflow-auto no-scrollbar'>
            {selectItens
              .sort((a, b) => a.localeCompare(b))
              .map((item) => (
                <button
                  onClick={() => {
                    setSelectItens(selectItens.filter((selectedItem) => selectedItem !== item));
                    setItens((prevItems) => [...prevItems, item]);
                  }}
                  key={`selectItens_${item}`}
                  className='flex w-full text-left text-navy-blue opacity-50 text-sm tracking-1 p-2 hover:bg-[#62829A] hover:text-white hover:opacity-100 border-b-[#62829A] border-b-1 border-b-solid'>
                  {item}
                </button>
              ))}
          </div>
        </div>
      </div>
      {allowedDaysOfWeek.size === 0 ? (
        <div className='grid grid-cols-1 gap-7 w-2/3 max-md:w-full mt-6 mb-6 bg-white p-10 rounded text-navy-blue text-center'>
          No momento, todos os horários estão preenchidos.<br></br> Por favor, envie-nos uma
          mensagem e agendaremos para você o mais breve possível.
          <br></br>Entraremos em contato assim que tivermos uma vaga disponível.
        </div>
      ) : null}
      <div
        className={`grid grid-cols-2 gap-7 w-2/3 max-md:w-full ${allowedDaysOfWeek.size === 0 ? 'opacity-30' : 'opacity-100'}`}>
        <div>
          <label
            className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3'
            htmlFor='choiceDate'>
            Escolha uma data*:
          </label>
          <input name='choiceDate' value={dateSeleted} type='hidden' readOnly />

          <button
            type='button'
            onClick={openCalendar}
            className='rounded border-1 flex justify-between items-center border-neutral-blue bg-white h-12 px-3 text-sm text-navy-blue tracking-1 w-full'>
            <span>{dateSeleted}</span>
            <span className='ml-2'>{showCalendar ? '▲' : '▼'}</span>
          </button>
        </div>

        <div
          className={`${allowedDaysOfWeek.size === 0 ? 'opacity-100' : selectDateValue ? 'opacity-100' : 'opacity-50'}`}>
          <span className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3'>
            Escolha um horário*:
          </span>
          <CustomDropdown
            disabled={!selectDateValue}
            ref={subDropdownRef}
            defaultValue={'00:00h'}
            optionsExternal={optionsHours}
            onChange={(value: string | number | boolean) => {
              if (typeof value === 'string' || typeof value === 'number') {
                selectHour(value.toString());
              }
            }}
          />
        </div>
      </div>
      <div className='mt-6 w-2/3 max-md:w-full'>
        <Textarea
          name='mensagem'
          label='Mensagem*:'
          onChange={(e) => (messageRef.current = e.target.value)}
        />
      </div>
      <p className='text-navy-blue text-xs mt-6'>*Campos obrigatórios</p>
      <div className='-mt-2 mb-6 w-1/3 max-md:w-full'>
        <Buttonblue background='navy-blue' color='white' text='Agendar' onClick={sendSchedule} />
      </div>
      <Show when={showCalendar}>
        <div className='bg-navy-blue bg-opacity-90 w-full h-full fixed inset-0 z-50 p-4 flex flex-col justify-center items-center'>
          <div className='bg-white rounded relative px-4 py-14 -mb-10 flex justify-center w-1/3 max-md:w-full'>
            <button
              className='bg-transparent absolute top-1 right-1'
              onClick={() => setShowCalendar(false)}>
              <Image
                src='/images/close.png'
                alt='Icon'
                className='w-auto h-auto'
                width={25}
                height={25}
                priority
              />
            </button>
            <CustomCalendar
              setDateSeleted={setDateSeleted}
              setDayOfWeek={setDayOfWeek}
              allowedDaysOfWeek={allowedDaysOfWeek}
              setSelectDateValue={setSelectDateValue}
              holidays={holidays}
              busyDays={busyDays}
            />
            <button
              className='absolute bottom-2 right-6 bg-navy-blue rounded  text-white px-6 py-2 text-sm'
              onClick={() => setShowCalendar(false)}>
              CONFIRMA
            </button>
          </div>
        </div>
      </Show>
    </>
  );
}
