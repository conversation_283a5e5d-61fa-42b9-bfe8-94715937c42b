/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';
import Link from 'next/link';
import { Fragment, useEffect, useLayoutEffect } from 'react';

import { ScheduleCompiled } from '@/@types/Schedule';
import { WHATS_NUMBER } from '@/constants';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import ButtonCalendar from '@/templates/components/Commons/buttons/ButtonCalendar';
import { useSchedule } from '@/templates/Schedule/hooks/useSchedule';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DateUtils } from '@/utils/DateUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import CustomLink from '../components/Commons/buttons/CustomLink';
import TitlePage from '../components/Commons/TitlePage';

// Define interfaces for our data structures
// interface Contract {
//   SituacaoEntrega__c: string;
// }

// interface Empreendimento {
//   EmpreendimentoId: string;
// }

// interface ContentType {
//   Empreendimento: Empreendimento | null;
//   Contract?: Contract | null;
//   type: string;
// }

// interface User {
//   AccountId: string;
// }

// interface AppContextType {
//   user: User | null;
//   content: ContentType | null;
//   setSubtitleHeaderMobile: (subtitle: string) => void;
//   setShowSidebar: (show: boolean) => void;
// }

interface ScheduleData {
  schedule: ScheduleCompiled[];
}

interface Day {
  name: string;
  day: string;
}

// Component for the calendar navigation header
const CalendarHeader: React.FC<{
  dataName: string | null;
  timimg: boolean;
  calcMonth: (direction: string) => void;
}> = ({ dataName, timimg, calcMonth }) => (
  <div
    className={`${timimg ? 'bg-white text-navy-blue' : 'bg-slate-400 text-white'} absolute md:relative h-12 top-[150px] md:top-[0px] left-0 right-0 justify-center items-center max-md:flex`}>
    <button
      className='bg-transparent absolute md:relative left-4 top-4 md:top-3'
      onClick={() => calcMonth('-')}>
      <Image
        src='/images/angle-down.svg'
        alt='Icon'
        className='rotate-90 object-contain w-auto h-auto  mt-[4px]'
        width={12}
        height={8}
        priority
      />
    </button>
    <p className='font-bold text-lg tracking-1 text-center justify-center md:-mt-6'>{dataName}</p>
    <button
      className='absolute right-4  md:top-2 w-[26px] h-[26px] mr-[10px] ml-[10px]'
      onClick={() => calcMonth('+')}>
      <Image
        src='/images/angle-down.svg'
        alt='Icon'
        className='-rotate-90 object-contain w-auto h-auto ml-[10px] mt-[-11px] '
        width={12}
        height={8}
        priority
      />
    </button>
  </div>
);

// Component for "Create Appointment" button (mobile)
const MobileCreateAppointment: React.FC<{ accountId: string }> = ({ accountId }) => (
  <>
    <CustomLink
      className='hidden flex-row justify-between items-center pt-14 w-full max-md:flex'
      href={`/${accountId}/agenda/agendamento`}>
      <div className='flex flex-row items-center'>
        <Image src='/images/pagou.png' alt='Icon' width={30} height={30} priority />
        <p className='text-sm text-navy-blue tracking-1 ml-3'>Fazer agendamento</p>
      </div>
      <Image
        src='/images/angle-down.svg'
        alt='Icon'
        className='-rotate-90 object-contain w-auto h-auto'
        width={12}
        height={8}
        priority
      />
    </CustomLink>
    <hr className='hidden border-t-1 border-white mt-4 mb-4 max-md:block' />
  </>
);

// Component for "Create Appointment" button (desktop)
const DesktopCreateAppointment: React.FC<{ accountId: string }> = ({ accountId }) => (
  <CustomLink
    className='mt-8 flex flex-row justify-between items-center w-1/3 bg-navy-blue h-14 max-md:hidden rounded px-3'
    href={`/${accountId}/agenda/agendamento`}>
    <div className='flex flex-row items-center'>
      <Image src='/images/agenda.png' alt='Icon' width={25} height={25} priority />
      <p className='text-sm text-white tracking-1 ml-3'>Fazer agendamento</p>
    </div>
    <Image
      src='/images/angle-right-white.svg'
      alt='Icon'
      className='w-auto h-auto'
      width={8}
      height={8}
      priority
    />
  </CustomLink>
);

// Component for WhatsApp message when keys aren't delivered
const KeysNotDeliveredMessage: React.FC = () => (
  <div className='flex-row justify-between items-center pt-14 w-full max-md:flex'>
    <span>
      Suas chaves não constam como entregues em nosso sistema, dessa forma, não é possível abrir um
      chamado de assistência técnica. Por favor, entre em contato pelo nosso{' '}
      {DeviceUtils.isMobileApp() ? (
        <button
          className='text-sm underline text-navy-blue tracking-1 mb-4'
          onClick={() =>
            BrowserUtils.ActionReactNative({
              type: 'openUrlBrowser',
              url: WHATS_NUMBER,
              data: {}
            })
          }>
          WhatsApp. Clique aqui.
        </button>
      ) : (
        <Link
          className='text-navy-blue text-lg tracking-8 leading-8 underline mb-4'
          href={WHATS_NUMBER}
          target='_blank'>
          WhatsApp. Clique aqui.
        </Link>
      )}
    </span>
  </div>
);

// Component for rendering the schedule list
const ScheduleList: React.FC<{ data: ScheduleData | null }> = ({ data }) => {
  if (!data?.schedule) return null;

  if (data.schedule.length === 0) {
    return (
      <div className='flex mt-2 mb-1 bg-light-gray max-md:bg-white w-full justify-center items-center rounded focus:outline-none min-h-[75px]'>
        <p>Sem compromissos</p>
      </div>
    );
  }

  return (
    <>
      {data.schedule.map((schedule: ScheduleCompiled) => {
        const thisDay = DateUtils.getDay(schedule.day) as Day | null;
        const thisDayName = thisDay ? thisDay.name : '';
        const thisDayDay = thisDay ? thisDay.day : '';
        return (
          <Fragment key={`calemdar_${thisDayName}_${thisDayDay}`}>
            <ButtonCalendar week={thisDayName} day={thisDayDay} name={schedule.name} />
          </Fragment>
        );
      })}
    </>
  );
};

// Component for the appointments section
const AppointmentsSection: React.FC<{
  data: ScheduleData | null;
  canOpen: boolean;
  accountId: string;
}> = ({ data, canOpen, accountId }) => (
  <div className='mt-4 md:mt-[30px]'>
    <div>
      <p className='font-bold text-base text-navy-blue tracking-1 mt-12 md:mt-4 mb-5'>
        Próximos compromissos:
      </p>
      <div className='bg-white rounded px-6 py-4 max-md:bg-transparent max-md:px-0 max-md:py-0 grid grid-cols-2 gap-5 max-md:grid-cols-none'>
        <ScheduleList data={data} />
      </div>
    </div>

    {canOpen ? <DesktopCreateAppointment accountId={accountId} /> : <KeysNotDeliveredMessage />}
  </div>
);

// Main component with reduced complexity
const ScheduleScreen: React.FC = () => {
  const { user, content, setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  const { canBeRender } = useCanBeRender();
  const { isMobile } = useMediaQuery();
  const { data, error, calcMonth, dataName, timimg } = useSchedule({
    EmpreendimentoId: content?.Empreendimento?.EmpreendimentoId as string
  });
  const { setLoadingInfo } = useLoading();

  // Set up effects
  useInitializeUI(setSubtitleHeaderMobile, setShowSidebar, isMobile, setLoadingInfo);

  // Check rendering conditions
  const conditions = [user === null, isMobile === null, dataName === null, content === null];
  if (!canBeRender({ conditions })) return null;
  if (content === null) return null;
  if (content?.type !== 'morador') return null;
  const chaves =
    content.Contract?.SituacaoEntrega__c?.toLowerCase() ??
    content.SituacaoEntrega__c?.toLowerCase();
  const canOpen = chaves === 'chaves entregues';

  // Safe account ID
  const accountId = user?.AccountId || '';

  return (
    <>
      {error && <p className='text-red-600'>{error.message}</p>}
      <TitlePage text='Agenda' />
      <CalendarHeader dataName={dataName} timimg={!!timimg} calcMonth={calcMonth} />
      {canOpen && <MobileCreateAppointment accountId={accountId} />}
      <AppointmentsSection data={data} canOpen={canOpen} accountId={accountId} />
    </>
  );
};

function useInitializeUI(
  setSubtitleHeaderMobile: (subtitle: string) => void,
  setShowSidebar: (show: boolean) => void,
  isMobile: boolean | null,
  setLoadingInfo: (loading: boolean) => void
) {
  // Set mobile title and sidebar visibility
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Agenda');
    if (isMobile !== null) {
      setShowSidebar(!isMobile);
    }
  }, [isMobile]);

  useEffect(() => {
    setLoadingInfo(false);
  }, []);
}

export default ScheduleScreen;
