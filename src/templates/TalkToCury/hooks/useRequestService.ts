/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useCallback, useEffect, useRef, useState } from 'react';

import { SubmitHandler, useForm, useWatch } from 'react-hook-form';

// import { Content, User } from '@/@types';
import { User } from '@/@types/user';
import { useCase, useModal } from '@/hooks';
import { CustomDropdownHandles } from '@/templates/components/Commons/inputs/CustomDropdown';

import { Content } from '@/@types/content';
import { useAppContext } from '@/contexts/AppContext';
import { FinanceUtils } from '@/utils/FinanceUtils';
import { requestOption } from '../mock/dataRequestOptions';
import { Value } from '../RequestServiceScreen';
import {
  filterRules,
  getRulesForAtendimento,
  getRulesForDocumentos,
  getRulesForFinanciamento
} from './RulesHook';
import { DataValidaFields, validaFields } from './ValidFields';

export interface IFormInputRequestService {
  mainCategory: string;
  valueSelected: number;
  description: string;
  assunto: string;
  ClienteEntregouDocumentoParaCartorio__c?: boolean;
  TipoReembolso__c?: string;
  DataVencOriginalBoleto__c?: string;
  DataVencPagarBoleto__c?: string;
  ValorCheque__c?: string;
  DataCheque__c?: string;
  MesReferencia__c?: string;
}

interface Option {
  id: number;
  name: string;
  value: string;
}

export const useRequestService = () => {
  const { user, content } = useAppContext();
  const { toggleModal } = useModal();
  const [fileCase, setFileCase] = useState<File | null>(null);
  const { sendCase } = useCase();
  const classificacaoDropdownRef = useRef<CustomDropdownHandles>(null);
  const subDropdownRef = useRef<CustomDropdownHandles>(null);
  const mesDropdownRef = useRef<CustomDropdownHandles>(null);
  const DocEntregueCartorioDropdownRef = useRef<CustomDropdownHandles>(null);
  const TipoReembolsoDropdownRef = useRef<CustomDropdownHandles>(null);
  const [showFieldsExtra, setShowFieldsExtra] = useState('');

  const { register, handleSubmit, setValue, control, reset, getValues } =
    useForm<IFormInputRequestService>();

  const results = useWatch({ control, name: 'mainCategory' });

  const type = content ? content.type : 'morador';
  const dependencyList =
    type === 'morador'
      ? [
          content?.Contract?.StatusCarteira__c,
          user?.MembroPatrimonioAfetacao__c,
          content?.Empreendimento.StatusMacro__c,
          content?.Empreendimento.DataRealMatriculaIndividualizada__c
        ]
      : [];
  const [mainCategoryTitle, setMainCategoryTitle] = useState('');
  const watchMainCategory = useCallback(
    (mainCategory: string) => {
      if (type !== 'morador') return {};
      if (mainCategory) {
        let requestOptionFilter = requestOption[mainCategory] as Option[];
        const statusCarteira = content?.Contract?.StatusCarteira__c as string;
        let rules: number[] = [];

        switch (mainCategory) {
          case 'Atendimento':
            rules = getRulesForAtendimento(statusCarteira);
            break;
          case 'Financiamento':
            rules = getRulesForFinanciamento(content as Content, statusCarteira);
            break;
          case 'Documentos':
            rules = getRulesForDocumentos(statusCarteira);
            break;
        }

        requestOptionFilter = filterRules(rules, requestOptionFilter);

        if (subDropdownRef.current !== null && mainCategoryTitle !== mainCategory) {
          subDropdownRef.current.setNewOption(requestOptionFilter);
          setMainCategoryTitle(mainCategory);
        }
      }
      return true;
    },
    [dependencyList, type]
  );

  useEffect(() => {
    watchMainCategory(results);
  }, [results, watchMainCategory]);

  const handleDropDown = (value: string | number | boolean) => {
    closeAll();
    setValue('mainCategory', value.toString());
    setShowFieldsExtra('');
    if (subDropdownRef.current) subDropdownRef.current.closeDropdown();
  };

  const handleDropAssunto = (value: string | number | boolean) => {
    setValue('valueSelected', value as number);
    const data = getValues();
    const categoryOptions = requestOption[data.mainCategory];
    const option = categoryOptions.find((option) => option.value === data.valueSelected);
    setShowFieldsExtra(option?.name ?? '');
  };
  const handleData = (
    // event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    value: string | number | boolean | Value | null,
    field: keyof IFormInputRequestService
  ) => {
    let valueField = value;
    if (field === 'ValorCheque__c') valueField = FinanceUtils.onChangeValueMoney(value as string);

    setValue(field, valueField as string | number | boolean | undefined);
  };

  const closeAll = () => {
    if (classificacaoDropdownRef.current) {
      classificacaoDropdownRef.current.closeDropdown();
    }
    if (subDropdownRef.current) {
      subDropdownRef.current.closeDropdown();
    }
    if (mesDropdownRef.current) {
      mesDropdownRef.current.closeDropdown();
    }
    if (DocEntregueCartorioDropdownRef.current) {
      DocEntregueCartorioDropdownRef.current.closeDropdown();
    }
    if (TipoReembolsoDropdownRef.current) {
      TipoReembolsoDropdownRef.current.closeDropdown();
    }
  };

  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleButtonClick = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const handleFileChange = ({ files }: { files: FileList }) => {
    setFileCase(files[0]);
    return files[0];
  };

  const handleSubmitForm: SubmitHandler<IFormInputRequestService> = async (
    data: DataValidaFields
  ) => {
    if (type === 'morador') {
      if (!validaFields(showFieldsExtra, data, toggleModal)) return null;
      const categoryOptions = requestOption[data.mainCategory];
      const option = categoryOptions.find((option) => option.value === data.valueSelected);
      if (!option) return false;
      await sendCase({
        user: user as User,
        content: content as Content,
        tipoAtendimento: 'Solicitação',
        classificacao: data.mainCategory,
        recordName: option.RecordName,
        OwnerName: option.OwnerName,
        assunto: option.name,
        description: data.description ?? '',
        ClienteEntregouDocumentoParaCartorio__c:
          data.ClienteEntregouDocumentoParaCartorio__c as boolean,
        TipoReembolso__c: data.TipoReembolso__c,
        DataVencOriginalBoleto__c: data.DataVencOriginalBoleto__c,
        DataVencPagarBoleto__c: data.DataVencPagarBoleto__c,
        ValorCheque__c: data.ValorCheque__c,
        DataCheque__c: data.DataCheque__c,
        MesReferencia__c: data.MesReferencia__c,
        file: fileCase ?? undefined,
        textLoadingCase: 'Estamos enviando sua solicitação...'
      });
      return true;
    }

    await sendCase({
      user: user as User,
      content: content as Content,
      tipoAtendimento: 'Solicitação',
      classificacao: type === 'sindico' ? 'Síndico' : 'Atendimento',
      recordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente',
      assunto: type === 'sindico' ? 'Informações técnicas' : 'Patrimônio de Afetação',
      description: data.description ?? '',
      file: fileCase ?? undefined,
      textLoadingCase: 'Estamos enviando sua solicitação...'
    });
    reset();
    setValue('description', '');
    return true;
  };

  return {
    register,
    handleSubmit,
    handleSubmitForm,
    setValue,
    classificacaoDropdownRef,
    mesDropdownRef,
    DocEntregueCartorioDropdownRef,
    TipoReembolsoDropdownRef,
    subDropdownRef,
    // handleDropMes,
    // handleDropDownTipo,
    // handleDropDocEntregueCartorio,
    fileInputRef,
    handleFileChange,
    handleButtonClick,
    handleDropDown,
    handleDropAssunto,
    closeAll,
    control,
    fileCase,
    // handleValorCheque,
    handleData,
    showFieldsExtra
  };
};
