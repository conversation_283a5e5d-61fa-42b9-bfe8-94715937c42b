/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Content } from '@/@types/content';

interface Option {
  id: number;
  name: string;
  value: string;
}

export const filterRules = (rules: number[], items: Option[]): Option[] => {
  return items.filter((item) => !rules.includes(item.id));
};

export const getRulesForAtendimento = (
  // content: Content,
  statusCarteira: string
  // user: User
) => {
  const rules = [];

  if (['FI - Repassado', 'FI - Registrado', 'VD - Registrado'].includes(statusCarteira)) {
    rules.push(1, 2);
  }
  if (['FI - Registrado', 'VD - Registrado'].includes(statusCarteira)) {
    rules.push(1);
  }
  if (
    [
      'FI - Em processo de repasse',
      'FI - Em processo de reversão',
      'VD - Não registrada',
      'VD - Em processo',
      'VD - Em processo de Reversão'
    ].includes(statusCarteira)
  ) {
    rules.push(4);
  }
  return rules;
};

export const getRulesForFinanciamento = (content: Content, statusCarteira: string) => {
  const rules = [];
  if (
    !['VD - Em processo', 'VD - Não registrada'].includes(statusCarteira) &&
    content?.Empreendimento.DataRealMatriculaIndividualizada__c === null
  ) {
    rules.push(4);
  }
  return rules;
};

export const getRulesForDocumentos = (statusCarteira: string) => {
  const rules = [];
  if (['FI - Registrado', 'VD - Registrado'].includes(statusCarteira)) {
    rules.push(4);
  }
  if (['FI - Registrado', 'Fi - Repassado'].includes(statusCarteira)) {
    rules.push(5);
  }
  return rules;
};
