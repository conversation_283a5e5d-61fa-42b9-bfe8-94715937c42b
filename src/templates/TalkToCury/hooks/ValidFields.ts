/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export interface DataValidaFields {
  valueSelected: string | number | boolean;
  description: string | null | undefined;
  mainCategory: string;
  MesReferencia__c?: string;
  DataVencOriginalBoleto__c?: string;
  DataVencPagarBoleto__c?: string;
  DataCheque__c?: string;
  ValorCheque__c?: string;
  ClienteEntregouDocumentoParaCartorio__c?: boolean;
  TipoReembolso__c?: string;
}

const isNullOrEmpty = (value: null | undefined | string | boolean): boolean => {
  return (
    value === null || value === undefined || value === 'Selecione o mês' || value === 'R$ 0,00'
  );
};

const validateChequeFields = (data: { DataCheque__c: string; ValorCheque__c: string }) => {
  let message = '';
  const mesRef = data.DataCheque__c;
  const valorCheque = data.ValorCheque__c;

  if (isNullOrEmpty(mesRef)) {
    message = '<p>O campo Data do Cheque é obrigatório.</p>';
  }
  if (isNullOrEmpty(valorCheque)) {
    message += '<p>O campo Valor do Cheque não pode ser R$ 0,00.</p>';
  }

  return message;
};
const validateConcessionariaields = (data: { TipoReembolso__c: string }) => {
  let message = '';
  const { TipoReembolso__c } = data;

  if (isNullOrEmpty(TipoReembolso__c)) {
    message = '<p>Favor selecionar o tipo de reembolso.</p>';
  }

  return message;
};

const validateMonth = (showFieldsExtra: string, data: { MesReferencia__c: string }) => {
  let message = '';
  const mesRef = data.MesReferencia__c;

  if (isNullOrEmpty(mesRef)) {
    message = '<p>O campo Mês é obrigatório.</p>';
  }

  return message;
};

const validateBoletoFields = (data: {
  DataVencOriginalBoleto__c: string;
  DataVencPagarBoleto__c: string;
}) => {
  let message = '';
  const dataOriginal = data.DataVencOriginalBoleto__c;
  const dataPagamento = data.DataVencPagarBoleto__c;

  if (isNullOrEmpty(dataOriginal)) {
    message = 'A data de vencimento original é obrigatória.';
  }
  if (isNullOrEmpty(dataPagamento)) {
    message = 'A data de vencimento para pagamento é obrigatória.';
  }

  return message;
};

const validateAlienacaoFields = (data: { ClienteEntregouDocumentoParaCartorio__c: boolean }) => {
  let message = '';
  const docentregue = data.ClienteEntregouDocumentoParaCartorio__c;
  if (isNullOrEmpty(docentregue)) {
    message = 'Favor selecionar o campo Entrou os documentos no cartório..';
  }

  return message;
};

export const validaFields = (
  showFieldsExtra: string,
  data: DataValidaFields,
  toggleModal: ({ text }: { text?: string }) => void
) => {
  let message = '';

  if (
    [
      'IPTU',
      'Despesas de Financiamento',
      'Condomínio',
      'Multa por atraso de entrega',
      'Concessionária'
    ].includes(showFieldsExtra)
  ) {
    message = validateMonth(showFieldsExtra, {
      MesReferencia__c: data.MesReferencia__c as string
    });
  } else if (showFieldsExtra === 'Boleto - Mês Atual') {
    message = validateBoletoFields({
      DataVencOriginalBoleto__c: data.DataVencOriginalBoleto__c as string,
      DataVencPagarBoleto__c: data.DataVencPagarBoleto__c as string
    });
  } else if (showFieldsExtra === 'Cheque') {
    message = validateChequeFields({
      DataCheque__c: data.DataCheque__c as string,
      ValorCheque__c: data.ValorCheque__c as string
    });
  } else if (showFieldsExtra === 'Alienação Fiduciária') {
    message = validateAlienacaoFields({
      ClienteEntregouDocumentoParaCartorio__c:
        data.ClienteEntregouDocumentoParaCartorio__c as boolean
    });
  } else if (showFieldsExtra === 'Concessionária') {
    message = validateConcessionariaields({
      TipoReembolso__c: data.TipoReembolso__c as string
    });
  }

  if (message) {
    toggleModal({ text: message });
    return false;
  }

  return true;
};
