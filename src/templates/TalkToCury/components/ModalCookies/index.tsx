/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { useAppContext } from '@/contexts/AppContext';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { AnimatePresence, motion } from "motion/react"
import React from 'react';

const ModalCookies: React.FC = () => {
  const { isModalCookieVisible, setModalCookieVisible } = useAppContext();

  BrowserUtils.handleBodyScroll(!isModalCookieVisible);

  if (isModalCookieVisible !== true) return null;

  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key='loading'
        initial={{ opacity: isModalCookieVisible ? 0 : 1 }}
        animate={{ opacity: isModalCookieVisible ? 1 : 0 }}
        exit={{ opacity: isModalCookieVisible ? 0 : 1 }}
        transition={{
          duration: 0.4,
          ease: 'easeInOut'
        }}
        className='absolute flex justify-center items-center w-full h-full bg-[#1A374D99] z-[10000002]'>
        <div
          style={{
            width: '90%',
            height: '70%',
            backgroundColor: 'white',
            padding: '20px',
            zIndex: 1000,
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            borderRadius: '15px',
            border: '2px solid rgb(98 130 154)',
            display: 'flex',
            alignItems: 'center'
          }}>
          <button
            style={{
              position: 'absolute',
              top: '16%',
              right: '6.5%',
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 'bold'
            }}
            onClick={() => setModalCookieVisible(false)}>
            X
          </button>
          <div className='relative font-mono text-left w-full text-navy-blue flex flex-col  items-center h-[90%]'>
            <div className='overflow-y-auto w-[95%]'>
              <h3 className='font-bold mb-2'>POLÍTICA DE COOKIES DO SITE CURY</h3>
              <p className='text-sm mb-2'>Atualização: 08 de Agosto de 2024</p>
              <p className='text-sm mb-2'>
                Esta Política de Cookies (“Política”) aplica-se ao site Cury (https://cury.net)
                (“Plataforma”), da Cury Construtora e Incorporadora S.A., pessoa jurídica de direito
                privado, devidamente inscrita no CNPJ sob o nº CNPJ/MF sob o nº 08.797.760/0001-83,
                estabelecida na Rua Funchal, nº 411, 13º andar, no município de São Paulo, estado de
                São Paulo isoladamente ou em conjunto com suas Sociedades de Propósito Específico
                e/ou as demais empresas que integram seu grupo econômico (“Nós”).
              </p>
              <h3 className='font-bold mb-2'>1. INTRODUÇÃO</h3>
              <p className='text-sm mb-2'>
                Temos a intenção de proporcionar a você, que acessa e utiliza nossa Plataforma
                (“Usuário”) uma experiência ainda mais transparente, explicando o conceito de
                cookies, como os utilizamos e, ainda, as possibilidades de customização do seu uso.
                Caso não concorde com o uso de cookies da forma apresentada, o Usuário poderá se
                recusar a aceitá-los, não clicando em “OK” no aviso que salta à sua tela quando
                acessa nossa plataforma, ajustar as configurações do navegador que utiliza para não
                permitir o uso de cookies ou não acessar a Plataforma. Importante mencionar que a
                desabilitação do uso de cookies poderá impactar a experiência do Usuário ao utilizar
                a nossa Plataforma. Recomendamos também a leitura da nossa Política de Privacidade,
                na qual trazemos mais informações sobre nosso compromisso com a privacidade do
                Usuário e a proteção dos seus dados pessoais. Para quaisquer questões ou dúvidas
                sobre esta Política, o Usuário poderá entrar em contato com o Encarregado da Cury
                pelo endereço eletrônico <EMAIL>.
              </p>
              2. O QUE SÃO COOKIES
              <p className='text-sm mb-2'>
                Cookies são pequenos arquivos digitais em formato de texto que são armazenados no
                dispositivo eletrônico do Usuário quando ele acessa nossa Plataforma. Os cookies
                guardam informações relacionadas às suas preferências, como idioma preferido,
                localização, recorrência das suas sessões e outras variáveis que os desenvolvedores
                da Plataforma consideram relevantes para tornar a sua experiência muito mais
                eficiente.
              </p>
              <h3 className='font-bold mb-2'>3. PARA QUE SERVEM OS COOKIES</h3>
              <p className='text-sm mb-2'>
                Os cookies servem para aprimorar a experiência do Usuário em nossa Plataforma, tanto
                em termos de performance, como em termos de usabilidade, uma vez que os conteúdos
                disponibilizados serão direcionados às suas necessidades e expectativas. Eles também
                podem ser utilizados para realizar estatísticas anônimas que permitem entender como
                os Usuários utilizam a Plataforma, bem como para aprimorar suas estruturas e
                conteúdo. Por serem estatísticas anônimas, não é possível identificá-lo pessoalmente
                por meio desses dados.
              </p>
              <h3 className='font-bold mb-2'>4. COOKIES UTILIZADOS NA PLATAFORMA</h3>
              <p className='text-sm mb-2'>
                Nós utilizamos dois tipos de cookies na nossa Plataforma:
              </p>
              <p className='text-sm mb-2'>
                Cookies de Sessão: são os cookies temporários que permanecem arquivados até que o
                Usuário saia da Plataforma ou encerre o navegador. Cookies Persistentes: são os
                cookies que ficam armazenados no dispositivo do Usuário até que sejam excluídos (o
                tempo que o Cookie permanecerá no dispositivo depende de sua validade e das
                configurações do seu navegador de internet). São esses cookies que são utilizados no
                acesso à Plataforma, mostrando os conteúdos mais relevantes e personalizados de
                acordo com os interesses do Usuário.
              </p>
              <p className='text-sm mb-2'>
                Os cookies (de Sessão ou Persistentes) podem ser categorizados de acordo com sua
                função:
              </p>
              <p className='text-sm mb-2'>
                Cookies Estritamente Necessários: permitem a navegação e utilização das aplicações,
                bem como acessar a áreas seguras da Plataforma. Sem estes cookies, a Plataforma não
                funciona corretamente.
              </p>
              <p className='text-sm mb-2'>
                Cookies Analíticos: coletam dados estatísticos anônimos com a finalidade de analisar
                a utilização da Plataforma e seu respectivo desempenho. Esse tipo de cookies é
                essencial para mantermos uma performance positiva, como: entender quais são as
                páginas mais populares e verificar o motivo dos erros apresentados nas páginas.
                Esses cookies não coletam nenhum dado pessoal.
              </p>
              <p className='text-sm mb-2'>
                Cookies de Funcionalidade: são utilizados para assegurar a disponibilização de
                funcionalidades adicionais da Plataforma ou para guardar as preferências definidas
                pelo Usuário ao navegar na internet, sempre que utilizar o mesmo dispositivo.
              </p>
              <p className='text-sm mb-2'>
                Cookies de Publicidade: coletam as informações de visita em nossa Plataforma para
                que as propagandas nela dispostas sejam mais relevantes para o Usuário e de acordo
                com os seus interesses. Geralmente estão atrelados a cookies de propriedade de
                terceiros. Caso você desabilite estes cookies, note que ainda visualizará as
                publicidades, mas elas serão personalizadas às suas preferências.
              </p>
              <h3 className='font-bold mb-2'>5. COMO CONTROLAR OU ELIMINAR COOKIES</h3>
              <p className='text-sm mb-2'>
                A maioria dos navegadores é configurada para aceitar automaticamente os cookies. O
                Usuário pode alterar as configurações para bloquear o seu uso ou alertá-lo quando um
                cookie estiver sendo enviado para seu dispositivo eletrônico. Existem várias formas
                de gerenciar cookies. Consulte as instruções ou a seção de ajuda, tools ou edit, do
                seu navegador para saber mais sobre como ajustar ou alterar essas configurações.
                Disponibilizamos abaixo alguns links para os navegadores mais conhecidos:
              </p>
              <ul>
                <li>Firefox</li>
                <li>Chrome</li>
                <li>Internet Explorer</li>
                <li>Safari</li>
                <li>Opera</li>
                <li>Microsoft Edge</li>
              </ul>
              <h3 className='font-bold mb-2 mt-4'>6. INFORMAÇÕES DETALHADAS SOBRE OS COOKIES</h3>
              <p className='text-sm mb-2'>
                Apresentamos abaixo os detalhes sobre o uso dos cookies na nossa Plataforma, bem
                como as suas finalidades.
              </p>
              <div className='text-sm mb-2'>
                <div className='overflow-x-auto flex w-[100%] '>
                  <table className='min-w-full bg-white border border-gray-300'>
                    <thead className='bg-gray-800 text-white'>
                      <tr>
                        <th className='w-1/3 px-4 py-2 border-b'>Nome</th>
                        <th className='w-1/3 px-4 py-2 border-b'>Função</th>
                        <th className='w-1/3 px-4 py-2 border-b'>Validade</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className='px-4 py-2 border-b'>user.temp-email</td>
                        <td className='px-4 py-2 border-b'>Email do usuário</td>
                        <td className='px-4 py-2 border-b'>1 dia</td>
                      </tr>
                      <tr className='bg-gray-100'>
                        <td className='px-4 py-2 border-b'>user.temp-token</td>
                        <td className='px-4 py-2 border-b'>Token temporário</td>
                        <td className='px-4 py-2 border-b'>1 dia</td>
                      </tr>
                      <tr>
                        <td className='px-4 py-2 border-b'>user.temp-cnpj</td>
                        <td className='px-4 py-2 border-b'>CNPJ do usuário</td>
                        <td className='px-4 py-2 border-b'>1 dia</td>
                      </tr>
                      <tr className='bg-gray-100'>
                        <td className='px-4 py-2 border-b'>user.temp-cpf</td>
                        <td className='px-4 py-2 border-b'>CPF do usuário</td>
                        <td className='px-4 py-2 border-b'>1 dia</td>
                      </tr>
                      <tr>
                        <td className='px-4 py-2 border-b'>user.temp-userLogin</td>
                        <td className='px-4 py-2 border-b'>CPF ou CNPJ informado</td>
                        <td className='px-4 py-2 border-b'>1 dia</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className='overflow-x-auto flex w-[100%] '>
                  <table className='min-w-full bg-white border border-gray-300'>
                    <thead className='bg-gray-800 text-white'>
                      <tr>
                        <th className='w-1/3 px-4 py-2 border-b'>Nome</th>
                        <th className='w-1/3 px-4 py-2 border-b'>Função</th>
                        <th className='w-1/3 px-4 py-2 border-b'>Validade</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className='px-4 py-2 border-b'>token</td>
                        <td className='px-4 py-2 border-b'>Token de acesso</td>
                        <td className='px-4 py-2 border-b'>60 dias</td>
                      </tr>
                      <tr className='bg-gray-100'>
                        <td className='px-4 py-2 border-b'>AccountId</td>
                        <td className='px-4 py-2 border-b'>ID único alfanumérico</td>
                        <td className='px-4 py-2 border-b'>60 dias</td>
                      </tr>
                      <tr>
                        <td className='px-4 py-2 border-b'>contentSelected</td>
                        <td className='px-4 py-2 border-b'>ID numérico do conteúdo selecionado</td>
                        <td className='px-4 py-2 border-b'>60 dias</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ModalCookies;
