/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import React from 'react';

import { Case } from '@/@types/cases';
import { useAppContext } from '@/contexts/AppContext';
import CustomLink from '@/templates/components/Commons/buttons/CustomLink';
import { DateUtils } from '@/utils/DateUtils';

interface ItemRequestProps {
  caseItem: Case;
}

const ItemRequest: React.FC<ItemRequestProps> = ({ caseItem }: ItemRequestProps) => {
  const formationDate = DateUtils.formatDate(caseItem.LastModifiedDate);
  const { user, content } = useAppContext();
  const baseLink =
    content?.type === 'morador'
      ? `/${user?.AccountId}/fale-com-a-cury/acompanhar-solicitacoes/`
      : `/${user?.AccountId}/sindico/acompanhar-atendimento/`;
  return (
    <tr className='border-b-1 border-white'>
      <td className='py-3 text-navy-blue text-xs tracking-wide flex flex-col mr-2'>
        <CustomLink className='flex flex-col ' href={`${baseLink}${caseItem.CaseId}`}>
          {caseItem.CaseNumber}
          <small className='text-light-blue text-xs tracking-wide'>{formationDate}</small>
        </CustomLink>
      </td>
      <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
        <CustomLink href={`${baseLink}${caseItem.CaseId}`}>
          <p
            className={`font-bold tracking-wide ${caseItem.Status === 'Fechado' ? 'text-blue-tertiary' : 'text-light-blue'} flex text-sm mr-2`}>
            {caseItem.Classificacao__c} - {caseItem.Assunto__c}
          </p>
        </CustomLink>
      </td>
      <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
        <CustomLink href={`${baseLink}${caseItem.CaseId}`}>
          <small
            className={`tracking-wide ${caseItem.Status === 'Fechado' ? 'text-blue-tertiary' : 'text-light-blue'} font-medium flex text-xs`}>
            {caseItem.Status}
          </small>
        </CustomLink>
      </td>
    </tr>
  );
};

export default ItemRequest;
