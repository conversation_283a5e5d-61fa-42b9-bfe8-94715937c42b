/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { Calendar } from 'react-calendar';
import 'react-calendar/dist/Calendar.css';

import { CalendarRequestProps, Value } from '@/@types/Schedule';
import Image from 'next/image';
import { useState } from 'react';

export default function CalendarRequest({ title, field, onChange }: CalendarRequestProps) {
  const [dateToShowSeleted, setDateToShowSeleted] = useState<string>('00/00/0000');
  const selectDate = (ev: Value) => {
    let date: Date | null = null;
    if (ev instanceof Date) {
      date = ev;
    } else if (ev && Array.isArray(ev)) {
      date = ev[0];
    }
    if (date) {
      const dateFormatToShow = date.toLocaleDateString('pt-BR', {
        day: 'numeric',
        month: 'numeric',
        year: 'numeric'
      });
      const day = date.getDay() < 10 ? `0${date.getDay()}` : `${date.getDay()}`;
      const month = date.getMonth() < 10 ? `0${date.getMonth()}` : `${date.getMonth()}`;
      const dateFormat = `${date.getFullYear()}-${month}-${day}`;
      setDateToShowSeleted(dateFormatToShow);
      onChange(dateFormat as unknown as Value, field);
    }
  };
  const [showCalendar, setShowCalendar] = useState<boolean>(false);

  return (
    <>
      <div>
        <label
          className='text-sm text-navy-blue tracking-widest flex mb-1.5 mt-3'
          htmlFor={field as string}>
          {title}
        </label>

        <button
          type='button'
          onClick={() => {
            setShowCalendar(true);
          }}
          className='rounded border-1 flex justify-between items-center border-neutral-blue bg-white h-12 px-3 text-sm text-navy-blue tracking-1 w-full'>
          <span>{dateToShowSeleted}</span>
          <span className='ml-2'>{showCalendar ? '▲' : '▼'}</span>
        </button>
      </div>

      {showCalendar && (
        <div className='bg-navy-blue bg-opacity-90 w-full h-full fixed inset-0 z-50 p-4 flex flex-col justify-center items-center'>
          <div className='bg-white rounded relative px-4 py-14 -mb-10 flex justify-center w-1/3 max-md:w-full'>
            <button
              className='bg-transparent absolute top-1 right-1'
              onClick={() => setShowCalendar(false)}>
              <Image
                src='/images/close.png'
                alt='Icon'
                className='w-auto h-auto'
                width={25}
                height={25}
                priority
              />
            </button>
            <Calendar locale='pt-BR' showNeighboringMonth={false} onChange={selectDate} />
            <button
              className='absolute bottom-2 right-6 bg-navy-blue rounded  text-white px-6 py-2 text-sm'
              onClick={() => setShowCalendar(false)}>
              CONFIRMA
            </button>
          </div>
        </div>
      )}
    </>
  );
}
