/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import React from 'react';

interface MenuModalProps {
  title: string;
  phone: string;
}

const MenuModal: React.FC<MenuModalProps> = ({ title, phone }) => {
  return (
    <div className='flex items-center justify-start p-4 flex-col'>
      <h2 className='text-lg '>{title}</h2>
      <p className='text-sm font-bold'>{phone}</p>
    </div>
  );
};

export default MenuModal;
