/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Case } from '@/@types/cases';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
import { DateUtils } from '@/utils/DateUtils';
import { Suspense, useLayoutEffect, type JSX } from 'react';
import SpinnerLoading from '../components/Commons/Loading/SpinnerLoading';

interface TrackRequestsDetailsScreenProps {
  caseItem: Case | null;
}
export default function TrackRequestsDetailsScreen({
  caseItem
}: Readonly<TrackRequestsDetailsScreenProps>): JSX.Element | null {
  const { isMobile } = useMediaQuery();
  const { user, setSubtitleHeaderMobile, setShowSidebar, content } = useAppContext();
  const { canBeRender } = useCanBeRender();
  const { setLoadingInfo } = useLoading();
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Fale com a Cury');
    setShowSidebar(!isMobile);
    setLoadingInfo(false);
  }, [isMobile]);

  const conditions = [isMobile === null, content === null, caseItem === null, user === null];
  if (!canBeRender({ conditions })) return null;

  const baseLink =
    content?.type === 'morador'
      ? `/${user?.AccountId}/fale-com-a-cury/acompanhar-solicitacoes/`
      : `/${user?.AccountId}/sindico/acompanhar-atendimento/`;

  if (!caseItem) return null;
  return (
    <>
      <Suspense fallback={<SpinnerLoading />}>
        <div className='flex justify-between p-3 bg-white text-navy-blue flex-col md:flew-row'>
          <div className='w-full md:w-[46%] mb-2 md:mb-0'>
            <span className='font-bold'>Número: </span>
            <span className='font-sans text-[0.9rem] text-navy-blue'>{caseItem.CaseNumber}</span>
          </div>
          <div className='w-full md:w-[33%] mb-2 md:mb-0'>
            <span className='font-bold'>Data: </span>
            <span className='font-sans text-[0.9rem] text-navy-blue '>
              {DateUtils.formatDate(caseItem.LastModifiedDate)}
            </span>
          </div>
          <div className='w-full md:w-[33%] '>
            <span className='font-bold'>Status: </span>
            <span className='font-sans text-[0.9rem] text-navy-blue font-bold'>
              {caseItem.Status}
            </span>
          </div>
        </div>

        <div className='grid grid-cols-1 p-4 bg-white border-t-1 border-gray-200'>
          <div className='font-bold'>Assunto: </div>
          <div className='font-sans text-[0.9rem] text-navy-blue'>
            {caseItem.Classificacao__c} - {caseItem.Assunto__c}
          </div>

          <div className='font-[600] text-[1rem] text-navy-blue mt-6 '>
            <span>Empreendimento: </span>
            <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
              {caseItem.NomeEmpreendimento__c}
            </span>
          </div>

          <div className='font-[600] text-[1rem] text-navy-blue'>
            <span>Unidade: </span>
            <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
              {caseItem.AtivoUnidade__c}
            </span>
          </div>

          {caseItem.BlocoFormula__c ? (
            <div className='font-[600] text-[1rem] text-navy-blue'>
              <span>Bloco: </span>
              <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
                {caseItem.BlocoFormula__c}
              </span>
            </div>
          ) : null}

          <div className='font-[600] text-[1rem] text-navy-blue  mt-6 '>
            <span>Tipo de Atendimento: </span>
            <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
              {caseItem.TipoAtendimento__c}
            </span>
          </div>

          <div className='font-[600] text-[1rem] text-navy-blue'>
            <span>Classificação: </span>
            <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
              {caseItem.Classificacao__c}
            </span>
          </div>

          <div className='font-[600] text-[1rem] text-navy-blue'>
            <span>Assunto: </span>
            <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
              {caseItem.Assunto__c}
            </span>
          </div>
          <span className='font-[600] text-[1rem] text-navy-blue  mt-6 '>
            Previsão de encerramento do Caso:
          </span>
          <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
            {DateUtils.formatDate(caseItem.PrevisaoConclusaoSla__c)}
          </span>

          <div className='font-[600] text-[1rem] text-navy-blue  mt-6 '>Mensagem:</div>
          <p className='form-textarea mt-1 block w-full pr-2'>{caseItem.Description}</p>
          {caseItem.work_order.length > 0 && (
            <>
              <span className='font-[600] text-[1rem] text-navy-blue  mt-6 '>Agendamento:</span>
              <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
                Número da ordem de serviço:{' '}
                <strong className='font-[600]'>{caseItem.work_order[0].WorkOrderNumber}</strong>
              </span>
              <span className='font-sans font-light text-[0.9rem] text-navy-blue'>
                Agendado:{' '}
                <strong className='font-[600]'>
                  {DateUtils.formatDateAndTime(
                    caseItem.work_order[0].StartDate,
                    caseItem.work_order[0].EndDate
                  )}
                </strong>
              </span>
            </>
          )}
          <div className='font-[600] text-[1rem] text-navy-blue  mt-6 '>Resposta:</div>
          <p className='form-textarea mt-1 w-full flex flex-col'>
            {caseItem.InformacoesEnvioEmail__c ? (
              <span>{caseItem.InformacoesEnvioEmail__c}</span>
            ) : null}
            {caseItem.StatusNegociacao__c ? (
              <span>
                <strong>Status da Negociação:</strong> {caseItem.StatusNegociacao__c}
              </span>
            ) : null}
            {caseItem.StatusAprovacao__c ? (
              <span>
                <strong>Status Aprovação: </strong>
                {caseItem.StatusAprovacao__c}
              </span>
            ) : null}
            {caseItem.DescricaoAtendimento__c ? (
              <span>
                <strong>Descrição do atendimento: </strong>
                {caseItem.DescricaoAtendimento__c}
              </span>
            ) : null}
          </p>
        </div>
        <div className='w-full flex justify-center '>
          <Buttontransparent color='navy-blue' text='Voltar' route={baseLink} />
        </div>
      </Suspense>
    </>
  );
}
