/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { Case } from '@/@types/cases';
import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';

import { useLoading } from '@/hooks';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useLayoutEffect, type JSX } from 'react';
import TitlePage from '../components/Commons/TitlePage';
import TrackRequestsContent from './content/TrackRequestsContent';

const TrackRequestsScreen = ({ cases }: { cases?: Case[] }): JSX.Element | null => {
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();
  const { setLoadingInfo } = useLoading();
  const { setSubtitleHeaderMobile, setShowSidebar } = useAppContext();

  useLayoutEffect(() => {
    setLoadingInfo(false);
    setSubtitleHeaderMobile('Fale com a Cury');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const conditions = [isMobile === null, cases === null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      {!isMobile && <TitlePage text='Fale com a Cury' />}

      <TrackRequestsContent cases={cases ?? []} />
    </>
  );
};
TrackRequestsScreen.displayName = 'TrackRequestsScreen';
export default TrackRequestsScreen;
