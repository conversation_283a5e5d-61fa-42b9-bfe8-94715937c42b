/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';

import { useAppContext } from '@/contexts/AppContext';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import ButtonSidebar from '@/templates/components/Commons/buttons/ButtonSidebar';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
import InputSelect from '@/templates/components/Commons/inputs/InputSelect';
import Textarea from '@/templates/components/Commons/inputs/Textarea';
import TitlePage from '@/templates/components/Commons/TitlePage';

export default function TalkToCuryMenuDeskScreen() {
  const { user } = useAppContext();
  if (user === null) return null;
  return (
    <div className='max-md:hidden'>
      <div className='w-2/3 max-md:w-full'>
        <TitlePage text='Fale com a Cury' />
        <p className='text-sm text-navy-blue tracking-1 mb-4'>
          Deseja falar com a nossa equipe de atendimento? Você pode preencher os campos abaixo com
          os seus dados e solicitar Assistência Técnica, Cessão de Direitos, Dúvidas Reclamações,
          dentre outros. Estamos aqui para auxiliá-lo.
        </p>
        <div className='grid grid-cols-2 gap-8'>
          <div>
            <InputSelect label='Tipo de Atendimento*:' name={''} />
          </div>
          <div>
            <InputSelect label='Classificação*:' name={''} />
          </div>
          <div className='-mt-3 mb-5'>
            <InputSelect label='Assunto*:' name={''} />
          </div>
          <div className='mt-px mb-5'>
            <p className='text-navy-blue text-sm mb-1'>Anexar arquivos:</p>
            <button className='flex h-12 mb-2.5 bg-white w-full flex-row items-center rounded max-md:flex-col max-md:h-full max-md:bg-transparent max-md:mt-3 focus:outline-none'>
              <div className='w-12 h-full bg-neutral-blue flex flex-row items-center justify-center rounded-tl rounded-bl max-md:rounded max-md:h-16 max-md:w-16 max-md:bg-white focus:outline-none'>
                <Image
                  src='/images/enviar-arquivo.png'
                  alt='Icon'
                  className=''
                  width={25}
                  height={25}
                  priority
                />
              </div>
              <div className='flex flex-row justify-between p-2.5 w-full max-md:justify-center max-md:w-16 max-md:p-0'>
                <p className='tracking-1 font-medium text-sm text-navy-blue max-md:text-10 max-md:tracking-normal max-md:leading-3 max-md:mt-1'>
                  Enviar arquivo
                </p>
                <Image
                  src='/images/angle-down.svg'
                  alt='Icon'
                  className='-rotate-90 max-md:hidden object-contain w-auto h-auto'
                  width={12}
                  height={8}
                  priority
                />
              </div>
            </button>
          </div>
        </div>
        <Textarea name='mensagem' label='Mensagem*:' />
        <div className='flex justify-center'>
          <div className='w-3/12 -mt-3 mb-7 mr-3'>
            <Buttontransparent color='navy-blue' text='Voltar' />
          </div>
          <div className='w-3/12 -mt-3 mb-7'>
            <Buttonblue background='navy-blue' color='white' text='Enviar' route='' />
          </div>
        </div>
      </div>
      <div className='grid grid-cols-3 gap-1'>
        <ButtonSidebar
          src='/images/central-de-relacionamento.png'
          srcMobile='/images/central-de-relacionamento.png'
          text='Central de Relacionamento'
        />
        <ButtonSidebar
          src='/images/fale-com-clara.png'
          srcMobile='/images/fale-com-clara.png'
          text='Fale com a Clara'
          route={`/${user?.AccountId}/fale-com-a-cury/clara`}
        />
        <ButtonSidebar
          src='/images/solicitar-atendimento.png'
          srcMobile='/images/solicitar-atendimento.png'
          text='Solicitar Atendimento'
          route={`/${user?.AccountId}/fale-com-a-cury/solicitar-atendimento`}
        />
      </div>
      <hr className='border-t-1 border-white mb-4 mt-4 w-full' />
      <div className='mt-5'>
        <h4 className='text-navy-blue font-medium text-xl tracking-1 max-md:hidden mb-5'>
          Acompanhar solicitações
        </h4>
        <table className='table-auto w-full'>
          <thead>
            <tr>
              <th className='pb-1.5 text-navy-blue text-sm font-normal tracking-wide text-left pl-3'>
                Nº/Data
              </th>
              <th className='pb-1.5 text-navy-blue text-sm font-normal tracking-wide text-left'>
                Assunto
              </th>
              <th className='pb-1.5 text-navy-blue text-sm font-normal tracking-wide text-left'>
                Status
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className='border-b-1 border-white bg-white'>
              <td className='py-3 text-navy-blue text-xs tracking-wide flex flex-col mr-2 pl-3'>
                0000000 <small className='text-light-blue text-xs tracking-wide'>18/03/2023</small>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <p className='font-bold tracking-wide text-navy-blue flex text-sm mr-2'>
                  Contrato - Financiamento bancário
                </p>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <small className='tracking-wide text-navy-blue font-bold flex text-xs'>
                  Em andamento
                </small>
              </td>
            </tr>
            <tr className='border-b-1 border-white'>
              <td className='py-3 text-navy-blue text-xs tracking-wide flex flex-col pl-3'>
                0000000 <small className='text-light-blue text-xs tracking-wide'>18/03/2023</small>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <p className='tracking-wide text-navy-blue font-medium flex text-sm'>
                  Assistência Técnica - Unidade
                </p>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <small className='tracking-wide text-blue-tertiary font-medium flex text-xs'>
                  Concluída
                </small>
              </td>
            </tr>
            <tr className='border-b-1 border-white bg-white'>
              <td className='py-3 text-navy-blue text-xs tracking-wide flex flex-col mr-2 pl-3'>
                0000000 <small className='text-light-blue text-xs tracking-wide'>18/03/2023</small>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <p className='font-bold tracking-wide text-navy-blue flex text-sm mr-2'>
                  Contrato - Financiamento bancário
                </p>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <small className='tracking-wide text-navy-blue font-bold flex text-xs'>
                  Em andamento
                </small>
              </td>
            </tr>
            <tr className='border-b-1 border-white'>
              <td className='py-3 text-navy-blue text-xs tracking-wide flex flex-col pl-3'>
                0000000 <small className='text-light-blue text-xs tracking-wide'>18/03/2023</small>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <p className='tracking-wide text-navy-blue font-medium flex text-sm'>
                  Assistência Técnica - Unidade
                </p>
              </td>
              <td className='py-3 text-navy-blue text-sm tracking-wide text-left'>
                <small className='tracking-wide text-blue-tertiary font-medium flex text-xs'>
                  Concluída
                </small>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}
