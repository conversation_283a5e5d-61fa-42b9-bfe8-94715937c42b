/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Options, RequestOptions } from '@/@types/Requests';

export const classificacaoOptions: Options[] = [
  { id: 0, value: 'Selecione uma opção', name: 'Selecione uma opção' },
  { id: 1, value: 'Atendimento', name: 'Atendimento' },
  { id: 2, value: 'Documento<PERSON>', name: 'Documentos' },
  { id: 3, value: 'Financeiro', name: 'Financeiro' },
  { id: 4, value: 'Pagamento/Reembolso', name: 'Pagamento/Reembolso' },
  { id: 5, value: 'Financiamento', name: 'Financiamento' }
];

export const mesOptions: Options[] = [
  { id: 0, value: 'Selecione o mês', name: 'Selecione o mês' },
  { id: 1, value: 'Janeiro', name: 'Janeiro' },
  { id: 2, value: 'Fevereiro', name: 'Fevereiro' },
  { id: 3, value: 'Março', name: 'Março' },
  { id: 4, value: 'Abril', name: 'Abril' },
  { id: 5, value: 'Maio', name: 'Maio' },
  { id: 6, value: 'Junho', name: 'Junho' },
  { id: 7, value: 'Julho', name: 'Julho' },
  { id: 8, value: 'Agosto', name: 'Agosto' },
  { id: 9, value: 'Setembro', name: 'Setembro' },
  { id: 10, value: 'Outubro', name: 'Outubro' },
  { id: 11, value: 'Novembro', name: 'Novembro' },
  { id: 12, value: 'Dezembro', name: 'Dezembro' }
];
export const DocEntregueCartorioOptions: Options[] = [
  {
    id: 0,
    value: 'Entregou documentos para o cartório?',
    name: 'Entregou documentos para o cartório?'
  },
  { id: 1, value: true, name: 'Sim' },
  { id: 2, value: false, name: 'Não' }
];
export const TipoReembolsoOptions: Options[] = [
  {
    id: 0,
    value: 'Tipo de Reembolso',
    name: 'Tipo de Reembolso'
  },
  { id: 1, value: 'Água', name: 'Água' },
  { id: 2, value: 'Energia Elétrica', name: 'Energia Elétrica' }
];
export const requestOption: RequestOptions = {
  'Atendimento': [
    {
      id: 0,
      value: 0,
      name: 'Selecione uma opção de Atendimento'
    },
    {
      id: 1,
      name: 'Distrato',
      value: 1,
      RecordName: 'Distrato',
      OwnerName: 'Relacionamento com Cliente'
      // Rules:
    },
    {
      id: 2,
      name: 'Troca de Unidade',
      value: 2,
      RecordName: 'Distrato',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 3,
      name: 'Programa Chega Mais',
      value: 3,
      RecordName: 'Chega Mais',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 4,
      name: 'Patrimônio de Afetação',
      value: 4,
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 5,
      name: 'Promoções - Comerciais',
      value: 5,
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 6,
      name: 'Informações contratos',
      value: 6,
      RecordName: 'Informações RCC',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 7,
      name: 'Informações técnicas',
      value: 7,
      RecordName: 'Informações RCC',
      OwnerName: 'Relacionamento com Cliente'
    }
  ],
  'Documentos': [
    { id: 0, value: 0, name: 'Selecione uma opção de Documentos' },
    {
      id: 1,
      value: 1,
      RecordName: 'Informações Áreas',
      name: 'Contrato - Compra e Venda',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 2,
      value: 2,
      name: 'Empreendimento',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 3,
      value: 3,
      name: 'Contrato - Financiamento bancário',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },

    {
      id: 4,
      value: 4,
      name: 'Termo de baixa de hipoteca',
      RecordName: 'Baixa de Hipoteca',
      OwnerName: 'Relacionamento com Cliente'
    },

    {
      id: 5,
      value: 5,
      name: 'Termo de Quitação',
      RecordName: 'Termo de Quitação',
      OwnerName: 'Envio - Termo de Quitação'
    }
  ],
  'Financeiro': [
    {
      id: 0,
      value: 'Selecione uma opção para Financeiro',
      name: 'Selecione uma opção'
    },

    {
      id: 3,
      value: 3,
      name: 'Antecipação de Valores',
      RecordName: 'Termo de Quitação',
      OwnerName: 'Envio - Termo de Quitação'
    },
    {
      id: 4,
      value: 4,
      name: 'Boleto - Em atraso',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 5,
      value: 5,
      name: 'Boleto - Mês Atual',
      RecordName: 'Boleto',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 6,
      value: 6,
      name: 'Boleto - Outros Vencimentos',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 7,
      value: 7,
      name: 'Cheque',
      RecordName: 'Solicitação de Cheque Devolvido',
      OwnerName: 'Cheque Devolvido'
    },
    {
      id: 8,
      value: 8,
      name: 'Comprovante de Pagamento',
      RecordName: 'Baixa de Valores',
      OwnerName: 'Baixa de Valores'
    },
    {
      id: 9,
      value: 9,
      name: 'Extrato Financeiro',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 10,
      value: 10,
      name: 'Informe de pagamentos (IR)',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 11,
      value: 11,
      name: 'Portal Antecipação – Boleto', //
      RecordName: 'Boleto',
      OwnerName: 'Emissão de Boleto'
    },
    {
      id: 12,
      value: 12,
      name: 'Renegociação',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 13,
      value: 13,
      name: 'Taxa de Concessionárias',
      RecordName: 'Taxa de Concessionária',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 14,
      value: 14,
      name: 'Taxa de obra',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 15,
      value: 15,
      name: 'Baixa de Valores',
      RecordName: 'Baixa de Valores',
      OwnerName: 'Baixa de Valores'
    }
  ],
  'Pagamento/Reembolso': [
    { id: 0, value: 0, name: 'Selecione uma opção de Pagamento/Reembolso' },
    {
      id: 1,
      value: 1,
      name: 'IPTU',
      RecordName: 'Pagamento Reembolso',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 2,
      value: 2,
      name: 'Despesas de Financiamento',
      RecordName: 'Pagamento Reembolso',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 3,
      value: 3,
      name: 'Condomínio',
      RecordName: 'Pagamento Reembolso',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 4,
      value: 4,
      name: 'Concessionária',
      RecordName: 'Pagamento Reembolso',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 5,
      value: 5,
      name: 'Multa por atraso de entrega',
      RecordName: 'Pagamento Reembolso',
      OwnerName: 'Relacionamento com Cliente'
    }
  ],
  'Financiamento': [
    { id: 0, value: 0, name: 'Selecione uma opção de Financiamento' },
    {
      id: 1,
      value: 1,
      name: 'Repasse',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 2,
      value: 2,
      name: 'Registro',
      RecordName: 'Informações Áreas',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 3,
      value: 3,
      name: 'Escritura definitiva',
      RecordName: 'Escritura Definitiva',
      OwnerName: 'Relacionamento com Cliente'
    },
    {
      id: 4,
      value: 4,
      name: 'Alienação Fiduciária',
      RecordName: 'Alienação Fiduciária',
      OwnerName: 'Alienação Fiduciária'
    }
  ]
};
