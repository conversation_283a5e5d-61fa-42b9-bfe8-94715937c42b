/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import ButtonUpload from '@/templates/components/Commons/buttons/ButtonUpload';
import CustomDropdown from '@/templates/components/Commons/inputs/CustomDropdown';
import Input from '@/templates/components/Commons/inputs/Input';

import 'react-calendar/dist/Calendar.css';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { ChangeEvent, useEffect, useLayoutEffect } from 'react';
import TitlePage from '../components/Commons/TitlePage';
import CalendarRequest from './components/CalendarRequest';
import { IFormInputRequestService, useRequestService } from './hooks/useRequestService';
import {
  classificacaoOptions,
  DocEntregueCartorioOptions,
  mesOptions,
  TipoReembolsoOptions
} from './mock/dataRequestOptions';

type ValuePiece = Date | null;
export type Value = ValuePiece | [ValuePiece, ValuePiece];

export default function RequestServiceScreen() {
  const { isMobile } = useMediaQuery();
  const { content, setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  const { canBeRender } = useCanBeRender();
  const {
    handleSubmit,
    register,
    handleSubmitForm,
    classificacaoDropdownRef,
    mesDropdownRef,
    DocEntregueCartorioDropdownRef,
    TipoReembolsoDropdownRef,
    subDropdownRef,
    handleDropDown,
    handleDropAssunto,
    fileInputRef,
    handleFileChange,
    handleButtonClick,
    closeAll,
    fileCase,
    handleData,
    showFieldsExtra
  } = useRequestService();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Solicitar Atendimento');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [isMobile === null, content === null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <div className='w-full md:w-10/12 mb-10'>
        {isMobile && <TitlePage text='Fale com a Cury' />}
        <p className='text-sm text-navy-blue tracking-1 mb-2'>
          Deseja falar com a nossa equipe de atendimento? Você pode preencher os campos abaixo com
          os seus dados e solicitar Assistência Técnica, Cessão de Direitos, Dúvidas Reclamações,
          dentre outros. Estamos aqui para auxiliá-lo.
        </p>

        <hr className='border-t-1 border-white mt-4 mb-5 w-full' />

        <form onSubmit={handleSubmit(handleSubmitForm)}>
          {content?.type === 'morador' ? (
            <div className='md:grid md:grid-cols-2 md:gap-4'>
              <CustomDropdown
                ref={classificacaoDropdownRef}
                label={'Classificação*:'}
                optionsExternal={classificacaoOptions}
                onChange={handleDropDown}
                defaultValue='Selecione a classificação'
                onClose={closeAll}
              />

              <CustomDropdown
                ref={subDropdownRef}
                label={'Assunto*:'}
                optionsExternal={[{ id: 0, value: '', name: '' }]}
                onChange={handleDropAssunto}
                defaultValue='Selecione primeiro a classificação'
                onClose={closeAll}
              />
              {showFieldsExtra === 'Cheque' && (
                <>
                  <Input
                    {...register('ValorCheque__c')}
                    type='input'
                    label='Valor do cheque:'
                    onChange={(e: ChangeEvent<HTMLInputElement>) => {
                      handleData(e.target.value, e.target.name as keyof IFormInputRequestService);
                    }}
                    defaultValue={'R$ 0,00'}
                    name={'ValorCheque__c'}
                  />
                  <CalendarRequest
                    onChange={(value: Value, field: string | number | symbol) => {
                      handleData(value, field as keyof IFormInputRequestService);
                    }}
                    field='DataCheque__c'
                    title='Data do cheque *:'
                  />
                </>
              )}
              {(showFieldsExtra === 'IPTU' ||
                showFieldsExtra === 'Despesas de Financiamento' ||
                showFieldsExtra === 'Condomínio' ||
                showFieldsExtra === 'Multa por atraso de entrega' ||
                showFieldsExtra === 'Concessionária') && (
                <CustomDropdown
                  ref={mesDropdownRef}
                  label={'Mês*:'}
                  optionsExternal={mesOptions}
                  onChange={(value) => handleData(value, 'MesReferencia__c')}
                  onClose={closeAll}
                />
              )}
              {showFieldsExtra === 'Concessionária' && (
                <CustomDropdown
                  ref={TipoReembolsoDropdownRef}
                  label={'Tipo de Reembolso*:'}
                  optionsExternal={TipoReembolsoOptions}
                  onChange={(
                    value: string | number | boolean | Value | null,
                    name?: string | undefined,
                    field?: keyof IFormInputRequestService | undefined
                  ) => handleData(value, field as keyof IFormInputRequestService)}
                  name={'TipoReembolso__c'}
                  defaultValue=''
                  onClose={closeAll}
                />
              )}

              {showFieldsExtra === 'Alienação Fiduciária' && (
                <CustomDropdown
                  ref={DocEntregueCartorioDropdownRef}
                  label={'Entregou os documentos no cartório? *:'}
                  optionsExternal={DocEntregueCartorioOptions}
                  onChange={(
                    value: string | number | boolean | Value | null,
                    name?: string | undefined,
                    field?: keyof IFormInputRequestService | undefined
                  ) => handleData(value, field as keyof IFormInputRequestService)}
                  name={'ClienteEntregouDocumentoParaCartorio__c'}
                  defaultValue={'Não'}
                  onClose={closeAll}
                />
              )}

              {showFieldsExtra === 'Boleto - Mês Atual' && (
                <>
                  <CalendarRequest
                    onChange={(value: Value, field: string | number | symbol) => {
                      handleData(value, field as keyof IFormInputRequestService);
                    }}
                    field='DataVencOriginalBoleto__c'
                    title='Data de Vencimento original *:'
                  />
                  <CalendarRequest
                    onChange={(value: Value, field: string | number | symbol) => {
                      handleData(value, field as keyof IFormInputRequestService);
                    }}
                    field='DataVencPagarBoleto__c'
                    title='Data de Vencimento para pagamento *:'
                  />
                </>
              )}
            </div>
          ) : null}
          <ButtonUpload
            src='/images/enviar-arquivo.png'
            fileInputRef={fileInputRef}
            handleFileChange={handleFileChange}
            handleButtonClick={handleButtonClick}
            fileName={fileCase?.name}
          />

          <Input {...register('description')} type='textarea' label='Mensagem:' rows={4} />

          <div className='w-full flex justify-center'>
            <Buttonblue background='navy-blue' color='white' text='Enviar' classExtra='!mt-4' />
          </div>
        </form>
      </div>
    </>
  );
}
