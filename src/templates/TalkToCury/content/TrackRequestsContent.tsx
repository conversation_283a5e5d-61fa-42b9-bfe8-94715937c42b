/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { Case } from '@/@types/cases';
import { useAppContext } from '@/contexts/AppContext';
import TitlePage from '@/templates/components/Commons/TitlePage';
import { DateUtils } from '@/utils/DateUtils';
import { type JSX } from 'react';
import ItemRequest from '../components/ItemRequest';

const TableHeader = ({ text }: { text: string }) => {
  return (
    <th className='pb-1.5 text-navy-blue text-sm font-normal tracking-wide text-left'>{text}</th>
  );
};

const TITLE_SOLICITACOES = 'Acompanhar solicitações';
const TrackRequestsContent = ({ cases }: { cases?: Case[] }): JSX.Element | null => {
  const { content } = useAppContext();
  return (
    <>
      <div className='mt-8'>
        {content?.type === 'morador' ? (
          <p className='font-bold text-base text-navy-blue tracking-1 mt-2 mb-4'>
            {TITLE_SOLICITACOES}
          </p>
        ) : (
          <TitlePage text={TITLE_SOLICITACOES} />
        )}
        {cases?.length === 0 ? (
          <p className='font-bold text-base text-navy-blue tracking-1 mt-2 mb-4 bg-white p-4 text-center'>
            Você ainda não possui solicitações.
          </p>
        ) : (
          <table className='table-auto w-full'>
            <thead>
              <tr>
                <TableHeader text='Nº/Data' />
                <TableHeader text='Assunto' />
                <TableHeader text='Status' />
              </tr>
            </thead>
            <tbody>
              {cases &&
                cases
                  ?.sort((a, b) => {
                    const dateA = new Date(DateUtils.formatDate(a.LastModifiedDate));
                    const dateB = new Date(DateUtils.formatDate(b.LastModifiedDate));
                    return dateB.getTime() - dateA.getTime();
                  })
                  .map((caseItem) => <ItemRequest key={caseItem.CaseId} caseItem={caseItem} />)}
            </tbody>
          </table>
        )}
      </div>
    </>
  );
};
TrackRequestsContent.displayName = 'TrackRequestsContent';
export default TrackRequestsContent;
