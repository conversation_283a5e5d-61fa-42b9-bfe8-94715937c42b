/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useAppContext } from '@/contexts/AppContext';
import { useEffect, useLayoutEffect, type JSX } from 'react';

import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';

export default function JusticeirasProjectScreen(): JSX.Element | null {
  const { isMobile } = useMediaQuery();
  const { user, setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  const { canBeRender } = useCanBeRender();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Cury e Projetos Justiceiras');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const url =
    'https://docs.google.com/forms/d/e/1FAIpQLSft--ccomNpgfVaU0O9Xjpmg_vLmhHsKZ8SG5YiphdMRshpgg/viewform';
  function openUrlBrowser() {
    const postData = {
      type: 'openUrlBrowser',
      url,
      data: {}
    };

    BrowserUtils.ActionReactNative(postData);
  }

  const conditions = [isMobile === null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <div className=''>
        <p className='text-base text-navy-blue tracking-1 leading-5 mt-5 text-bold'>
          Cury e Projeto Justiceiras
        </p>
        <p className='text-sm text-navy-blue tracking-1 leading-5 mt-4'>
          A Cury está totalmente engajada na luta contra a violência feminina. Trabalhamos
          diariamente na construção do sonho de milhares de brasileiros e brasileiras. Nossos
          empreendimentos são objeto de desejo de pessoas de diferentes faixas etárias e classes
          sociais. Dessa forma, buscando promover um ambiente social e um ambiente corporativo cada
          vez mais seguros, em parceria com o “Projeto Justiceiras”, o Programa de Integridade Cury
          (PIC) disponibiliza um link de comunicação exclusivo e focado na orientação jurídica,
          psicológica, socioassistencial, médica e toda rede de apoio e acolhimento gratuito para
          mulheres em situação de vulnerabilidade.
        </p>
        <p className='text-sm text-navy-blue tracking-1 leading-5 mt-4'>
          Importante mencionar que o Projeto pode ser acessado por toda e qualquer mulher, sejam
          elas colaboradoras, prestadoras de serviços ou clientes Cury. Basta clicar no link abaixo
          e seguir as orientações. Após a análise das informações prestadas pelo(a) denunciante,
          inicia-se o contato com a vítima através de um grupo profissional e multidisciplinar, com
          a finalidade de auxiliar na busca dos seus direitos.{' '}
        </p>
        <p className='text-sm text-navy-blue tracking-1 leading-5 mt-4'>
          Uma vida livre de violência é um direito de toda mulher!
        </p>
        <div className='w-full mr-1 flex justify-around'>
          <div className={'w-[160px]'}>
            <Buttonblue
              text='Voltar'
              background='navy-blue'
              color='white'
              route={`/${user?.AccountId}/fale-com-a-cury`}
            />
          </div>
          <div className={'w-[160px]'}>
            <Buttonblue
              background='navy-blue'
              color='white'
              text='Obtenha ajuda aqui!'
              route={DeviceUtils.isMobileApp() ? null : url}
              onClick={DeviceUtils.isMobileApp() ? () => openUrlBrowser() : undefined}
              target='_blank'
            />
          </div>
        </div>
      </div>
    </>
  );
}
