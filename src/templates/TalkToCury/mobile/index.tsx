/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import ButtonImage from '@/templates/components/Commons/buttons/ButtonImage';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import Link from 'next/link';

export default function TalkToCuryMenuMobileScreen() {
  const { setModalCookieVisible, setIsModalVisible, user } = useAppContext();

  function openUrlBrowser() {
    const postData = {
      type: 'openUrlBrowser',
      url: 'https://cury.net/politica-de-privacidade',
      data: {}
    };

    BrowserUtils.ActionReactNative(postData);
  }
  if (user === null) return null;
  return (
    <div className='hidden max-md:block'>
      <ButtonImage
        text='Central de Relacionamento'
        src='/images/central-de-relacionamento.png'
        onClick={() => setIsModalVisible(true)}
      />
      <ButtonImage
        text='Fale com a Clara'
        src='/images/fale-com-clara.png'
        route={`/${user?.AccountId}/fale-com-a-cury/clara`}
      />
      <ButtonImage
        text='Solicitar Atendimento'
        src='/images/solicitar-atendimento.png'
        route={`/${user?.AccountId}/fale-com-a-cury/solicitar-atendimento`}
      />
      <ButtonImage
        text='Acompanhar Solicitações'
        src='/images/acompanhar-solicitacoes.png'
        route={`/${user?.AccountId}/fale-com-a-cury/acompanhar-solicitacoes`}
      />
      {DeviceUtils.isMobileApp() ? (
        <ButtonImage
          text='Política de privacidade'
          src='/images/politica-de-privacidade.png'
          onClick={openUrlBrowser}
        />
      ) : (
        <Link
          href='https://cury.net/politica-de-privacidade'
          target='_blank'
          rel='noopener noreferrer'>
          <ButtonImage text='Política de privacidade' src='/images/politica-de-privacidade.png' />
        </Link>
      )}
      <ButtonImage
        text='Política de cookies'
        src='/images/politica-de-cookies.png'
        onClick={() => setModalCookieVisible(true)}
      />
      <ButtonImage
        text='Cury e Projeto Justiceiras'
        src='/images/justiceiras.png'
        route={`/${user?.AccountId}/fale-com-a-cury/projeto-justiceiras`}
      />
    </div>
  );
}
