/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { ENV } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { usePathname } from 'next/navigation';
import Script from 'next/script';
import { useCallback, useEffect } from 'react';

const ClaraContent: React.FC = () => {
  const { user } = useAppContext();
  const path = usePathname();
  const isMobile = BrowserUtils.getSizeWindow().maxWidth < 450;
  const isTargetPage = path.endsWith('/clara');

  const removeInertAndPreventReaddition = useCallback(() => {
    const removeInert = () => {
      document.querySelectorAll('[inert]').forEach((elem) => {
        elem.removeAttribute('inert');
      });
    };

    removeInert();

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'inert') {
          removeInert();
        }
      });
    });

    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ['inert']
    });

    return () => observer.disconnect();
  }, []);

  const handleChatVisibility = useCallback(() => {
    const embeddedMessagingFrame = document.getElementById('embedded-messaging');
    const blockheader = document.getElementById('blockheader');

    if (!embeddedMessagingFrame) return;

    if (isMobile) {
      embeddedMessagingFrame.style.display = isTargetPage ? 'block' : 'none';
      if (blockheader) blockheader.style.display = isTargetPage ? 'flex' : 'none';

      if (isTargetPage) {
        const interval = setInterval(() => {
          const helpButton = document.querySelector('.embeddedMessagingConversationButton');
          const embeddedMessagingModalOverlay = document.querySelector(
            '#embeddedMessagingModalOverlay'
          );

          if (helpButton) {
            clearInterval(interval);
            if (embeddedMessagingModalOverlay === null) {
              (helpButton as HTMLButtonElement).click();
            } else {
              embeddedMessagingFrame.style.display = 'block';
              if (blockheader) blockheader.style.display = 'flex';
            }
          }
        }, 200);
      }
    }
  }, [isMobile, isTargetPage]);

  const initEmbeddedMessaging = useCallback(() => {
    if (typeof window === 'undefined') return false;

    try {
      const settings = {
        language: 'pt_BR',

        displayHelpButton: true
      };

      Object.assign(window.embeddedservice_bootstrap.settings, settings);

      const initConfig =
        ENV === 'production'
          ? {
            orgId: '00D1U000000qzO3',
            eswConfigDevName: 'Clara',
            baseLiveAgentURL: 'https://curyconstrutora.my.site.com/ESWClara1711495599408',
            scrt2URL: 'https://curyconstrutora.my.salesforce-scrt.com'
          }
          : {
            orgId: '00DHZ00000272Lh',
            eswConfigDevName: 'Clara',
            baseLiveAgentURL:
                'https://curyconstrutora--uatfull.sandbox.my.site.com/ESWClaraUAT1715366861998',
            scrt2URL: 'https://curyconstrutora--uatfull.sandbox.my.salesforce-scrt.com'
          };

      window.embeddedservice_bootstrap.init(
        initConfig.orgId,
        initConfig.eswConfigDevName,
        initConfig.baseLiveAgentURL,
        { scrt2URL: initConfig.scrt2URL }
      );

      handleChatVisibility();

      removeInertAndPreventReaddition();
      return true;
    } catch (err) {
      console.error('Error loading Embedded Messaging: ', err);
      return false;
    }
  }, [handleChatVisibility, removeInertAndPreventReaddition]);

  useEffect(() => {
    if (!user) return;
    removeInertAndPreventReaddition();
  }, [user, removeInertAndPreventReaddition]);

  useEffect(() => {
    if (!user) return;
    handleChatVisibility();
  }, [path]);

  if (!user) return null;

  const srcClara =
    ENV === 'production'
      ? 'https://curyconstrutora.my.site.com/ESWClara1711495599408/assets/js/bootstrap.min.js'
      : 'https://curyconstrutora--uatfull.sandbox.my.site.com/ESWClaraUAT1715366861998/assets/js/bootstrap.min.js';

  return <Script src={srcClara} onLoad={initEmbeddedMessaging} />;
};

export default ClaraContent;
