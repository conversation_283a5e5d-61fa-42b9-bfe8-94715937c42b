/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading, useMediaQuery } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useEffect, useLayoutEffect } from 'react';
import ClaraContent from './ClaraContent';

const ClaraScreen = () => {
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();
  const { setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Fale com a Cury');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [isMobile === null];
  if (!canBeRender({ conditions })) return null;

  return <ClaraContent />;
};

export default ClaraScreen;
