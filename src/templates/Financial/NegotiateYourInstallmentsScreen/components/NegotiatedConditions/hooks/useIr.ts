/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { IrResponse } from '@/@types/responses';
import { useAppContext } from '@/contexts/AppContext';
import { useInforme } from '@/contexts/useInforme';
import { useModal } from '@/hooks';
import { useInstallments } from '@/hooks/Installments/useInstallments';
import { DateUtils } from '@/utils/DateUtils';
import { downloadFile } from '@/utils/DownloadUtils';
import { StringUtils } from '@/utils/StringUtils';

export const useIr = () => {
  const { user, content } = useAppContext();
  const { showInforme, setShowInforme } = useInforme();
  const { getIr } = useInstallments();
  const { toggleModal } = useModal();
  const yearList = DateUtils.createYearList(user?.CreatedDate as string);

  const handleIr = async (
    sendEmail: boolean,
    year: string = (new Date().getFullYear() - 1).toString()
  ) => {
    if (user && content) {
      try {
        if (year === '') return false;
        const filename = `${StringUtils.normalizeText(user.Name)}-informe-rendimentos-cury-${year}.pdf`;
        toggleModal({
          text: 'Iniciando download do informe de rendimentos...'
        });
        const response = (await getIr({
          customerId: user?.CodigoSienge__c,
          companyId: content?.Empreendimento.CodigoSiengeSPE,
          year,
          filename,
          sendEmail
        })) as IrResponse | boolean;
        setShowInforme(false);
        if (response === null) {
          toggleModal({
            text: 'Não foi encontrado nenhum informe para o ano informado.'
          });
          return false;
        } else {
          toggleModal({
            text: sendEmail
              ? 'Informe de rendimentos enviado para seu email.'
              : 'Download iniciado. Por favor, aguarde.'
          });
        }
        const irResponse = response as IrResponse;
        if (irResponse.irPDF && !sendEmail) {
          const srcBoleto = irResponse.irPDF;

          await downloadFile(srcBoleto, filename, toggleModal);
        }
        return true;
      } catch (e) {
        toggleModal({
          text: 'Não foi encontrado nenhum informe para o ano informado.'
        });
        return false;
      }
    }
    return false;
  };

  return {
    yearList,
    showInforme,
    setShowInforme,
    handleIr
  };
};
