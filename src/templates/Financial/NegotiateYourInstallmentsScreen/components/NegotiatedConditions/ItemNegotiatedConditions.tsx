/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';
import { idKey, TitleKey } from './CardNegotiatedConditions';

interface ItemNegotiatedConditionsProps {
  id: idKey;
  title: TitleKey;
  value: string;
}

const titles: { [key in TitleKey]: { [key in idKey]: string } } = {
  'Valor total selecionado': {
    QuitacaoSaldoDevedor: '',
    QuitacaoSaldoVencido: '',
    ParcelamentoSaldoDevedor: '',
    ParcelamentoSaldoVencido: '',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: ''
  },
  'Desconto': {
    QuitacaoSaldoDevedor: '',
    QuitacaoSaldoVencido: '',
    ParcelamentoSaldoDevedor: '',
    ParcelamentoSaldoVencido: '',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: ''
  },
  'Valor a pagar': {
    QuitacaoSaldoDevedor: '',
    QuitacaoSaldoVencido: '',
    ParcelamentoSaldoDevedor: '',
    ParcelamentoSaldoVencido: '',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: ''
  },
  'Prazo para pagamento': {
    QuitacaoSaldoDevedor: '',
    QuitacaoSaldoVencido: '',
    ParcelamentoSaldoDevedor: '',
    ParcelamentoSaldoVencido: '',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: ''
  },
  'total': {
    QuitacaoSaldoDevedor: 'Saldo Devedor Atual:',
    QuitacaoSaldoVencido: 'Saldo Vencido Atual:',
    ParcelamentoSaldoDevedor: 'Saldo Devedor Atual:',
    ParcelamentoSaldoVencido: 'Saldo Vencido Atual:',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: 'Valor total selecionado'
  },
  'desconto': {
    QuitacaoSaldoDevedor: 'Desconto:',
    QuitacaoSaldoVencido: 'Desconto:',
    ParcelamentoSaldoDevedor: 'Desconto:',
    ParcelamentoSaldoVencido: 'Desconto:',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: 'Desconto'
  },
  'totalAPagar': {
    QuitacaoSaldoDevedor: ' Total a Pagar:',
    QuitacaoSaldoVencido: ' Total a Pagar:',
    ParcelamentoSaldoDevedor: ' Total a Pagar:',
    ParcelamentoSaldoVencido: ' Total a Pagar:',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: 'Valor a pagar'
  },
  'entrada': {
    QuitacaoSaldoDevedor: 'Entrada de:',
    QuitacaoSaldoVencido: 'Entrada de:',
    ParcelamentoSaldoDevedor: 'Entrada de:',
    ParcelamentoSaldoVencido: 'Entrada de:',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: 'Entrada de:'
  },
  'vencimento': {
    QuitacaoSaldoDevedor: 'Vencimento:',
    QuitacaoSaldoVencido: 'Vencimento:',
    ParcelamentoSaldoDevedor: 'Vencimento:',
    ParcelamentoSaldoVencido: 'Vencimento:',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: 'Prazo para pagamento'
  },
  'parcela': {
    QuitacaoSaldoDevedor: 'Total de Parcelas :',
    QuitacaoSaldoVencido: 'Total de Parcelas :',
    ParcelamentoSaldoDevedor: 'Total de Parcelas :',
    ParcelamentoSaldoVencido: 'Total de Parcelas :',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: ''
  },
  'valor parcela': {
    QuitacaoSaldoDevedor: 'Parcelas de :',
    QuitacaoSaldoVencido: 'Parcelas de :',
    ParcelamentoSaldoDevedor: 'Parcelas de :',
    ParcelamentoSaldoVencido: 'Parcelas de :',
    NegociacaoParcialDisponivel: '',
    NegociacaoParcialSimulacao: ''
  }
};

export default function ItemNegotiatedConditions({
  id,
  title,
  value
}: ItemNegotiatedConditionsProps) {
  if (value === '0.00' || value === null) return false;
  const titleCard = id ? titles[title][id] : title;
  let formattedValue = '';
  if (title === 'vencimento' || title === 'Prazo para pagamento') {
    formattedValue = DateUtils.formatDate(value);
  } else if (title === 'parcela') {
    formattedValue = value;
  } else {
    formattedValue = FinanceUtils.MoneyFormat.format(Number(value));
  }

  return (
    <div className={'flex flex-row justify-between items-center  mb-4 '}>
      <small className='text-xs text-neutral-blue tracking-1 flex capitalize'>{titleCard}</small>
      <p className='tracking-1 text-navy-blue text-sm leading-4'>{formattedValue}</p>
    </div>
  );
}
