
/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import React from 'react';

import { InstallmentItem, SelectedInstallment, SelectedInstallments } from '@/@types/installments';
import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import { DateUtils } from '@/utils/DateUtils';
import { InnerTextCondicions, TextCondicions, TitleCondicions } from '../../mock/TextsCondicions';
import CardInstallmentNegotiate from '../CardInstallmentNegotiate';

interface InstalltmentsProps {
  filteredInstallments: InstallmentItem[] | null;
  selectInstallments: (selectedInstallment: SelectedInstallment) => void;
  simulationAction: () => void;
  selectedInstallments: SelectedInstallments | null;
  DataAntecipacaoDesconto__c: string;
  paid: boolean;
  show: boolean;
}

const Installtments = React.memo(
  ({
    filteredInstallments,
    selectInstallments,
    simulationAction,
    selectedInstallments,
    DataAntecipacaoDesconto__c,
    paid,
    show
  }: InstalltmentsProps) => {
    if (!show || !filteredInstallments || filteredInstallments === null) return null;

    return (
      <motion.div
        initial='collapsed'
        animate={'open'}
        variants={AnimateFadeIn}
        exit='exit'
        transition={{ duration: 0.5, ease: [0, 0, 1, 1] }}>
        {paid ? TextAdimplente(DataAntecipacaoDesconto__c) : TextInadimplente()}

        <div className='mt-6 md:h-[40vh] md:overflow-auto'>
          <table className='table-auto w-full'>
            <thead>
              <tr className='border-b-1 border-white'>
                <th className='opacity-0'>-</th>
                <th className='pb-3 text-navy-blue text-sm font-normal tracking-wide text-center'>
                  Título
                </th>
                <th className='pb-3 text-navy-blue text-sm font-normal tracking-wide text-center'>
                  Parcela
                </th>
                <th className='pb-3 text-navy-blue text-sm font-normal tracking-wide text-center'>
                  Tipo/Valor
                </th>
                <th className='pb-3 text-navy-blue text-sm font-normal tracking-wide text-center'>
                  Vencimento
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredInstallments?.map((installment, index2) => (
                <CardInstallmentNegotiate
                  key={index2}
                  index={installment.parentIndex as number}
                  index2={index2}
                  installment={installment}
                  selectInstallments={selectInstallments}
                  selectedInstallments={selectedInstallments}
                />
              ))}
            </tbody>
          </table>
        </div>
        <div className='w-1/2 flex m-auto mb-10'>
          <Buttonblue
            background='navy-blue'
            text='Simular'
            color='white'
            onClick={simulationAction}
          />
        </div>
      </motion.div>
    );
  }
);
const TextAdimplente = (DataAntecipacaoDesconto__c: string) => {
  return (
    <TextCondicions>
      <TitleCondicions>Antecipe suas parcelas</TitleCondicions>
      <InnerTextCondicions>
        Simule as condições para antecipar suas parcelas com descontos imperdíveis
      </InnerTextCondicions>
      <InnerTextCondicions>
        Cliente Cury, disponibilizamos campanhas com descontos especiais para você realizar
        antecipação de suas parcelas.
      </InnerTextCondicions>
      <InnerTextCondicions>Você poderá realizar até 1 operação por dia.</InnerTextCondicions>
      <InnerTextCondicions>Aproveite as condições.</InnerTextCondicions>
      <hr className='border-b-1 border-solid border-white mb-4 mt-4'></hr>
      <InnerTextCondicions>
        Até a data de <strong>{DateUtils.formatDate(DataAntecipacaoDesconto__c)}</strong> apenas uma
        parcela deve ser selecionada por simulação. A partir desta data, selecione quantas parcelas
        desejar. .
      </InnerTextCondicions>
    </TextCondicions>
  );
};

const TextInadimplente = () => {
  return (
    <TextCondicions>
      <TitleCondicions>Renegociação Parcial de Parcelas Vencidas</TitleCondicions>
      <InnerTextCondicions>
        Selecione uma parcela em atraso para solicitar o boleto atualizado para pagamento.
      </InnerTextCondicions>
      <InnerTextCondicions>Você poderá realizar apenas 01 operação por dia.</InnerTextCondicions>
      <InnerTextCondicions>
        O restante do saldo devedor em atraso permanecerá inadimplente com a atualização diária dos
        juros e multa por atraso.
      </InnerTextCondicions>
      <InnerTextCondicions>
        Caso você não realize o pagamento do novo boleto, a Renegociação será excluída e a parcela
        voltará para a condição de atraso anterior a renegociação.
      </InnerTextCondicions>
    </TextCondicions>
  );
};

Installtments.displayName = 'Installtments';
export default Installtments;
