/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import Image from 'next/image';
import React from 'react';
import { useIr } from './hooks/useIr';

const IrScreen = React.memo(() => {
  const { showInforme, setShowInforme, handleIr, yearList } = useIr();

  if (!showInforme) return null;
  return (
    <motion.div
      initial='collapsed'
      animate={showInforme ? 'open' : 'collapsed'}
      variants={AnimateFadeIn}
      exit='exit'
      transition={{ duration: 0.3, ease: 'linear' }}
      className='bg-navy-blue bg-opacity-90 w-full h-full fixed inset-0 z-[1000000001] overflow-auto p-4 flex flex-col justify-start md:justify-center items-center'>
      <div className='bg-transparent rounded relative px-4 py-6 flex flex-col items-start'>
        <button
          className='bg-transparent absolute top-1 right-1'
          onClick={() => setShowInforme(false)}>
          <Image
            src='/images/close.png'
            alt='Icon'
            className='invert w-auto h-auto'
            width={30}
            height={30}
            priority
          />
        </button>
        <div className='w-[240px] h-[240px]'>
          <Image
            src='/images/informe-pagamentos.png'
            alt='Icon'
            className='w-full'
            width={240}
            height={240}
            priority
          />
        </div>
      </div>
      <p className='text-2xl text-white tracking-1 leading-4 text-center'>Informe de Pagamentos</p>
      <p className='text-base text-white tracking-1 leading-6 text-center mt-5 w-[350px]'>
        Clique nos botões abaixo para baixar ou receber o Informe de rendimento de 2024.
      </p>
      {/* <CustomDropdown
        ref={yearListDropdownRef}
        label={'Ano de referência*:'}
        defaultValue='Ano'
        optionsExternal={yearList}
        onChange={(value: string | number | boolean) => changeSetYear(value as string)}
        name={'year'}
      /> */}
      <div className='flex justify-center flex-row'>
        <div className='w-1/2 mr-1'>
          <Buttonblue
            text='Enviar por e-mail'
            background='white'
            color='navy-blue'
            onClick={() => {
              handleIr(true, '2024');
            }}
          />
        </div>
        <div className='w-1/2 ml-1'>
          <Buttonblue
            text='Fazer Download'
            background='white'
            color='navy-blue'
            onClick={() => {
              handleIr(false, '2024');
            }}
          />
        </div>
      </div>
      <div className='flex justify-center flex-row  w-[250px] mt-4'>
        <p className='text-white text-base text-center'>
          Caso precise de <b>Informes de Rendimentos</b> de outros anos, selecione abaixo.
        </p>
      </div>
      <div>
        <hr className='w-full border-white  mt-8' />
        <ul className='mt-6 w-full'>
          {yearList.map((year) => (
            <li key={year.id}>
              <ButtonsYears textyear={year.name} handleIr={handleIr} />
            </li>
          ))}
        </ul>
      </div>
    </motion.div>
  );
});

const ButtonsYears = ({
  handleIr,
  textyear
}: {
  handleIr: (sendEmail: boolean, year: string) => void;
  textyear: string;
}) => {
  return (
    <div className='flex justify-center align-middle items-center flex-col w-full mb-4'>
      <p className='text-white text-sm center  mb-[-24px]'>{textyear}</p>
      <div className='flex justify-center flex-row '>
        <Buttonblue
          text='Enviar por e-mail'
          background='white'
          color='navy-blue'
          onClick={() => {
            handleIr(true, textyear);
          }}
        />
        <div className='w-4'></div>
        <Buttonblue
          text='Fazer Download'
          background='white'
          color='navy-blue'
          onClick={() => {
            handleIr(false, textyear);
          }}
        />
      </div>
      <hr className='w-[90%] border-[#00000050]  mt-8' />
    </div>
  );
};
IrScreen.displayName = 'IrScreen';
export default IrScreen;
