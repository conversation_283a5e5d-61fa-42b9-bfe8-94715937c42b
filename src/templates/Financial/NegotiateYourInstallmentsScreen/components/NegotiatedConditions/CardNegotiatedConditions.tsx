/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { NegotiatedConditionsType } from '@/@types/installments';
import { useCallback, useEffect } from 'react';
import CardElementNegotiatedConditions from './ItemNegotiatedConditions';

interface CardNegotiatedConditionsProps {
  index: number;
  installment: NegotiatedConditionsType;
  selectCondiction: (index: number) => void | number | null;
  condictionSelect: number;
  total: number;
}

export type TitleKey =
  | 'Valor total selecionado'
  | 'Desconto'
  | 'Valor a pagar'
  | 'Prazo para pagamento'
  | 'total'
  | 'desconto'
  | 'totalAPagar'
  | 'entrada'
  | 'vencimento'
  | 'parcela'
  | 'valor parcela';

export type idKey =
  | 'QuitacaoSaldoDevedor'
  | 'QuitacaoSaldoVencido'
  | 'ParcelamentoSaldoDevedor'
  | 'ParcelamentoSaldoVencido'
  | 'NegociacaoParcialDisponivel'
  | 'NegociacaoParcialSimulacao';

/**
 * Componente para exibir um card de condição negociada
 */
export default function CardNegotiatedConditions({
  index,
  installment,
  selectCondiction,
  condictionSelect,
  total
}: Readonly<CardNegotiatedConditionsProps>) {
  const check = condictionSelect === index;

  // Define estilos do card com base no estado de seleção
  const classCard = `text-[0.8rem] bg-white cursor-pointer p-4 ${
    index !== total ? 'mb-10' : ''
  } ${check ? 'ring-offset-2 ring-4 ring-neutral-blue' : ''}`;

  // Determina o estilo do título baseado no tipo de card
  const classDivTitle =
    installment.id === 'NegociacaoParcialDisponivel'
      ? 'flex justify-center items-center align-items-center text-center h-[50px] '
      : 'flex flex-row justify-between items-center mb-4 text-sm';

  // Determina o título baseado no tipo de card
  const titleCard =
    installment.id === 'NegociacaoParcialDisponivel'
      ? `${installment.title}\nEscolha a(s) parcela(s) que deseja pagar`
      : installment.title;

  /**
   * Manipula o clique no card
   */
  const handleClick = useCallback(() => {
    const result = selectCondiction(index);
  }, [index, selectCondiction]);

  return (
    <button className={classCard} onClick={handleClick}>
      <div style={{ whiteSpace: 'pre-wrap' }} className={classDivTitle}>
        {titleCard}
      </div>
      {installment.data?.map((installmentItem) => (
        <CardElementNegotiatedConditions
          key={`${installment.id}_${installmentItem.title}_${index}`}
          id={installment.id as idKey}
          title={installmentItem.title as TitleKey}
          value={installmentItem.value}
        />
      ))}
    </button>
  );
}
