/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useCallback, useEffect } from 'react';

interface CardNegotiatedConditionsStaticProps {
  title: string;
  index: number;
  selectCondiction: (index: number) => void | number | null;
  condictionSelect: number;
}

/**
 * Componente para exibir um card estático de condição negociada
 * Usado principalmente para a opção "Quero receber contato para mais informações"
 */
export function CardNegotiatedConditionsStatic({
  title,
  index,
  selectCondiction,
  condictionSelect
}: Readonly<CardNegotiatedConditionsStaticProps>) {
  const check = condictionSelect === index;

  // Define estilos do card com base no estado de seleção
  const classCard = `bg-white cursor-pointer mt-10 p-4 ${
    check ? 'ring-offset-2 ring-4 ring-neutral-blue' : ''
  }`;

  /**
   * Manipula o clique no card
   */
  const handleClick = useCallback(() => {
    const result = selectCondiction(index);
  }, [index, selectCondiction]);

  return (
    <button className={classCard} onClick={handleClick}>
      <div className='flex justify-center items-center align-items-center h-[50px] text-[0.8rem] text-center'>
        <span className='text-navy-blue'>{title}</span>
      </div>
    </button>
  );
}
