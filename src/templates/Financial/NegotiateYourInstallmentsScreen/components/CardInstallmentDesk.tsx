/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { InstallmentDetails, InstallmentItem } from '@/@types/installments';
import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import RowsTable from '@/templates/components/Financial/TitleTable/RowsTable';
// import { RowsTable } from '@/templates/components/Financial/TitleTable/RowsTable';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"
import { memo, useMemo } from 'react';

interface CardInstallmentProps {
  index: number;
  installment: InstallmentItem;
  detail: InstallmentDetails;
}

// Memoize animation props globally since they only depend on duration and ease
const baseAnimationProps = {
  initial: 'collapsed',
  animate: 'open',
  variants: AnimateFadeIn,
  exit: 'exit'
};

const CardInstallmentDesk = memo(({ index, installment, detail }: CardInstallmentProps) => {
  // Only memoize the delay part of animation since it depends on index
  const transition = useMemo(
    () => ({
      duration: 0.7,
      ease: 'easeOut',
      delay: Math.min(index * 0.05, 2) // Cap maximum delay at 2 seconds
    }),
    [index]
  );

  const rowClassName = useMemo(() => {
    if (!detail) return '';
    return `border-b-1 h-[78px] border-white ${index % 2 === 0 ? 'bg-[#f6f6f6]' : ''} ${
      detail.statusBoleto === 'Vencido' ? ' !bg-[#ffe6e6] border-b-2' : ''
    } id-${installment.id}`;
  }, [index, detail, installment.id]);

  // Memoize the row data to prevent unnecessary re-renders
  const rows = useMemo(() => {
    if (!detail?.rowsData) return [];
    return detail.rowsData.map((data, rowIndex) => (
      <RowsTable
        key={`${data.value}-${rowIndex}-${installment.id}`}
        id={installment.id}
        parentIndex={installment.parentIndex as number}
        customClass={data.customClass}
        value={data.value as string | number}
      />
    ));
  }, [detail?.rowsData, installment.id, installment.parentIndex]);

  if (detail === null) return null;

  return (
    <motion.tr
      {...baseAnimationProps}
      transition={transition}
      key={`installment-${installment.id}`}
      className={rowClassName}>
      {rows}
    </motion.tr>
  );
});

CardInstallmentDesk.displayName = 'CardInstallmentDesk';

export default CardInstallmentDesk;
