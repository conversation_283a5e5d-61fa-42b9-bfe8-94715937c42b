/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';

import { InstallmentItem } from '@/@types/installments';
import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { useAppContext } from '@/contexts/AppContext';
import CustomLink from '@/templates/components/Commons/buttons/CustomLink';
import { Status } from '@/templates/Financial/Status';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';
//import { motion } from 'framer-motion';
import { motion } from "motion/react"

interface CardInstallmentCopyBillProps {
  index: number;
  index2: number;
  installment: InstallmentItem;
}

const CardInstallmentCopyBill = ({ index, index2, installment }: CardInstallmentCopyBillProps) => {
  // const dateBoleto: number | null = installment.receipts[0].days;
  const { user } = useAppContext();
  //const today = new Date().toISOString().split('T')[0];
  const today = DateUtils.getTodayDate().todayDate;
  const duodatesituation = DateUtils.compareDates(
    today,
    (installment.isoDueDate ?? installment.dueDate) as string
  );
  const statusBoletoIndex: string | null = duodatesituation
    ? 'Vencido'
    : installment.receipts[0].type;
  const key = statusBoletoIndex === null ? 'null' : statusBoletoIndex;
  const statusBoleto = Status[key];

  return (
    <motion.tr
      initial='collapsed'
      animate={'open'}
      variants={AnimateFadeIn}
      exit='exit'
      transition={{
        duration: 0.7,
        ease: [0, 0, 0.2, 1],
        delay: index * 0.1
      }}
      key={`installment-copy-${index2}`}
      className={`border-b-1 border-white ${statusBoleto === 'Vencido' ? 'bg-white' : ''}`}>
      <td className='py-3 pl-5 text-navy-blue text-sm tracking-wide w-[20%]'>
        {installment.id < 10 ? `0${installment.id}` : installment.id}
      </td>
      <td className='py-3 pl-1 text-navy-blue text-sm tracking-wide text-left w-[35%]'>
        <CustomLink
          href={`/${user?.AccountId}/financeiro/2via-de-boleto/${installment.id}/${index}`}
          className='w-full'>
          <small className='tracking-wide text-navy-blue font-medium flex text-xs'>
            {installment.paymentTerms.descrition}
          </small>
          <p className='font-bold'>
            {FinanceUtils.MoneyFormat.format(
              installment.currentBalanceWithAddition
                ? installment.currentBalanceWithAddition
                : installment.originalValue
            )}
          </p>
        </CustomLink>
      </td>
      <td className='py-3 text-navy-blue text-sm tracking-wide text-left w-[40%]'>
        <CustomLink
          href={`/${user?.AccountId}/financeiro/2via-de-boleto/${installment.id}/${index}`}
          className='w-full'>
          <p className='font-bold'>
            {DateUtils.formatDate((installment.isoDueDate ?? installment.dueDate) as string)}
          </p>
        </CustomLink>
      </td>
      <td className='w-[5%]'>
        <Image
          src='/images/angle-down.svg'
          alt='Icon'
          className='-rotate-90 object-contain w-auto h-auto'
          width={24}
          height={16}
          priority
        />
      </td>
    </motion.tr>
  );
};

export default CardInstallmentCopyBill;
