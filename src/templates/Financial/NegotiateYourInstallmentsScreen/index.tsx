/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { lazy, Suspense, useEffect, useLayoutEffect } from 'react';

import { NegotiatedConditionsType } from '@/@types/installments';
import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useInstallmentSelection } from '@/hooks/Negotiation/useInstallmentSelection';
import { useNegotiateYourInstallments } from '@/hooks/Negotiation/useNegotiateYourInstallments';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import TitlePage from '@/templates/components/Commons/TitlePage';
import { Show } from '@/utils/components/Show';

// Componentes carregados sob demanda
const Conditions = lazy(() => import('./components/NegotiatedConditions/Conditions'));
const Installtments = lazy(() => import('./components/NegotiatedConditions/Installtments'));
const ReturnNegotiate = lazy(() => import('./components/ReturnNegotiate'));
const CancelNegotiate = lazy(() => import('./components/CancelNegotiate'));

/**
 * Componente principal da tela de negociação de parcelas
 */
function NegotiateYourInstallmentsScreen() {
  // Hooks de media query e renderização
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();
  const { setLoadingInfo } = useLoading();

  // Hook principal de negociação
  const negotiateState = useNegotiateYourInstallments();

  const {
    // Estados da UI
    returnTitle,
    returnText,
    showParcelas,
    showCondicoes,
    showReturn,
    showCancelamento,

    // Estados de dados
    paid,
    negotiatedConditions,
    filteredInstallments,
    selectedInstallments: hookSelectedInstallments,
    DataAntecipacaoDesconto__c,

    // Ações
    setShowCancelamento,
    selectCondictionBase,
    confirmAction,
    simulationAction,
    cancelAction,
    cancelMotivationSelect,
    selectInstallments,
    sendCancel
  } = negotiateState;

  // Hook de seleção de parcelas com estado local
  const {
    handleSelectInstallment,
    selectedInstallments: localSelectedInstallments,
    updateFilteredInstallments
  } = useInstallmentSelection({
    paid,
    selectInstallments
  });

  // Atualiza as parcelas filtradas quando mudam no hook principal
  useEffect(() => {
    if (filteredInstallments) {
      updateFilteredInstallments(filteredInstallments);
    }
  }, [filteredInstallments, updateFilteredInstallments]);

  // Configuração do layout e sidebar
  const { setSubtitleHeaderMobile, setShowSidebar, currentSubtitle } = useAppContext();

  useLayoutEffect(() => {
    if (currentSubtitle !== 'Negocie suas parcelas') {
      setSubtitleHeaderMobile('Negocie suas parcelas');
    }
    setShowSidebar(!isMobile);
  }, [isMobile, setSubtitleHeaderMobile, setShowSidebar, currentSubtitle]);

  // Desativar indicador de carregamento ao montar
  useEffect(() => {
    setLoadingInfo(false);
  }, [setLoadingInfo]);

  // Condições para renderização
  const conditions = [
    isMobile === null,
    showParcelas === null,
    showCondicoes === null,
    showReturn === null,
    showCancelamento === null,
    paid === null,
    DataAntecipacaoDesconto__c === null
  ];

  // Não renderizar se alguma condição não for atendida
  if (!canBeRender({ conditions })) return null;

  return (
    <Suspense fallback={<p>Carregando as condições...</p>}>
      {/* Título da página (apenas para desktop) */}
      <Show when={!isMobile}>
        <TitlePage text='Financeiro' />
      </Show>

      {/* Componente de parcelas */}
      <Show when={showParcelas}>
        <Installtments
          filteredInstallments={filteredInstallments}
          selectInstallments={handleSelectInstallment}
          simulationAction={simulationAction}
          selectedInstallments={hookSelectedInstallments}
          DataAntecipacaoDesconto__c={DataAntecipacaoDesconto__c}
          paid={paid}
          show={showParcelas}
        />
      </Show>

      {/* Componente de condições negociadas */}
      <Show when={showCondicoes}>
        <Conditions
          negotiatedConditions={negotiatedConditions as NegotiatedConditionsType[]}
          paid={paid}
          selectCondictionBase={selectCondictionBase}
          confirmAction={confirmAction}
          cancelAction={cancelAction}
          show={showCondicoes}
        />
      </Show>

      {/* Componente de retorno após negociação */}
      <Show when={showReturn}>
        <ReturnNegotiate title={returnTitle} text={returnText} show={showReturn} />
      </Show>

      {/* Componente de cancelamento */}
      <Show when={showCancelamento}>
        <CancelNegotiate
          title={'Motivo do cancelamento'}
          text={'Por favor, nos diga qual o motivo do cancelamento:'}
          show={showCancelamento}
          cancelMotivationSelect={cancelMotivationSelect}
          close={() => {
            setShowCancelamento(false);
          }}
          sendCancel={sendCancel}
        />
      </Show>
    </Suspense>
  );
}

export default NegotiateYourInstallmentsScreen;
