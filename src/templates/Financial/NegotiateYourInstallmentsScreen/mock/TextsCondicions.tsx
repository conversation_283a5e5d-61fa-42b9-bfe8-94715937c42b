/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import React from 'react';

type ChildrenProps = {
  children: React.ReactNode;
};

export const TitleCondicions: React.FC<ChildrenProps> = () => null;
export const InnerTextCondicions: React.FC<ChildrenProps> = () => null;

export const TextCondicions: React.FC<ChildrenProps> = ({ children }) => {
  let titleContent: React.ReactNode = null;
  const textContents: React.ReactNode[] = [];

  React.Children.forEach(children, (child) => {
    if (React.isValidElement(child)) {
      if (child.type === TitleCondicions) {
        titleContent = (child as React.ReactElement<ChildrenProps>).props.children;
      } else if (child.type === InnerTextCondicions) {
        textContents.push((child as React.ReactElement<ChildrenProps>).props.children);
      }
    }
  });

  return (
    <>
      {titleContent && (
        <h4 className='text-navy-blue font-medium text-xl tracking-1 mb-2'>{titleContent}</h4>
      )}
      {textContents.map((text, index) => (
        <p key={`textContents_${index}`} className='text-sm text-navy-blue tracking-1 mb-2'>
          {text}
        </p>
      ))}
    </>
  );
};
