/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';

import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useLoading } from '@/hooks/useLoading';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import ButtonImage from '@/templates/components/Commons/buttons/ButtonImage';
import { useGetLinkRegulamento } from '@/templates/Financial/CuryChegaMaisScreen/hook/useGetLinkRegulamento';
import { useJoinChegaMais } from '@/templates/Financial/CuryChegaMaisScreen/hook/useJoinChegaMais';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';

export default function CuryChegaMaisScreen() {
  const { setLoadingInfo } = useLoading();
  const { isMobile, content } = useAppContext();
  const { canBeRender } = useCanBeRender();

  const [showAceiteError, setShowAceiteError] = useState(false);
  const [aceiteReg, setAceiteReg] = useState(false);

  const aceitaRegRef = useRef(false);

  let textChegaMais = '';
  switch (content?.Contract?.ContratoComProgramaFidelidade__c) {
    case 'Validado':
      textChegaMais = 'Seu cadastro foi validado e você já faz parte do programa Cury Chega Mais.';
      break;
    case 'Em validação':
      textChegaMais =
        'O seu cadastro no programa Cury Chega Mais está em processo de análise, aguarde.';
      break;
  }
  const { link, getLinkRegulamento } = useGetLinkRegulamento();
  const { data, joinChegaMais } = useJoinChegaMais();

  useEffect(() => {
    getLinkRegulamento();
    setLoadingInfo(false);
  }, []);

  useEffect(() => {
    if (data && data.success) {
      switch (data.content?.Contract?.ContratoComProgramaFidelidade__c) {
        case 'Validado':
          textChegaMais =
            'Seu cadastro foi validado e você já faz parte do programa Cury Chega Mais.';
          break;
        case 'Em validação':
          textChegaMais =
            'O seu cadastro no programa Cury Chega Mais está em processo de análise, aguarde.';
          break;
      }
    }
  }, [data]);

  const join = async () => {
    const contractId = content?.Contract?.ContractId ?? content?.ContractId;

    if (!aceitaRegRef.current || !contractId) {
      setShowAceiteError(true);
      return false;
    }
    setLoadingInfo(true, 'Processando o cadastro do Chega Mais. Aguarde.');
    await joinChegaMais({ ContractId: contractId });
    setLoadingInfo(false);
    return true;
  };
  function changeHandle(): void {
    setShowAceiteError(false);
    setAceiteReg(!aceiteReg);
    aceitaRegRef.current = !aceitaRegRef.current;
  }

  function openUrlBrowser() {
    const postData = {
      type: 'openUrlBrowser',
      url: link ?? '',
      data: {}
    };

    BrowserUtils.ActionReactNative(postData);
  }

  const { setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Cury Chega Mais');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const conditions = [isMobile === null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <div className='mb-10'>
        <Image
          src={isMobile ? '/images/foto-chega-mais-mobile2.webp?t=' + new Date().getTime() : '/images/foto-chega-mais-desk2.webp?t=' + new Date().getTime()}
          alt='Foto'
          className='w-full'
          width={isMobile ? 300 : 1000}
          height={isMobile ? 168 : 560}
          quality={75}
          priority
        />

        <p className='text-sm text-navy-blue tracking-1 mt-4 mb-4 text-center px-2 md:px-20'>
          São muitos benefícios para você! Tudo sem mistério: pagando 6 meses seguidos em dia, a
          Cury desconta a última parcela. Assim, você quita seu imóvel mais rápido e fica mais perto
          do seu novo lar!
        </p>
        <p className='font-bold text-xl max-md:text-base text-navy-blue tracking-1 mt-10 max-md:mt-4 text-center'>
          Como funciona?
        </p>
        <hr className='border-t-1 border-white mt-4 mb-4' />
        <div className='flex flex-row items-center justify-center max-md:justify-normal'>
          <Image
            src='/images/comprou.png'
            alt='Icon'
            className=''
            width={30}
            height={30}
            priority
          />
          <p className='text-sm text-navy-blue tracking-1 ml-3'>Comprou</p>
        </div>
        <hr className='border-t-1 border-white mt-4 mb-4' />
        <div className='flex flex-row items-center justify-center max-md:justify-normal'>
          <Image src='/images/pagou.png' alt='Icon' className='' width={30} height={30} priority />
          <p className='text-sm text-navy-blue tracking-1 ml-3'>Pagou em dia</p>
        </div>
        <hr className='border-t-1 border-white mt-4 mb-4' />
        <div className='flex flex-row items-center justify-center max-md:justify-normal'>
          <Image src='/images/ganhou.png' alt='Icon' className='' width={30} height={30} priority />
          <p className='text-sm text-navy-blue tracking-1 ml-3'>Ganhou parcela mensal</p>
        </div>
        <hr className='border-t-1 border-white mt-4 mb-4' />
        <div className='flex flex-row items-center justify-center max-md:justify-normal'>
          <Image src='/images/quitou.png' alt='Icon' className='' width={30} height={30} priority />
          <p className='text-sm text-navy-blue tracking-1 ml-3'>Quitou mais rápido</p>
        </div>
        <hr className='border-t-1 border-white mt-4 mb-4' />
        <p className='text-sm text-navy-blue tracking-1 mb-4 text-center px-4 md:px-20 md:w-2/3 m-auto'>
          E não é só isso! Além das parcelas mensais, você também pode ter descontos para
          antecipação de parcelas, prioridade na vistoria do imóvel e na entrega das chaves!
        </p>
        <p className='font-bold text-xl max-md:text-base text-navy-blue tracking-1 mt-10 mb-5 max-md:mt-4 text-center px-2 md:px-20'>
          Como aderir ao programa?
        </p>
        {DeviceUtils.isMobileApp() ? (
          <ButtonImage
            text='Leia o regulamento'
            src='/images/contrato.png'
            onClick={openUrlBrowser}
          />
        ) : (
          <Link
            className='w-2/5 flex m-auto max-md:w-full'
            href={`${link}`}
            target='_blank'
            rel='noreferrer'>
            <ButtonImage text='Leia o regulamento' src='/images/contrato.png' route={''} />
          </Link>
        )}
        {textChegaMais !== '' ? (
          <div className='flex flex-row items-center mt-5 justify-center'>
            <p className='text-sm text-white tracking-1 mb-4 border-light-blue border-1 p-4 text-center bg-navy-blue'>
              {textChegaMais}
            </p>
          </div>
        ) : (
          <>
            <div className='flex flex-row items-center max-md:mt-5 justify-center'>
              <input
                type='checkbox'
                value=''
                className='mt-1 h-7 w-7 rounded accent-light-blue border-1 border-light-blue'
                onChange={changeHandle}
              />
              <p className='text-sm text-navy-blue tracking-1 ml-3'>
                Declaro que li e concordo com os termos do regulamento
              </p>
            </div>
            {showAceiteError ? (
              <div className='flex flex-row items-center max-md:mt-5 justify-center mt-4'>
                <p className='text-red-500 tracking-1 ml-3 border-1 border-red-500 border-solid p-3 text-center '>
                  Você precisa aceitar os <strong>termos do regulamento</strong> antes de
                  participar.
                </p>
              </div>
            ) : null}
            <div className='w-1/3 max-md:w-1/2 flex justify-center items-center m-auto'>
              <Buttonblue
                background='navy-blue'
                text='Quero participar'
                color='white'
                onClick={join}
              />
            </div>
          </>
        )}
      </div>
    </>
  );
}
