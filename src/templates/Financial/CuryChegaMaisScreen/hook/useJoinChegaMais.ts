/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useState } from 'react';

import { Content } from '@/@types/content';
import { useModal } from '@/hooks';
import { useApi } from '@/hooks/useApi';
import { apiPost } from '@/server/services/api';
import { DateUtils } from '@/utils/DateUtils';

interface JoinChegaMaisProps {
  ContractId: string;
}

export const useJoinChegaMais = () => {
  const { toggleModal } = useModal();
  const [data, setData] = useState<{
    success: boolean;
    content: Content;
  } | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const { getFetch } = useApi();
  const joinChegaMais = async ({ ContractId }: JoinChegaMaisProps) => {
    try {
      const { todayDate } = DateUtils.getTodayDate();
      if (!ContractId) return false;

      const response = await apiPost('chegamais/join', {
        ContractId,
        DataAdesaoProgramaFidelidade__c: todayDate
      });
      // const response = await getFetch<{
      //   data: { success: boolean; content: Content };
      // }>({
      //   method: 'POST',
      //route: 'chegamais/join',
      //body: {
      //   ContractId,
      //   DataAdesaoProgramaFidelidade__c: todayDate
      //   }
      //  });
      if (response instanceof Error) {
        toggleModal({
          text: 'Ops... Ocorreu um erro. Tente novamente mais tarde por favor.'
        });
        setError(response);
      } else {
        toggleModal({
          text: 'Seu cadastro foi enviado para validação.'
        });
        if (response) setData(response.data);
      }
      return response;
    } catch (e) {
      setError(e as Error);
      return e;
    }
  };

  return { data, error, joinChegaMais };
};
