/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useInforme } from '@/contexts/useInforme';

import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { usePermissions } from '@/hooks/usePermissions';
import ButtonImage from '@/templates/components/Commons/buttons/ButtonImage';
import { useEffect, useLayoutEffect } from 'react';

export default function FinancialScreen() {
  const { user, content, setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();
  const { getPermission } = usePermissions(content);
  const { setShowInforme } = useInforme();
  const { canBeRender } = useCanBeRender();
  const canViewSegundaVia = getPermission('2a via boleto');
  const canViewRelatorioDeExtrato = true; //getPermission('Relatório de Extrato');
  const canViewNegocie = true; // getPermission('Negocie suas parcelas');
  const canViewChegaMais = getPermission('Chega Mais');
  const canViewInforme = true; //getPermission('Informe de pagamentos');

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Financeiro');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [isMobile === null, user === null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <div className='w-9/12 max-md:w-full'>
        <div className='max-md:block'>
          <ButtonImage
            text='2º via de Boleto'
            src='/images/2via-boleto.png'
            route={`/${user?.AccountId}/financeiro/2via-de-boleto`}
            permission={canViewSegundaVia}
          />
          <ButtonImage
            text='Relatório de Extrato'
            src='/images/relatorio-de-extrato.png'
            route={`/${user?.AccountId}/financeiro/relatorio-de-extrato`}
            permission={canViewRelatorioDeExtrato}
          />
          <ButtonImage
            text='Negocie suas parcelas'
            src='/images/negocie-suas-parcelas.png'
            route={`/${user?.AccountId}/financeiro/negocie-suas-parcelas`}
            permission={canViewNegocie}
          />
          <ButtonImage
            text='Cury Chega Mais'
            src='/images/cury-chega-mais.png'
            route={`/${user?.AccountId}/financeiro/cury-chega-mais`}
            permission={canViewChegaMais}
          />
          <ButtonImage
            text='Informe de Pagamentos'
            src='/images/informe-de-pagamentos.png'
            onClick={() => setShowInforme(true)}
            permission={canViewInforme}
          />
        </div>
      </div>
    </>
  );
}
