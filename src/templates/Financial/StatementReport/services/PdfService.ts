/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Financials } from '@/@types/financial';
import { InstallmentItem } from '@/@types/installments';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DateUtils } from '@/utils/DateUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { StringUtils } from '@/utils/StringUtils';
import { jsPDF } from 'jspdf';
import autoTable, { UserOptions } from 'jspdf-autotable';

import * as pdfUtils from '../utils/pdfUtils';
import { apiPost } from '@/server/services/api';

interface PdfGenerationParams {
  user: {
    Name?: string;
    CPF__c?: string;
    TelefoneCelular__c?: string;
    Email__c?: string;
    CodigoSienge__c?: string;
  } | null;
  content: {
    Empreendimento?: {
      name?: string;
    };
    UnidadeAtivoName?: string;
    Contract?: {
      DataCompra__c?: string;
    };
  } | null;
  financials: Financials | null;
  allInstallments: InstallmentItem[];
  installmentDetailFn: (id: number, parentIndex: number) => any;
  getFetch: (options: any) => Promise<any>;
}

export class PdfService {
  /**
   * Adiciona informações do cliente ao PDF
   */
  private static addClientInfoToDocument(
    doc: jsPDF,
    user: PdfGenerationParams['user'],
    content: PdfGenerationParams['content']
  ): void {
    const pageWidth = doc.internal.pageSize.width;
    const margins = 20;
    const availableWidth = pageWidth - margins;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');

    autoTable(doc, {
      startY: 60,
      head: [['CLIENTE', '', 'UNIDADE', '']],
      body: [
        ['NOME', user?.Name ?? '', 'EMPREENDIMENTO', content?.Empreendimento?.name ?? ''],
        ['CPF', user?.CPF__c ?? '', 'UNIDADE', content?.UnidadeAtivoName ?? ''],
        [
          'TELEFONE',
          user?.TelefoneCelular__c ?? '',
          'DATA DA COMPRA',
          DateUtils.formatDate(content?.Contract?.DataCompra__c ?? '')
        ],
        ['EMAIL', user?.Email__c ?? '', '', '']
      ],
      theme: 'grid',
      headStyles: pdfUtils.getHeaderStyle(),
      styles: pdfUtils.getTableStyle(),
      columnStyles: {
        0: { cellWidth: availableWidth * 0.15 },
        1: { cellWidth: availableWidth * 0.35 },
        2: { cellWidth: availableWidth * 0.15 },
        3: { cellWidth: availableWidth * 0.35 }
      },
      margin: { left: margins / 2, right: margins / 2 },
      tableWidth: 'auto'
    });
  }

  /**
   * Adiciona tabela de resumo ao PDF
   */
  private static addSummaryTableToDocument(doc: jsPDF, financials: Financials | null): void {
    const pageWidth = doc.internal.pageSize.width;
    const margins = 20;
    const availableWidth = pageWidth - margins;

    autoTable(doc, {
      startY: 105,
      head: [['RESUMO', 'PARCELAS', 'VALOR']],
      body: [
        ['VALOR PAGO', financials?.totalInstallmentsPaid ?? '', financials?.totalPaid ?? ''],
        [
          'VALOR A PAGAR',
          financials?.totalInstallmentsPayable ?? '',
          financials?.totalPayable ?? ''
        ],
        [
          'VALOR VENCIDO',
          financials?.totalOutstanding ?? '',
          financials?.totalOutstandingInstallments ?? ''
        ],
        ['VALOR TOTAL', '-', financials?.total ?? '']
      ],
      theme: 'grid',
      headStyles: pdfUtils.getHeaderStyle(),
      styles: pdfUtils.getTableStyle(),
      columnStyles: {
        0: { cellWidth: availableWidth * 0.4 },
        1: { cellWidth: availableWidth * 0.3 },
        2: { cellWidth: availableWidth * 0.3 }
      },
      margin: { left: margins / 2, right: margins / 2 },
      tableWidth: 'auto'
    });
  }

  /**
   * Adiciona tabela de detalhes ao PDF
   */
  private static addDetailsTableToDocument(
    doc: jsPDF,
    allInstallments: InstallmentItem[],
    installmentDetailFn: (id: number, parentIndex: number) => any
  ): void {
    const margins = 20;
    const tableBody = pdfUtils.prepareDetailsTableBody(allInstallments, installmentDetailFn);

    autoTable(doc, {
      startY: 140,
      head: [
        [
          'PARCELAS',
          'TÍTULO',
          'TIPO DE CONTRATO',
          'TIPO DE CONDIÇÃO',
          'VENCIMENTO',
          'DATA DA BAIXA',
          'VALOR ORIGINAL',
          'ÍNDICE',
          'VALOR CORREÇÃO',
          'MULTA+JUROS',
          'VALOR ATUALIZADO',
          'VALOR A PAGAR',
          'VALOR PAGO',
          'STATUS'
        ]
      ],
      body: tableBody,
      theme: 'striped',
      headStyles: {
        ...pdfUtils.getHeaderStyle(),
        cellWidth: 18,
        minCellHeight: 8,
        halign: 'center',
        valign: 'middle'
      },
      styles: { ...pdfUtils.getTableStyle(8), cellPadding: 1 },
      columnStyles: pdfUtils.getDetailsColumnStyles() as UserOptions['columnStyles'],
      margin: { left: margins / 2, right: margins / 2 },
      tableWidth: 'auto'
    });
  }

  /**
   * Gera o documento PDF completo
   */
  public static async generatePdfDocument({
    user,
    content,
    financials,
    allInstallments,
    installmentDetailFn
  }: PdfGenerationParams): Promise<{ doc: jsPDF; docname: string }> {
    const doc = pdfUtils.createPdfDocument();

    await pdfUtils.addLogoToDocument(doc);
    pdfUtils.addTitleToDocument(doc);
    this.addClientInfoToDocument(doc, user, content);
    this.addSummaryTableToDocument(doc, financials);
    this.addDetailsTableToDocument(doc, allInstallments, installmentDetailFn);

    const { todayDate } = DateUtils.getTodayDate();
    const docname = `Extrato-${StringUtils.normalizeText(user?.Name ?? '')}-${todayDate}.pdf`;

    return { doc, docname };
  }

  /**
   * Faz o download do documento PDF
   */
  public static async downloadPdf(doc: jsPDF, docname: string): Promise<boolean> {
    const pdfBase64 = await doc.output('datauristring');

    if (DeviceUtils.isMobileApp() && window.ReactNativeWebView) {
      BrowserUtils.ActionReactNative({
        type: 'downloadPDF',
        data: {
          base64: pdfBase64,
          filename: docname
        }
      });
    } else {
      doc.save(docname);
    }
    return true;
  }

  /**
   * Envia o documento PDF por email
   */
  public static async emailPdf(
    doc: jsPDF,
    docname: string,
    user: PdfGenerationParams['user']
  ): Promise<boolean> {
    try {
      const pdfBinary = doc.output('arraybuffer');
      const pdfBlob = new Blob([pdfBinary], { type: 'application/pdf' });
      const pdfFile = new File([pdfBlob], docname, { type: 'application/pdf' });
      const response = await apiPost('sendExtractToEmail', {
        name: user?.Name,
        email: user?.Email__c,
        docname,
        file: pdfFile
      });
      if (response.status == 500 || response.status == 422) {
        return false;
      }
      return true;
    } catch (error) {
      console.error('Erro ao enviar email:', error);
      return false;
    }
  }
}
