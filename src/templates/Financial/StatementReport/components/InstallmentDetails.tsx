/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useAppContext } from '@/contexts/AppContext';
import { Show } from '@/utils/components/Show';
import InstallmentTableDesk from './InstallmentTableDesk';
import InstallmentTableMobile from './InstallmentTableMobile';

export default function InstallmentDetails() {
  const { isMobile } = useAppContext();
  if (isMobile === null) return null;
  return (
    <div className='mt-10 mb-10 md:overflow-auto md:p-4 md:bg-white  pr-2 md:h-full md:max-h-[50vh]'>
      <Show when={isMobile} fallback={<InstallmentTableDesk />}>
        <InstallmentTableMobile />
      </Show>
    </div>
  );
}
