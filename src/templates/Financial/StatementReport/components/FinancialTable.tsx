/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { AnimatePresence, motion } from "motion/react"

import SpinnerLoading from '@/templates/components/Commons/Loading/SpinnerLoading';
import { useFinancialData } from '../hooks/useFinancialData';

export default function FinancialTable() {
  const { financials } = useFinancialData();

  return (
    <>
      {financials === null ? (
        <div className='flex justify-center items-center mt-4 h-[172px]'>
          <SpinnerLoading />
        </div>
      ) : (
        <>
          <AnimatePresence>
            <motion.div
              initial='collapsed'
              animate={'open'}
              variants={AnimateFadeIn}
              exit='exit'
              className='bg-white rounded-md pt-2 pl-2'
              transition={{ duration: 1, ease: [0, 0, 0.2, 1] }}>
              <table id='infoResumo' className='table-auto w-full'>
                <thead>
                  <tr>
                    <th className='opacity-0'></th>
                    <th className='pb-1.5 text-light-blue max-md:text-xs font-normal tracking-wide'>
                      Parcelas
                    </th>
                    <th className='pb-1.5 text-light-blue max-md:text-xs font-normal tracking-wide'>
                      Valor
                    </th>
                  </tr>
                </thead>

                <tbody>
                  <tr className='border-t-1 border-light-gray'>
                    <td className='py-3 pl-3 text-navy-blue max-md:text-xs tracking-wide'>
                      Valor pago
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center'>
                      {financials.totalInstallmentsPaid}
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center'>
                      {financials.totalPaid}
                    </td>
                  </tr>
                  <tr className='border-t-1 border-light-gray'>
                    <td className='py-3 pl-3 text-navy-blue max-md:text-xs tracking-wide'>
                      Valor a vencer
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center'>
                      {financials.totalInstallmentsPayable}
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center'>
                      {financials.totalPayable}
                    </td>
                  </tr>
                  <tr className='border-t-1 border-light-gray'>
                    <td className='py-3 pl-3 text-navy-blue max-md:text-xs tracking-wide'>
                      Valor vencido
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center'>
                      {financials.totalOutstanding}
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center'>
                      {financials.totalOutstandingInstallments}
                    </td>
                  </tr>
                  <tr className='border-t-1 border-light-gray'>
                    <td className='py-3 pl-3 text-navy-blue max-md:text-xs tracking-wide font-bold'>
                      Valor total
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center font-bold'>
                      -
                    </td>
                    <td className='text-navy-blue max-md:text-xs tracking-wide text-center font-bold'>
                      {financials.total}
                    </td>
                  </tr>
                </tbody>
              </table>
            </motion.div>
          </AnimatePresence>
        </>
      )}
    </>
  );
}
