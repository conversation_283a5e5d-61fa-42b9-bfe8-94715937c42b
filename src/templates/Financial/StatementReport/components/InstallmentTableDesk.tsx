/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import SpinnerLoading from '@/templates/components/Commons/Loading/SpinnerLoading';
import { TitleTable } from '@/templates/components/Financial/TitleTable';
import { memo, useMemo } from 'react';
import CardInstallmentDesk from '../../NegotiateYourInstallmentsScreen/components/CardInstallmentDesk';
import { useFinancialData } from '../hooks/useFinancialData';

const cols = [
  'Parcelas',
  'T<PERSON><PERSON><PERSON>',
  'Tipo de contrato',
  'Tipo de condição',
  'Vencimento',
  'Data da baixa',
  'Valor original',
  'Índice',
  'Valor correção',
  'Multa+juros',
  'Valor atualizado',
  'Valor a pagar',
  'Valor pago',
  'Status'
];

// Memoize the header to prevent unnecessary re-renders
const TableHeader = memo(() => (
  <tr>
    {cols.map((col, i) => (
      <TitleTable key={`${col}${i}`} value={col} />
    ))}
  </tr>
));

TableHeader.displayName = 'TableHeader';

const InstallmentTableDesk = () => {
  const { allInstallments, installmentDetail } = useFinancialData();

  // Memoize the loading state check
  const isLoading = useMemo(() => {
    return (
      allInstallments === null || allInstallments.length === 0 || allInstallments === undefined
    );
  }, [allInstallments]);

  // Pre-calculate all installment details to avoid recalculation on each render
  const installmentDetails = useMemo(() => {
    if (!allInstallments) return [];
    return allInstallments.map((installment) => ({
      installment,
      detail: installmentDetail(installment.id, installment.parentIndex as number)
    }));
  }, [allInstallments, installmentDetail]);

  if (allInstallments === null) return null;

  if (isLoading) {
    return (
      <div className='flex justify-center mt-4'>
        <SpinnerLoading />
      </div>
    );
  }

  return (
    <table id='infoFinanceiras' className='table-auto w-full h-full bg-white p-4'>
      <thead>
        <TableHeader />
      </thead>
      <tbody>
        {installmentDetails.map(({ installment, detail }, index) => (
          <CardInstallmentDesk
            key={`CardInstallmentDesk-${index}-${installment.id}`}
            index={index}
            installment={installment}
            detail={detail}
          />
        ))}
      </tbody>
    </table>
  );
};

export default memo(InstallmentTableDesk);
