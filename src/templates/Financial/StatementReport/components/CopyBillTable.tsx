/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';

import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import SpinnerLoading from '@/templates/components/Commons/Loading/SpinnerLoading';
import CardInstallmentCopyBill from '@/templates/Financial/NegotiateYourInstallmentsScreen/components/CardInstallmentCopyBill';
// import { AnimatePresence, motion } from "motion/react"
import { AnimatePresence, motion } from "motion/react"
import { useFinancialData } from '../hooks/useFinancialData';

export default function CopyBillTable() {
  const { copybillData } = useFinancialData();

  return (
    <>
      {copybillData === null ? (
        <div className='flex justify-center mt-4 flex-col items-center'>
          <SpinnerLoading />
          <p className='text-base text-navy-blue tracking-1 mt-2'>
            Verificando se existem boletos.
          </p>
        </div>
      ) : (
        <>
          <AnimatePresence>
            <motion.div
              initial='collapsed'
              animate={'open'}
              className='mb-10'
              variants={AnimateFadeIn}
              exit='exit'
              transition={{ duration: 0.3, ease: [0, 0, 1, 1] }}>
              {copybillData.length === 0 ? (
                <div className='flex justify-center flex-col items-center mt-8'>
                  <Image
                    src='/images/vazio.png'
                    alt='Icon'
                    className=''
                    width={50}
                    height={50}
                    priority
                  />
                  <p className='text-base text-navy-blue tracking-1 mt-2'>
                    Nenhum boleto disponível
                  </p>
                </div>
              ) : (
                <div className='mt-6'>
                  <table className='table-auto w-full'>
                    <thead>
                      <tr>
                        <th className='pb-1.5 text-navy-blue text-sm font-normal tracking-wide text-left'>
                          Parcela
                        </th>
                        <th className='pb-1.5 text-navy-blue text-sm font-normal tracking-wide text-left'>
                          Tipo/Valor
                        </th>
                        <th className='pb-1.5 text-navy-blue text-sm font-normal tracking-wide text-left'>
                          Vencimento
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {copybillData &&
                        copybillData.map((installment, index) => (
                          <CardInstallmentCopyBill
                            key={index}
                            index={installment.parentIndex as number}
                            index2={index}
                            installment={installment}
                          />
                        ))}
                    </tbody>
                  </table>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </>
      )}
    </>
  );
}
