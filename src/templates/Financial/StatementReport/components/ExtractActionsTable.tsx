/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { AnimateFadeIn, AnimateFadeInOpenHeight } from '@/constants/ANIMATIONS';
import { AnimatePresence, motion } from "motion/react"

import ButtonActionExtract from '@/templates/components/Commons/buttons/ButtonActionExtract';
import Image from 'next/image';
import { useState } from 'react';
import { useFinancialData } from '../hooks/useFinancialData';

export default function ExtractActionsTable() {
  const { action } = useFinancialData();
  const [show, setShow] = useState(true);

  return (
    <>
      <motion.div
        initial='collapsed'
        animate={'openWithBigDelay'}
        className='w-full'
        variants={AnimateFadeIn}
        exit='exit'
        transition={{ duration: 1, ease: 'easeOut' }}>
        <button
          className='h-16 bg-white  rounded flex flex-row items-center justify-between  w-full '
          onClick={() => setShow(!show)}
          type='button'>
          <div className='h-16 bg-white  rounded flex flex-row items-center   w-full '>
            <div className='w-12 h-full bg-neutral-blue flex flex-row items-center justify-center rounded-tl rounded-bl   focus:outline-none'>
              <Image
                src='/images/extrato-simplificado.png'
                alt='Icon'
                width={30}
                height={30}
                priority
              />
            </div>
            <p className='ml-4 text-base text-navy-blue tracking-1 leading-4'>
              Extrato simplificado
            </p>
          </div>
          {show ? (
            <Image
              src='/images/less.svg'
              alt='Icon'
              className='rounded-full mr-4'
              width={17}
              height={17}
              priority
            />
          ) : (
            <Image
              src='/images/more.svg'
              alt='Icon'
              className='rounded-full mr-4'
              width={17}
              height={17}
              priority
            />
          )}
        </button>

        <AnimatePresence>
          {show && (
            <motion.div
              initial='collapsed'
              animate={show ? 'open' : 'collapsed'}
              variants={AnimateFadeInOpenHeight}
              exit='exit'
              transition={{ duration: 0.3, ease: 'easeInOut' }}>
              <ButtonActionExtract
                src='/images/enviar-por-email.png'
                text='Enviar por e-mail'
                onClick={() => action('email')}
                permission={true}
              />

              <ButtonActionExtract
                src='/images/baixar-pdf.png'
                text='Baixar PDF'
                onClick={() => action('download')}
                permission={true}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </>
  );
}
