/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import SpinnerLoading from '@/templates/components/Commons/Loading/SpinnerLoading';
import CardInstallment from '../../NegotiateYourInstallmentsScreen/components/CardInstallment';
import { useFinancialData } from '../hooks/useFinancialData';

export default function InstallmentTableMobile() {
  const { allInstallments, installmentDetail } = useFinancialData();
  if (allInstallments === null) return null;
  return (
    <>
      {allInstallments.length === 0 || allInstallments === null || allInstallments === undefined ? (
        <div className='flex justify-center mt-4'>
          <SpinnerLoading />
        </div>
      ) : (
        <table className='table-auto w-full'>
          <thead>
            <tr>
              <th className='pb-3 pl-3 text-navy-blue text-md font-normal tracking-wide text-left'>
                Vencimento
              </th>
              <th className='pb-3 text-navy-blue text-md font-normal tracking-wide text-left'>
                Tipo/Valor
              </th>
              <th className='pb-3 text-navy-blue text-md font-normal tracking-wide text-left'>
                Status
              </th>
            </tr>
          </thead>
          <tbody>
            {allInstallments &&
              allInstallments.map((installment, index) => (
                <CardInstallment
                  key={`CardInstallmentMobile-${index}-${installment.id}`}
                  index={index}
                  installment={installment}
                  detail={installmentDetail(installment.id, installment.parentIndex as number)}
                />
              ))}
          </tbody>
        </table>
      )}
    </>
  );
}
