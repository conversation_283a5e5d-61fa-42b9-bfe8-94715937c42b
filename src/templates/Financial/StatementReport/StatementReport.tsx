/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useLoading } from '@/hooks';

import { useAppContext } from '@/contexts/AppContext';
import { useEffect } from 'react';
import ExtractActionsTable from './components/ExtractActionsTable';
import FinancialTable from './components/FinancialTable';
import InstallmentDetails from './components/InstallmentDetails';

const StatementReportScreen = () => {
  const { setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();

  useEffect(() => {
    setSubtitleHeaderMobile('Relatório de Extrato');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (isMobile === null) return null;
  return (
    <>
      <div className='text-navy-blue max-md:w-full'>
        <h3 className='font-bold tracking-1 text-3xl mb-5 max-md:hidden'>Financeiro</h3>
        <h4 className='font-medium text-xl tracking-1 max-md:hidden'>Relatório de Extrato</h4>
        <p className='text-sm tracking-1 mb-4 w-2/3 max-md:w-full'>
          Confira todas as informações referentes à situação do seu imóvel: parcelas, vencimentos,
          pagamentos, valores a pagar, etc.
        </p>

        <div className='grid grid-cols-2 gap-8 max-md:grid-cols-none max-md:flex max-md:flex-col-reverse'>
          <FinancialTable />
          <ExtractActionsTable />
        </div>

        <InstallmentDetails />
      </div>
    </>
  );
};

export default StatementReportScreen;
