/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { InstallmentItem, Receipt } from '@/@types/installments';
import { DateUtils } from '@/utils/DateUtils';
import { Status } from '../../Status';

/**
 * Filtra parcelas com base em certos critérios
 * @param installment Parcela a ser verificada
 * @param showFn Flag que indica se exibe parcelas do tipo FN
 */
export const filterInstallment = (installment: InstallmentItem, showFn: boolean): boolean => {
  const tiposExcluidos = ['Reparcelamento', 'Substituição', 'Cancelamento'];
  const cond1 = installment.receipts.filter((receipt) => !tiposExcluidos.includes(receipt.type));
  const condFn = ['FN'].includes(installment.paymentTerms.id);
  const cond2 = condFn ? showFn : cond1;
  return Boolean(cond1 && cond2);
};

/**
 * Determina o status do boleto
 * @param installmentSituation Situação da parcela
 * @param date Data de vencimento
 * @param receipts Recibos
 * @param currentBalanceWithAddition Saldo atual com adições
 */
export const getBoletoStatus = (
  installmentSituation: string,
  date: string,
  receipts: Receipt[],
  currentBalanceWithAddition: number
): string => {
  const today = new Date().toISOString().split('T')[0];
  const isDueDatePast = DateUtils.compareDates(today, date);
  const statusBoletoIndex: string | null =
    installmentSituation === '0' && isDueDatePast ? 'Vencido' : receipts[0].type;

  const key = statusBoletoIndex === null ? 'null' : statusBoletoIndex;
  return key === 'Recebimento' && currentBalanceWithAddition !== 0
    ? 'A vencer'
    : (Status[key] as string);
};

/**
 * Retorna o valor total pago das parcelas
 * @param receipts Recibos
 */
export const getTotalPaidValue = (receipts: Receipt[]): number => {
  return receipts.reduce((sum, receipt) => sum + receipt.netReceipt, 0);
};

/**
 * Prepara dados da parcela para o detalhe
 */
export const prepareInstallmentRowData = (
  id: number,
  titleName: string,
  contractType: string,
  typeCondiction: string,
  duedateFormated: string,
  receiptDate: string,
  originalValue: string,
  indexerName: string,
  correctionValue: string,
  currentJurosMulta: string,
  currentBalanceWithAddition: string,
  paidValue: string,
  paidTotalValue: string,
  statusBoleto: string
) => {
  const customClassDefault = ' text-nowrap';

  return [
    {
      value: id < 10 ? `0${id}` : id,
      customClass: customClassDefault
    },
    {
      value: titleName,
      customClass: customClassDefault
    },
    {
      value: contractType,
      customClass: customClassDefault
    },
    {
      value: typeCondiction,
      customClass: ''
    },
    {
      value: duedateFormated,
      customClass: customClassDefault
    },
    {
      value: receiptDate,
      customClass: customClassDefault
    },
    {
      value: originalValue,
      customClass: customClassDefault
    },
    {
      value: indexerName,
      customClass: customClassDefault
    },
    {
      value: correctionValue,
      customClass: customClassDefault
    },
    {
      value: currentJurosMulta,
      customClass: customClassDefault
    },
    {
      value: currentBalanceWithAddition,
      customClass: customClassDefault
    },
    {
      value: paidValue,
      customClass: ''
    },
    {
      value: paidTotalValue,
      customClass: ''
    },
    {
      value: statusBoleto,
      customClass:
        statusBoleto === 'Vencido' ? `${customClassDefault} text-salmon` : customClassDefault
    }
  ];
};
