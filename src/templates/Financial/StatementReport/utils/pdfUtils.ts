/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { InstallmentItem } from '@/@types/installments';
import { MiscUtils } from '@/utils/MiscUtils';
import { jsPDF } from 'jspdf';
import { RowInput, Styles } from 'jspdf-autotable';

/**
 * Cria um novo documento PDF
 */
export const createPdfDocument = (): jsPDF => {
  return new jsPDF({
    orientation: 'l',
    unit: 'mm',
    format: 'a4'
  });
};

/**
 * Adiciona o logo ao documento PDF
 */
export const addLogoToDocument = async (doc: jsPDF): Promise<boolean> => {
  try {
    const svgString = await fetch('/images/logo.svg').then((response) => response.text());
    const imgData = await MiscUtils.svgToBase64(svgString);

    const pageWidth = doc.internal.pageSize.width;
    const imgWidth = 80;
    const imgHeight = 26;
    const imgX = (pageWidth - imgWidth) / 2;
    doc.addImage(imgData, 'PNG', imgX, 10, imgWidth, imgHeight);

    return true;
  } catch (error) {
    console.error('Error adding logo:', error);
    return false;
  }
};

/**
 * Adiciona o título ao documento PDF
 */
export const addTitleToDocument = (doc: jsPDF): void => {
  const pageWidth = doc.internal.pageSize.width;
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor('#1a374d');
  doc.text('EXTRATO SIMPLIFICADO', pageWidth / 2, 50, { align: 'center' });
};

/**
 * Adiciona configurações de estilo de cabeçalho para tabelas
 */
export const getHeaderStyle = (): Partial<Styles> => ({
  fillColor: [220, 220, 220],
  textColor: [26, 55, 77],
  fontStyle: 'bold' as const,
  cellWidth: 'wrap'
});

/**
 * Adiciona configurações de estilo para células de tabelas
 */
export const getTableStyle = (fontSize = 10): Partial<Styles> => ({
  fontSize,
  cellPadding: 2,
  textColor: [26, 55, 77] as [number, number, number],
  cellWidth: 'auto'
});

/**
 * Prepara rows para a tabela de detalhes de parcelas
 */
export const prepareDetailsTableBody = (
  allInstallments: InstallmentItem[],
  installmentDetailFn: (id: number, parentIndex: number) => any
): RowInput[] => {
  return allInstallments
    .map((installment) => {
      const detail = installmentDetailFn(
        installment?.id as number,
        installment?.parentIndex as number
      );
      return detail ? detail.rowsData.map((cell: any) => cell.value) : [];
    })
    .filter((row) => row.length > 0) as RowInput[];
};

/**
 * Adiciona estilos para as colunas da tabela de detalhes
 */
export const getDetailsColumnStyles = () => {
  return {
    0: { cellWidth: 18, minCellHeight: 10, halign: 'center', valign: 'middle' },
    1: { cellWidth: 15, minCellHeight: 10, halign: 'center', valign: 'middle' },
    2: { cellWidth: 15, minCellHeight: 10, halign: 'center', valign: 'middle' },
    3: { cellWidth: 25, minCellHeight: 10, halign: 'left', valign: 'middle' },
    4: { cellWidth: 17, minCellHeight: 10, halign: 'center', valign: 'middle' },
    5: { cellWidth: 16, minCellHeight: 10, halign: 'center', valign: 'middle' },
    6: { cellWidth: 20, minCellHeight: 10, halign: 'left', valign: 'middle' },
    7: { cellWidth: 15, minCellHeight: 10, halign: 'center', valign: 'middle' },
    8: { cellWidth: 20, minCellHeight: 10, halign: 'left', valign: 'middle' },
    9: { cellWidth: 15, minCellHeight: 10, halign: 'left', valign: 'middle' },
    10: { cellWidth: 20, minCellHeight: 10, halign: 'left', valign: 'middle' },
    11: { cellWidth: 20, minCellHeight: 10, halign: 'left', valign: 'middle' },
    12: { cellWidth: 20, minCellHeight: 10, halign: 'left', valign: 'middle' },
    13: { cellWidth: 15, minCellHeight: 10, halign: 'center', valign: 'middle' }
  };
};
