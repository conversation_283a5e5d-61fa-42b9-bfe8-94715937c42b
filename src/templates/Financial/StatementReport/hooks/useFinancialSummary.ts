/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Financials } from '@/@types/financial';
import { Installments } from '@/@types/installments';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';
import { useMemo } from 'react';

interface UseFinancialSummaryProps {
  installments: Installments | null;
  showFn: boolean;
}

export const useFinancialSummary = ({
  installments,
  showFn
}: UseFinancialSummaryProps): Financials | null => {
  return useMemo(() => {
    if (!installments || installments.data === null) {
      return null;
    }

    let totalPaid = 0; // valor total pago
    let totalInstallmentsPaid = 0; //numero de parcelas pagas
    let totalPayable = 0; // valor total a vencer
    let totalInstallmentsPayable = 0; // numero de parcelas a vencer
    let totalOutstanding = 0; // valor total vencido
    let totalOutstandingInstallments = 0; // numero de parcelas vencidas

    // Calcula todos os totais em uma única iteração
    installments.data.forEach((subArray) => {
      subArray.installments.forEach((installment) => {
        const {
          isoDueDate,
          dueDate,
          paymentTerms,
          receipts,
          installmentSituation,
          currentBalanceWithAddition
        } = installment;

        // Verificações de datas e status
        const today = new Date().toISOString().split('T')[0];
        const isDueDatePast = DateUtils.compareDates(today, (isoDueDate ?? dueDate) as string);

        // Cálculo de valores pagos
        const netReceipt = receipts.reduce((sum, receipt) => sum + receipt.netReceipt, 0);
        let totalInstallmentsPaidVerify = 1;
        let currentBalanceWithAdditionLocal = currentBalanceWithAddition;

        // Tratamento especial para parcelas do tipo FN
        const isFnPaymentTerm = ['FN'].includes(paymentTerms.id);
        const shouldSkipFn = isFnPaymentTerm && !showFn;

        if (shouldSkipFn) {
          totalInstallmentsPaidVerify = 0;
          currentBalanceWithAdditionLocal = 0;
        } else if (isFnPaymentTerm && currentBalanceWithAddition !== 0) {
          totalInstallmentsPaidVerify = 0;
        }

        // Acumula valores pagos
        totalPaid += shouldSkipFn ? 0 : netReceipt;

        // Acumula parcelas pagas
        if (receipts[0]?.type === 'Recebimento' || receipts[0]?.type === 'Adiantamento') {
          totalInstallmentsPaid += totalInstallmentsPaidVerify;
        }

        // Acumula valores a vencer
        if (currentBalanceWithAdditionLocal !== 0 && receipts[0]?.days === 0 && !isDueDatePast) {
          totalInstallmentsPayable++;
          totalPayable += currentBalanceWithAdditionLocal;
        }

        // Acumula valores vencidos
        if (installmentSituation === '0' && isDueDatePast) {
          totalOutstanding++;
          totalOutstandingInstallments += currentBalanceWithAddition;
        }
      });
    });

    // Formata os valores calculados
    return {
      totalPaid: FinanceUtils.MoneyFormat.format(totalPaid),
      totalInstallmentsPaid: totalInstallmentsPaid.toString(),
      totalPayable: FinanceUtils.MoneyFormat.format(totalPayable),
      totalInstallmentsPayable: totalInstallmentsPayable.toString(),
      totalOutstandingInstallments: FinanceUtils.MoneyFormat.format(totalOutstandingInstallments),
      totalOutstanding: totalOutstanding.toString(),
      total: FinanceUtils.MoneyFormat.format(
        totalPayable + totalPaid + totalOutstandingInstallments
      )
    };
  }, [installments, showFn]);
};
