/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { ContentContext, Financials } from '@/@types/financial';
import { InstallmentDetails, InstallmentItem, Installments } from '@/@types/installments';
import { useAppContext } from '@/contexts/AppContext';
import { useInstallments } from '@/hooks/Installments/useInstallments';
import { useEffect } from 'react';

import { useFinancialSummary } from './useFinancialSummary';
import { useInstallmentProcessor } from './useInstallmentProcessor';
import { usePdfGenerator } from './usePdfGenerator';

/**
 * Hook principal para gerenciamento de dados financeiros
 */
export const useFinancialData = (): {
  financials: Financials | null;
  content: ContentContext['content'];
  installments: Installments | null;
  copybillData: InstallmentItem[] | null;
  allInstallments: InstallmentItem[] | null;
  installmentDetail: (id: number, parentIndex: number) => InstallmentDetails;
  action: (type: 'download' | 'email') => Promise<void>;
} => {
  const { user, content } = useAppContext();
  const { allIndexer, getIndexerName, installments, getInstallments } = useInstallments();

  // Flag que indica se deve exibir parcelas do tipo FN
  const showFn = content ? content?.Empreendimento.Empreendimento_novo__c : false;

  // Carrega dados de parcelas quando o usuário ou conteúdo mudam
  useEffect(() => {
    if (user?.CodigoSienge__c && content?.UnidadeAtivoName) {
      getIndexerName(0);
      getInstallments(user?.CodigoSienge__c, content?.UnidadeAtivoName);
    }
  }, [user, content, getInstallments, getIndexerName]);

  // Processa parcelas e detalhes
  const { allInstallments, installmentDetail, copybillData } = useInstallmentProcessor({
    installments,
    allIndexer,
    showFn
  });

  // Calcula o resumo financeiro
  const financials = useFinancialSummary({ installments, showFn });

  // Gerencia a geração e exportação do PDF
  const { action } = usePdfGenerator({
    user,
    content,
    financials,
    allInstallments: allInstallments as InstallmentItem[],
    installmentDetailFn: installmentDetail
  });

  return {
    financials,
    content,
    installments,
    copybillData,
    allInstallments: allInstallments as InstallmentItem[],
    installmentDetail,
    action
  };
};
