/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { WHATS_NUMBER } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useLoading } from '@/hooks/useLoading';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import Link from 'next/link';
import { useEffect, useLayoutEffect } from 'react';
import TitlePage from '../components/Commons/TitlePage';
import CopyBillTable from './StatementReport/components/CopyBillTable';
import { useFinancialData } from './StatementReport/hooks/useFinancialData';

export default function CopyBillScreen() {
  const { loading, setLoadingInfo } = useLoading();
  const { canBeRender } = useCanBeRender();
  const { user, setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();
  const { content, copybillData } = useFinancialData();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('2º via de Boleto');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [isMobile === null, !user && !content && copybillData !== null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      {loading ? null : (
        <>
          <TitlePage text='Financeiro' />
          <h4 className='text-navy-blue font-medium text-xl tracking-1 max-md:hidden'>
            2º via de Boleto
          </h4>
          <p className='text-sm text-navy-blue tracking-1'>
            Atenção: a emissão de 2ª via de boleto é válida apenas para parcelas a vencer e não para
            parcelas vencidas.
          </p>
          <p className='text-sm text-navy-blue tracking-1 mt-4 mb-4'>
            Para emissão de boleto de uma parcela vencida entre em contato com nossa equipe de
            negociação. Para entrar em contato via WhatsApp.{' '}
            {DeviceUtils.isMobileApp() ? (
              <button
                className='text-sm underline text-navy-blue tracking-1 mb-4'
                onClick={() =>
                  BrowserUtils.ActionReactNative({
                    type: 'openUrlBrowser',
                    url: WHATS_NUMBER,
                    data: {}
                  })
                }>
                clique aqui.
              </button>
            ) : (
              <Link
                className='text-sm underline text-navy-blue tracking-1 mb-4'
                href={WHATS_NUMBER}
                target='_blank'>
                clique aqui.
              </Link>
            )}
          </p>
          <hr className='hidden border-t-1 border-white mb-6 mt-6 w-full max-md:block' />
          <CopyBillTable />
        </>
      )}
    </>
  );
}
