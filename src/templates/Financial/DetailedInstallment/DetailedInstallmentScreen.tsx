/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
// import { useEffect, useState } from 'react';

import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';

import { InstallmentDetails } from '@/@types/installments';
import { useAppContext } from '@/contexts/AppContext';
import { useInstallments, useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useHacks } from '@/hooks/useHacks';
import Boleto from '@/templates/components/Boleto/Boleto';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import TitlePage from '@/templates/components/Commons/TitlePage';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useFinancialData } from '../StatementReport/hooks/useFinancialData';

interface DetailedInstallmentProps {
  id: number;
  installmentId: number;
  copy?: boolean;
}

const DetailedInstallmentScreen = ({
  id,
  installmentId,
  copy = false
}: DetailedInstallmentProps) => {
  const [showBoleto, setShowBoleto] = useState(false);
  const toggleBoleto = () => setShowBoleto((prevShowBoleto) => !prevShowBoleto);
  const [details, setDetails] = useState<InstallmentDetails | null>(null);
  const { canBeRender } = useCanBeRender();
  const { user, content, isMobile } = useAppContext();
  const { urlBoleto, digitableNumberBoleto, getBoleto } = useInstallments();
  const { allInstallments, installmentDetail } = useFinancialData();
  const { goBack } = useHacks();
  useEffect(() => {
    const loadData = async () => {
      const idFinal = typeof id === 'string' ? parseInt(id, 10) : id;
      const installmentIdFinal =
        typeof installmentId === 'string' ? parseInt(installmentId, 10) : installmentId;

      const data = installmentDetail(idFinal, installmentIdFinal);

      if (data) {
        setDetails(data);
        if (copy) {
          await getBoleto(idFinal, data.billReceivableId, content?.UnidadeAtivoName as string);
        }
      }
    };
    loadData();
  }, [allInstallments]);

  const { setSubtitleHeaderMobile, setShowSidebar } = useAppContext();
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Parcela detalhada');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [isMobile === null, allInstallments === null, details === null];
  if (!canBeRender({ conditions })) return null;
  if (details === null || user === null) return null;
  const {
    installmentNumber,
    titleName,
    contractType,
    typeCondiction,
    duedateFormated,
    receiptDate,
    originalValue,
    indexerName,
    correctionValue,
    currentJurosMulta,
    currentBalanceWithAddition,
    paidValue,
    paidTotalValue,
    statusBoleto,
    generatedBillet
  } = details;

  const password =
    user && user.CPF__c ? user?.CPF__c.replace(/\D/g, '') : user?.CNPJ__c.replace(/\D/g, '');

  return (
    <>
      <TitlePage text='Financeiro' />
      <h4 className='text-navy-blue font-medium text-xl tracking-1 max-md:hidden mb-4'>
        Parcela detalhada
      </h4>

      <div className='bg-white'>
        <div className='p-3'>
          <div className='flex flex-row justify-between items-center'>
            <div>
              <small className='text-xs text-neutral-blue tracking-1 mb-0 flex'>Parcela</small>
              <p className='font-bold tracking-wide text-navy-blue text-base leading-4'>
                {installmentNumber}
              </p>
            </div>
            <div>
              <small className='text-xs text-neutral-blue tracking-1 mb-0 flex'>Vencimento</small>
              <p className='font-bold tracking-wide text-navy-blue text-base leading-4'>
                {duedateFormated}
              </p>
            </div>
            <div>
              <small className='text-xs text-neutral-blue tracking-1 mb-0 flex'>Status</small>
              <p
                className={`tracking-wide text-blue-secondary text-sm leading-4 ${statusBoleto === 'Vencido' ? 'text-salmon' : 'text-navy-blue'}`}>
                {statusBoleto}
              </p>
            </div>
          </div>
          <div className='mt-5'>
            <small className='text-xs text-neutral-blue tracking-1 mb-2.5 flex'>
              Data da Baixa:
              <p className='tracking-1 text-navy-blue text-sm leading-4 ml-2 font-bold max-md:font-normal'>
                {receiptDate}
              </p>
            </small>
            <small className='text-xs text-neutral-blue tracking-1 mb-2.5 flex'>
              Título:
              <p className='tracking-1 text-navy-blue text-sm leading-4 ml-2 font-bold max-md:font-normal'>
                {titleName}
              </p>
            </small>
            <small className='text-xs text-neutral-blue tracking-1 mb-2.5 flex'>
              Tipo de Contrato:
              <p className='tracking-1 text-navy-blue text-sm leading-4 ml-2 font-bold max-md:font-normal'>
                {contractType}
              </p>
            </small>
            <small className='text-xs text-neutral-blue tracking-1 mb-2.5 flex'>
              Tipo de Condição:
              <p className='tracking-1 text-navy-blue text-sm leading-4 ml-2 font-bold max-md:font-normal'>
                {typeCondiction}
              </p>
            </small>
          </div>
        </div>
        <hr className='border-t-1 border-light-gray' />
        <div className='p-3 mt-3'>
          <div className='flex flex-row justify-between items-center mb-4'>
            <small className='text-xs text-neutral-blue tracking-1 flex'>Valor original</small>
            <hr className='border-b-1 border-dashed border-neutral-blue w-3/4 max-md:hidden' />
            <p className='tracking-1 text-navy-blue text-sm leading-4'>{originalValue}</p>
          </div>
          <div className='flex flex-row justify-between items-center mb-4'>
            <small className='text-xs text-neutral-blue tracking-1 flex'>Valor atualizado</small>
            <hr className='border-b-1 border-dashed border-neutral-blue w-3/4 max-md:hidden' />
            <p className='tracking-1 text-navy-blue text-sm leading-4'>
              {currentBalanceWithAddition}
            </p>
          </div>
          <div className='flex flex-row justify-between items-center mb-4'>
            <small className='text-xs text-neutral-blue tracking-1 flex'>Índice de Correção</small>
            <hr className='border-b-1 border-dashed border-neutral-blue w-3/4 max-md:hidden' />
            <p className='tracking-1 text-navy-blue text-sm leading-4'>{indexerName}</p>
          </div>
          <div className='flex flex-row justify-between items-center mb-4'>
            <small className='text-xs text-neutral-blue tracking-1 flex'>Valor Correção</small>
            <hr className='border-b-1 border-dashed border-neutral-blue w-3/4 max-md:hidden' />
            <p className='tracking-1 text-navy-blue text-sm leading-4'>{correctionValue}</p>
          </div>

          <div className='flex flex-row justify-between items-center mb-4'>
            <small className='text-xs text-neutral-blue tracking-1 flex'>Multa+juros</small>
            <hr className='border-b-1 border-dashed border-neutral-blue w-3/4 max-md:hidden' />
            <p className='tracking-1 text-navy-blue text-sm leading-4'>{currentJurosMulta}</p>
          </div>
          <div className='flex flex-row justify-between items-center mb-4'>
            <small className='text-xs text-neutral-blue tracking-1 flex'>Valor a pagar</small>
            <hr className='border-b-1 border-dashed border-neutral-blue w-3/4 max-md:hidden' />
            <p className='tracking-1 text-navy-blue text-sm leading-4'>{paidValue}</p>
          </div>
          <div className='flex flex-row justify-between items-center mb-4'>
            <small className='text-xs text-neutral-blue tracking-1 flex'>Valor Pago</small>
            <hr className='border-b-1 border-dashed border-neutral-blue w-3/4 max-md:hidden' />
            <p className='tracking-1 text-navy-blue text-sm leading-4 font-bold'>
              {paidTotalValue}
            </p>
          </div>
        </div>
      </div>
      <div className='flex justify-center flex-row -mt-3 w-1/2 max-md:w-full m-auto'>
        <div className='w-1/2 mr-1'>
          <Buttontransparent color='navy-blue' text='Voltar' onClick={goBack} />
        </div>
        {copy && (
          <>
            <div className='w-1/2 ml-1 flex justify-center flex-col align-items-center'>
              {statusBoleto === 'Pago' ? (
                <Buttonblue
                  text={'Boleto Pago'}
                  background='blue-secondary'
                  color='white'
                  classExtra='pointer-events-none'
                  // disabled={true}
                />
              ) : (
                <>
                  {generatedBillet ? (
                    <Buttonblue
                      text='Gerar Boleto'
                      background='navy-blue'
                      color='white'
                      onClick={() => setShowBoleto(true)}
                    />
                  ) : (
                    <>
                      <Buttonblue
                        text={statusBoleto === 'Vencido' ? 'Boleto vencido' : 'Boleto não gerado'}
                        background={statusBoleto === 'Vencido' ? 'salmon' : 'navy-blue'}
                        color='white'
                      />
                      {statusBoleto === 'Vencido' && <div>Entrar em contato com o financeiro</div>}
                    </>
                  )}
                </>
              )}
            </div>
          </>
        )}
      </div>
      {urlBoleto && (
        <Boleto
          filename={`boleto-${installmentNumber}-${titleName}.pdf`}
          urlBoleto={urlBoleto}
          digitableNumberBoleto={digitableNumberBoleto}
          // password={password}
          show={showBoleto}
          toggleBoleto={toggleBoleto}
        />
      )}
    </>
  );
};

export default DetailedInstallmentScreen;
