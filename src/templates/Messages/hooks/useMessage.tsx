/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { useState } from 'react';

import { Message, MessageCount, MessageResponse, MessagesResponse } from '@/@types/messages';
import { useData } from '@/hooks/useData';
import { apiGet } from '@/server/services/api';

const API_FAILED = 'API call failed:';
export const useMessage = () => {
  const [error] = useState<Error | null>(null);
  const { getCookie } = useData();
  // const { getFetch } = useApi();

  const getMessage = async (messageId: string) => {
    const messageUser = await apiGet<MessageResponse>(`message/${messageId}`);
    // const messageUser = await getFetch<MessageResponse>({
    //   method: 'GET',
    //   route: 'message',
    //   params: {
    //     queryString: {
    //       id: messageId
    //     }
    //   }
    // });
    if (messageUser && messageUser?.data) {
      return messageUser.data.message;
    } else {
      // setError(messageUser as Error);
      return messageUser;
    }
  };

  const getCountMessagesUnread = async (accountId: string) => {
    if (!accountId) return 0;
    try {
      const messages = await apiGet<MessageCount>(
        `message/getCountMessagesUnread?accountId=${accountId}`
      );
      // const messages = await getFetch<MessageCount>({
      //   method: 'GET',
      //   route: 'message/getCountMessagesUnread',
      //   params: {
      //     queryString: {
      //       accountId
      //     }
      //   }
      // });
      if (messages && messages?.data) {
        return messages.data.total;
      }
    } catch (err) {
      return 0;
    }

    return 0;
  };

  const getMessages = async () => {
    const accountId = getCookie('AccountId') as string;
    if (!accountId) return [] as Message[];
    try {
      const messagesUser = await apiGet<MessagesResponse>(
        `message/showMessages?accountId=${accountId}`
      );
      // const messagesUser = await getFetch<MessagesResponse>({
      //   method: 'GET',
      //   route: 'message/showMessages',
      //   params: {
      //     queryString: {
      //       accountId
      //     }
      //   }
      // });

      return messagesUser?.data.messages as Message[];
    } catch (error: unknown) {
      console.error(API_FAILED, error);
      return error as Error;
    }
  };
  return {
    getMessage,
    getCountMessagesUnread,
    getMessages,
    error
  };
};
