/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Suspense, useEffect, useState } from 'react';

import { useAppContext } from '@/contexts/AppContext';
import MessageList from '@/templates/Messages/components/MessageList';

import { useLoading } from '@/hooks';

import { Message } from '@/@types/messages';
import { Buttonblue } from '../components/Commons/buttons/Buttonblue';
import { useMessage } from './hooks/useMessage';

export default function MessagesScreen() {
  const { user, setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();
  const [messages, setMessages] = useState<Message[] | null>(null);
  const { getMessages } = useMessage();
  useEffect(() => {
    if (user) {
      const fetchMessage = async () => {
        const messageLoaded = (await getMessages()) as Message[];
        setMessages(messageLoaded ? messageLoaded : null);
      };
      fetchMessage();
    }
  }, []);

  useEffect(() => {
    setSubtitleHeaderMobile('Mensagens');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  if (messages === null || user === null) return null;

  return (
    <Suspense fallback={'...carregando'}>
      <div className='flex flex-col bg-white rounded px-1 max-md:px-0 h-[auto] mb-10'>
        {!messages ||
          (messages.length === 0 && (
            <p className='font-bold text-base text-navy-blue tracking-1 mt-2 mb-4 bg-white p-4 text-center'>
              Você ainda não possui mensagens.
            </p>
          ))}
        {messages &&
          messages.map((message: Message) => (
            <MessageList key={message.MessageId} message={message} />
          ))}
        <div className='flex justify-center items-center mb-8 '>
          <Buttonblue
            text='Voltar'
            background='navy-blue'
            color='white'
            route={`/${user.AccountId}/home`}
            classExtra={'max-w-[200px]'}
          />
        </div>
      </div>
    </Suspense>
  );
}
