/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';

import { Message } from '@/@types/messages';
import HtmlExternalComponent from '@/templates/components/Commons/external/HtmlExternalComponent';

interface MensagemProps {
  message: Message;
}

const MessageContent = ({ message }: MensagemProps) => {
  return (
    <>
      <div className={' flex-row items-center px-5 rounded py-3 hover:cursor-pointer flex'}>
        <Image
          src='/images/envelope-open.png'
          alt='Icon'
          className=''
          width={36}
          height={36}
          priority
        />
        <div className='flex flex-col ml-4'>
          <p className='tracking-1 font-normal text-base text-navy-blue'>{message.Subject}</p>
          <small className='flex flex-row tracking-widest font-normal text-sm text-navy-blue'>
            <span>De:</span>
            <span className='text-blue-secondary tracking-wide ml-1'>{message.FromName}</span>
          </small>
        </div>
      </div>
      {/* <p
        className={`flex border-t border-light-gray pt-4 mt-1 px-5 text-navy-blue font-medium tracking-1 text-base mb-5 flex-col`}
        dangerouslySetInnerHTML={{ __html: message.HtmlBody }}
      /> */}
      <HtmlExternalComponent
        html={message.HtmlBody}
        className={'flex border-t border-light-gray pt-4 mt-1 px-5 text-navy-blue font-medium tracking-1 text-base mb-5 flex-col'}
      />
      {/* <div className="w-full h-full">
        <iframe
          className="w-full h-[60vh]"
          src={
            'https://view.rcc.cury.net/?qs=f80afc5ab2d1407541a9abc1ad0db25b3f31714ad55916b4e85baa620353c1ad8599a10209895de63989fe6ec40383512bb0fa2eca9c037c'
          }
        />
      </div> */}
    </>
  );
};

export default MessageContent;
