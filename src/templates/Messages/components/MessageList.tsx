/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';

import { Message } from '@/@types/messages';
import { useAppContext } from '@/contexts/AppContext';
import CustomLink from '@/templates/components/Commons/buttons/CustomLink';

interface MensagemProps {
  message: Message;
}

const MessageList = ({ message }: MensagemProps) => {
  const { user } = useAppContext();
  if (user === null) return null;
  return (
    <CustomLink key={message.id} href={`/${user?.AccountId}/mensagens/${message.id}`}>
      <div
        className={`
            ${
    message.read
      ? 'bg-light-gray flex-row items-center px-5 rounded py-3 hover:cursor-pointer flex border-b-1 border-b-white'
      : 'px-5 rounded flex-row items-center py-3 hover:cursor-pointer flex max-md:rounded-none'
    }`}>
        <Image
          src={message.read ? '/images/envelope-open.png' : '/images/envelope-close.png'}
          alt='Icon'
          className='w-9 h-9'
          width={36}
          height={36}
          priority
        />
        {!message.read && (
          <div className='absolute top-0.5 right-0 w-2.5 h-2.5 bg-salmon rounded-full'></div>
        )}
        <div className='flex flex-col ml-4'>
          <p className='tracking-1 font-normal text-base text-navy-blue'>{message.Subject}</p>
          <small className='flex flex-row tracking-widest font-normal text-sm text-navy-blue'>
            <span>De:</span>
            <span className='text-blue-secondary tracking-wide ml-1'>{message.FromName}</span>
          </small>
        </div>
      </div>
    </CustomLink>
  );
};

export default MessageList;
