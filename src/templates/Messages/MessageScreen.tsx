/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { Message } from '@/@types/messages';
import { useAppContext } from '@/contexts/AppContext';

import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import { useEffect, useLayoutEffect, useState } from 'react';
import MessageContent from './components/MessageContent';
import { useMessage } from './hooks/useMessage';

interface MessageScreenProps {
  messageId: string;
}

export default function MessageScreen({ messageId }: Readonly<MessageScreenProps>) {
  const { user, setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();
  const { canBeRender } = useCanBeRender();
  const { getMessage, error } = useMessage();
  const [message, setMessage] = useState<Message | null>(null);

  const { setLoadingInfo } = useLoading();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Mensagens');
    setShowSidebar(!isMobile);
  }, [isMobile, setSubtitleHeaderMobile, setShowSidebar]);

  useEffect(() => {
    const fetchMessage = async () => {
      if (messageId) {
        const fetchedMessage = await getMessage(messageId);
        setMessage(fetchedMessage as Message);
      }
      setLoadingInfo(false);
    };

    fetchMessage();
  }, [messageId]);

  const conditions = [user === null, isMobile === null, message === null];
  if (!canBeRender({ conditions })) return null;
  if (user === null) return null;
  return (
    <>
      {error ? (
        <p className='text-red-600'>{error.message}</p>
      ) : (
        <>
          <div className='flex flex-col bg-white rounded px-1'>
            {message && <MessageContent message={message} />}
          </div>
          <div className='flex justify-center w-full'>
            <Buttonblue
              text='Voltar'
              background='navy-blue'
              color='white'
              classExtra='w-full'
              route={`/${user?.AccountId}/mensagens`}
            />
          </div>
        </>
      )}
    </>
  );
}
