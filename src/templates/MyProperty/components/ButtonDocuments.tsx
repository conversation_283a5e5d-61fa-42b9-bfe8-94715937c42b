/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';

import type { DocumentType } from '@/@types/document';
import type { ButtonDocumentsProps } from '@/@types/Inputs';
import type { toggleModalProps } from '@/@types/Modal';
import { useModal } from '@/contexts/useModal';
import ButtonImage from '@/templates/components/Commons/buttons/ButtonImage';
import { downloadFile } from '@/utils/DownloadUtils';
import { StringUtils } from '@/utils/StringUtils';

async function handleDownloadDocument(
  props: ButtonDocumentsProps,
  toggleModal: ({ text, params }: toggleModalProps) => void
): Promise<void> {
  const { docs } = props;
  const srcDownloadDoc = getDownloadSource(docs);
  toggleModal({
    text: 'Seu download irá iniciar em breve.<br>Por favor, aguarde.',
    params: {}
  });
  const docname = getDocumentName(docs);
  const idDoc = docs.DocumentId;

  if (srcDownloadDoc && docname) {
    await downloadFile('', docname, toggleModal, idDoc);
    // await downloadFile(srcDownloadDoc, docname, toggleModal, idDoc);
  }
}

function getDocumentName(docs: DocumentType): string {
  if (docs.ProposalId || docs.ContractId || docs.AccountId) {
    return docs.Name || docs.TituloArquivo__c;
  }
  return docs.Subgrupo__c;
}

function getDownloadSource(docs: DocumentType): string {
  return docs.LinkS3 || docs.Link__c || docs.IdInterno__c;
}

export function ButtonDocuments(props: ButtonDocumentsProps) {
  const { docs } = props;
  const { toggleModal } = useModal();
  const docname = getDocumentName(docs);

  return (
    <ButtonImage
      key={`${docname}-${props.index}`}
      text={StringUtils.FileName(docname)}
      src='/images/download.png'
      onClick={() => handleDownloadDocument(props, toggleModal)}
    />
  );
}
