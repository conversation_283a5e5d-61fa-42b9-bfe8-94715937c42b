/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import type { DocumentType } from '@/@types/document';
import { ButtonDocuments } from './ButtonDocuments';

interface DocumentSectionProps {
  title: string;
  documents: DocumentType[];
  EmpreendimentoId?: string;
  emptyMessage?: string;
}

export const DocumentSection = ({
  title,
  documents,
  EmpreendimentoId,
  emptyMessage
}: DocumentSectionProps) => {
  
  if (!documents?.length) {
    return (
      <div className='flex mt-4 mb-4'>
        <p className='w-full font-bold text-base text-navy-blue tracking-1 mt-2 mb-4 bg-white p-4 text-center'>
          {emptyMessage || 'Não existem documentos cadastrados.'}
        </p>
      </div>
    );
  }

  return (
    <>
      <p className='font-bold text-base text-navy-blue tracking-1 mb-4'>{title}</p>
      {documents
        .sort((a, b) => {
          const nameA = a.Name || a.TituloArquivo__c || '';
          const nameB = b.Name || b.TituloArquivo__c || '';
          return nameA.localeCompare(nameB);
        })
        .map((docs: DocumentType, index: number) => (
          <ButtonDocuments
            key={`document-${docs.DocumentId}`}
            docs={docs}
            EmpreendimentoId={docs.EmpreendimentoId || ''}
            index={index}
          />
        ))}
    </>
  );
};
