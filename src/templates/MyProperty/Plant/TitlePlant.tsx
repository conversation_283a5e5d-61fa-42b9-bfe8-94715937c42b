/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
export default function TitlePlant({
  UnidadeAtivoName,
  EmpreendimentoName,
  CustomClass
}: {
  UnidadeAtivoName: string | null;
  EmpreendimentoName: string | null;
  CustomClass?: string | null;
}) {
  const customClass = CustomClass
    ? CustomClass
    : 'text-base text-navy-blue font-bold tracking-1 leading-6 mt-5 text-center whitespace-pre-wrap mb-5 md:mb-1';
  return (
    <p key={customClass} className={customClass}>
      Unid. {UnidadeAtivoName}
      <br />
      {EmpreendimentoName}
    </p>
  );
}
