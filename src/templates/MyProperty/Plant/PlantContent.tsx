/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { FILES_URL, WHATS_NUMBER } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { useMediaQuery, useModal } from '@/hooks';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import SpinnerLoading from '@/templates/components/Commons/Loading/SpinnerLoading';
import ModalPlant from '@/templates/components/Plant/ModalPlant';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { downloadFile } from '@/utils/DownloadUtils';

import { getServerContent } from '@/server/actions';
import { apiPost } from '@/server/services/api';
import TitlePlant from './TitlePlant';

// Interface para os dados da planta
interface PlantData {
  imgPlanta: string | null;
  imgPlantaToDownload: string | null;
  unidadeAtivoName: string;
  empreendimentoName: string;
  documentId: string;
  isImageValid: boolean;
}

// Função otimizada para verificar se a imagem carrega (mobile-friendly)
const validateImageExists = (imageUrl: string): Promise<boolean> => {
  if (!imageUrl) return Promise.resolve(false);

  return new Promise((resolve) => {
    const img = document.createElement('img');

    // Timeout reduzido para mobile (3 segundos)
    const timer = setTimeout(() => {
      resolve(false);
    }, 3000);

    img.onload = () => {
      clearTimeout(timer);
      resolve(true);
    };

    img.onerror = () => {
      clearTimeout(timer);
      resolve(false);
    };

    // Otimizações para mobile
    img.loading = 'eager';
    img.decoding = 'async';
    img.src = imageUrl;
  });
};

// Componente para exibir a imagem da planta com o botão de download
const PlantImage: React.FC<{
  imgPlanta: string;
  imgPlantaToDownload: string;
  isMobile: boolean | null;
  toggleImage: () => void;
  handleDownload: () => Promise<boolean | null>;
}> = ({ imgPlanta, isMobile, toggleImage, handleDownload }) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <div
      className={`transition-all duration-700 ease-in-out ${imageLoaded
          ? 'opacity-100 translate-y-0'
          : 'opacity-0 translate-y-4'
        }`}
    >
      <button type='button' className='flex items-center justify-center mb-4' onClick={toggleImage}>
        <Image
          src={imgPlanta}
          className={isMobile ? 'flex w-full h-full' : 'w-[400px]'}
          width={isMobile ? 350 : 500}
          height={isMobile ? 350 : 500}
          priority
          quality={isMobile ? 75 : 90}
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
          alt='planta'
          onLoad={() => setImageLoaded(true)}
        />
      </button>
      <div className='w-full flex justify-center mt-4 z-10'>
        <Buttonblue
          text='Fazer Download'
          background='navy-blue'
          color='white'
          disabled={false}
          onClick={handleDownload}
        />
      </div>
    </div>
  );
};

// Componente para quando a planta não está disponível
const PlantNotAvailable: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Pequeno delay para sincronizar com o fim do loading
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div
      className={`transition-all duration-700 ease-in-out ${isVisible
          ? 'opacity-100 translate-y-0'
          : 'opacity-0 translate-y-4'
        }`}
    >
      <p className='text-navy-blue text-lg text-center font-bold tracking-1 leading-5 mt-8'>
        A planta não está disponível no momento.
      </p>
      <p className='text-navy-blue text-lg text-center tracking-1 leading-5 mt-4'>
        Para solicitar uma cópia, entre em contato conosco pelo <WhatsAppLink />
      </p>
      <p className='text-navy-blue text-lg text-center mb-4 tracking-1 leading-5 mt-1'>
        {' '}
        Estaremos prontos para ajudar você!
      </p>
    </div>
  );
};

// Componente do link do WhatsApp
const WhatsAppLink: React.FC = () => {
  if (DeviceUtils.isMobileApp()) {
    return (
      <button
        type='button'
        className='text-sm underline text-navy-blue tracking-1 mb-4'
        onClick={() =>
          BrowserUtils.ActionReactNative({
            type: 'openUrlBrowser',
            url: WHATS_NUMBER,
            data: {}
          })
        }>
        WhatsApp. Clique aqui.
      </button>
    );
  }

  return (
    <Link
      className='text-navy-blue text-lg tracking-8 leading-8 underline mb-4'
      href={WHATS_NUMBER}
      target='_blank'>
      WhatsApp. Clique aqui.
    </Link>
  );
};

// Componente para exibir loading
const PlantLoading: React.FC = () => (
  <div className='flex flex-col items-center justify-center py-16'>
    <SpinnerLoading width={60} height={60} />
    <p className='text-navy-blue text-lg text-center tracking-1 leading-5 mt-4'>
      Verificando planta...
    </p>
  </div>
);

// Hook otimizado para processar os dados da planta (mobile-friendly)
async function usePlantData(content: any, contentSelected: any): Promise<PlantData | null> {

  if (content === null || contentSelected === undefined) {
    return {
      imgPlanta: null,
      imgPlantaToDownload: null,
      unidadeAtivoName: '',
      empreendimentoName: '',
      documentId: '',
      isImageValid: false
    };
  }

  if (content.ProposalId === null) {
    return {
      imgPlanta: null,
      imgPlantaToDownload: null,
      unidadeAtivoName: content?.UnidadeAtivoName ?? '',
      empreendimentoName: content?.Empreendimento?.name ?? '',
      documentId: '',
      isImageValid: false
    };
  }

  // Faz o parse se for string, senão usa como está
  let plantaArray: any[] = [];

  if (typeof content.planta === 'string') {
    try {
      plantaArray = JSON.parse(content.planta);
    } catch (e) {
      console.error('Erro ao fazer parse de content.planta:', e);
      plantaArray = [];
    }
  } else if (Array.isArray(content.planta)) {
    plantaArray = content.planta;
  }

  let plantaImage = plantaArray?.[0]?.Link__c;
  let documentId = plantaArray?.[0]?.Id;

  if (plantaImage === null || plantaImage === undefined) {
    const response = await apiPost('proposal/updateplanta', {
      ProposalId: content.ProposalId,
      PlantId: content.PlantaId ?? null
    });
    plantaImage = response.data?.plantaSrc;
    documentId = response.data?.documentId;
  }

  const srcplanta = plantaImage
    ? plantaImage?.startsWith('http')
      ? plantaImage
      : `${FILES_URL}${plantaImage}`
    : null;

  // Remove .webp se existir na srcplanta
  const srcplantaClean = srcplanta ? srcplanta.replace(/\.webp$/, '') : null;
  const validExt = srcplantaClean && !srcplantaClean.endsWith('.webp');
  const hasExt = validExt ? '.webp' : '';
  const imgPlanta = srcplantaClean ? `${srcplantaClean}${hasExt}` : null;
  const imgPlantaToDownload = imgPlanta;

  // Para mobile: retorna dados rapidamente e valida imagem em background
  const baseData = {
    imgPlanta,
    imgPlantaToDownload,
    unidadeAtivoName: content?.UnidadeAtivoName ?? '',
    empreendimentoName: content?.Empreendimento?.name ?? '',
    documentId: documentId ?? '',
    isImageValid: true // Assume true por padrão para carregamento mais rápido
  };

  return baseData;
}

// Componente principal
export default function PlantContent() {
  const { toggleModal } = useModal();
  const { isMobile } = useMediaQuery();
  const [showImage, setShowImage] = useState(false);
  const [plantData, setPlantData] = useState<PlantData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showTitle, setShowTitle] = useState(false);
  const { content, contentSelected } = useAppContext();

  useEffect(() => {
    const fetchContent = async () => {
      setIsLoading(true);
      setShowTitle(false);

      try {
        // Primeiro: carrega dados básicos rapidamente
        const basicPlantData = await usePlantData(content, contentSelected);
        if (basicPlantData) {
          setPlantData(basicPlantData);
          setIsLoading(false);
          setTimeout(() => setShowTitle(true), 200);
        }

        // Segundo: busca dados do servidor em background
        await getServerContent({
          ProposalId: content.ProposalId || null,
          ContractId: content.ContractId || null,
          type: content.type,
          AccountId: content.AccountId,
          selectedIndex: content.contentSelected,
          EmpreendimentoId: content.Empreendimento?.EmpreendimentoId,
          page: 'MyProperty'
        });

        // Terceiro: atualiza dados com informações do servidor
        const updatedPlantData = await usePlantData(content, contentSelected);
        if (updatedPlantData) {
          setPlantData(updatedPlantData);
        }

      } catch (error) {
        console.error('Error fetching content:', error);
        setIsLoading(false);
        setTimeout(() => setShowTitle(true), 200);
      }
    };

    // Se já temos dados básicos, mostra imediatamente
    if (content && (content.UnidadeAtivoName || content.Empreendimento?.name)) {
      const basicData = {
        imgPlanta: null,
        imgPlantaToDownload: null,
        unidadeAtivoName: content?.UnidadeAtivoName ?? '',
        empreendimentoName: content?.Empreendimento?.name ?? '',
        documentId: '',
        isImageValid: false
      };
      setPlantData(basicData);
      setIsLoading(false);
      setTimeout(() => setShowTitle(true), 100);
    }

    fetchContent();
  }, [content, contentSelected]);

  const {
    imgPlanta,
    imgPlantaToDownload,
    unidadeAtivoName,
    empreendimentoName,
    documentId,
    isImageValid
  } = plantData ?? {
    imgPlanta: null,
    imgPlantaToDownload: null,
    unidadeAtivoName: '',
    empreendimentoName: '',
    documentId: '',
    isImageValid: false
  };

  // Função para fazer download da planta
  const download = async () => {
    // if (imgPlantaToDownload === null) return null;
    await downloadFile('', 'planta', toggleModal, documentId);
    return true;
  };

  // Função para alternar a visualização da imagem
  const toggleImage = () => setShowImage(!showImage);
  return (
    <div className='flex flex-col bg-white rounded px-4 items-center justify-start py-4 h-[auto]'>
      <div className='flex flex-col items-center justify-center w-full'>
        {isLoading ? (
          <PlantLoading />
        ) : imgPlanta ? (
          <PlantImage
            imgPlanta={imgPlanta}
            imgPlantaToDownload={imgPlantaToDownload || ''}
            isMobile={isMobile}
            toggleImage={toggleImage}
            handleDownload={download}
          />
        ) : (
          <PlantNotAvailable />
        )}
      </div>
      {showTitle && (
        <div
          className='transition-all duration-500 ease-in-out opacity-0 translate-y-4'
          style={{
            opacity: showTitle ? 1 : 0,
            transform: showTitle ? 'translateY(0)' : 'translateY(16px)'
          }}
        >
          <TitlePlant
            UnidadeAtivoName={unidadeAtivoName || null}
            EmpreendimentoName={empreendimentoName || null}
          />
        </div>
      )}

      {imgPlanta && (
        <ModalPlant
          show={showImage}
          toogleImage={toggleImage}
          imgPlanta={imgPlanta}
          titlePlant={
            <TitlePlant
              UnidadeAtivoName={unidadeAtivoName || null}
              EmpreendimentoName={empreendimentoName || null}
              CustomClass='text-base text-white tracking-1 leading-6 mt-5 text-center whitespace-pre-wrap'
            />
          }
        />
      )}
    </div>
  );
}
