/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useEffect, useLayoutEffect } from 'react';
import GeneralMeetingContent from './GeneralMeeting/GeneralMeetingContent';

export default function GeneralMeetingScreen() {
  const { setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();
  const { canBeRender } = useCanBeRender();
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Assembleia Geral');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [isMobile === null];
  if (!canBeRender({ conditions })) return null;

  return <GeneralMeetingContent />;
}
