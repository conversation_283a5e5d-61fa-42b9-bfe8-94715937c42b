/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { DocumentType } from '@/@types/document';
import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { useEffect, useLayoutEffect, useMemo } from 'react';
import { DocumentSection } from './components/DocumentSection';
import { TYPE_GROUP_FILTER } from './MyDocuments/mock/filter';

const TITLE = 'Manual do Proprietário';
export default function OwnersManualScreen() {
  const { canBeRender } = useCanBeRender();
  const { content, setSubtitleHeaderMobile, setShowSidebar, isMobile } = useAppContext();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile(TITLE);
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const empreendimentoDocuments = useMemo(
    () =>
      Array.isArray(content?.Empreendimento.documents)
        ? content?.Empreendimento.documents
        : JSON.parse(content?.Empreendimento.documents || '[]'),
    [content?.Empreendimento.documents]
  );

  const manualDocuments = useMemo(
    () =>
      empreendimentoDocuments?.filter(
        (doc: DocumentType) => doc.Subgrupo__c === TYPE_GROUP_FILTER && content?.type === 'morador'
      ),
    [empreendimentoDocuments, content?.type]
  );

  const { setLoadingInfo } = useLoading();
  useEffect(() => {
    setLoadingInfo(false);
  }, []);

  const conditions = [isMobile === null, content === null];
  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <DocumentSection
        title='Manual do Proprietário'
        documents={manualDocuments}
        EmpreendimentoId={content?.EmpreendimentoId}
        emptyMessage='O Manual do Proprietário ainda não foi disponibilizado.'
      />
    </>
  );
}
