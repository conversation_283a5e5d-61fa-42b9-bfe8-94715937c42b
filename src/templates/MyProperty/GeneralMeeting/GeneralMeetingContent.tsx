/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import GeneralMeetingVideo from '@/templates/components/Commons/GeneralMeetingVideo';
import { DateUtils } from '@/utils/DateUtils';
import { getTextAssembleia } from '../StaticData';

export default function GeneralMeetingContent() {
  const { content, isMobile } = useAppContext();

  return (
    <div>
      {isMobile && <GeneralMeetingVideo />}
      <p className='text-base text-navy-blue tracking-1 leading-5 mt-5 font-bold'>
        O que é a assembleia geral de instalação de condomínio?
      </p>
      <p className='text-sm text-navy-blue tracking-1 leading-5 mt-4'>
        {getTextAssembleia(content?.Empreendimento.StatusMacro__c as string)}
      </p>
      {content?.Empreendimento.DataAGIRealizada__c && (
        <>
          <div className='flex mt-4 text-lg'>
            <span className=''>Data da Assembléia: </span>
            <p className=' font-bold text-salmon  ml-2'>
              {DateUtils.formatDate(content?.Empreendimento.DataAGIRealizada__c)}
            </p>
          </div>
        </>
      )}
    </div>
  );
}
