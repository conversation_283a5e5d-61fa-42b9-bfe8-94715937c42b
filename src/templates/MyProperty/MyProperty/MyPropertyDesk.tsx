/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useLoading, useMediaQuery } from '@/hooks';

import { useAppContext } from '@/contexts/AppContext';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import GeneralMeetingVideo from '@/templates/components/Commons/GeneralMeetingVideo';
import TitlePage from '@/templates/components/Commons/TitlePage';
import { useEffect, useLayoutEffect } from 'react';
import GeneralMeetingContent from '../GeneralMeeting/GeneralMeetingContent';
import MyDocumentsContent from '../MyDocuments/MyDocumentsContent';
import PlantContent from '../Plant/PlantContent';

export default function MyPropertyDesk() {
  const { setLoadingInfo } = useLoading();
  const { setSubtitleHeaderMobile, setShowSidebar, content } = useAppContext();
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Meu Imóvel');
    setShowSidebar(!isMobile);
  }, [isMobile]);

  const conditions = [isMobile === null, content === null];

  useEffect(() => {
    if (conditions) setLoadingInfo(false);
  }, [isMobile, content]);

  if (!canBeRender({ conditions })) return null;

  return (
    <>
      <div className='w-full'>
        <TitlePage text='Meu imóvel' />
        <div className='grid grid-cols-2 gap-4'>
          <GeneralMeetingVideo />
          <GeneralMeetingContent />
        </div>
        <div className='grid grid-cols-2 gap-4 mt-4'>
          <PlantContent />
          <MyDocumentsContent />
        </div>
      </div>
    </>
  );
}
