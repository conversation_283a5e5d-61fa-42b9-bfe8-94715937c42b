/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading, useMediaQuery } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { usePermissions } from '@/hooks/usePermissions';
import ButtonImage from '@/templates/components/Commons/buttons/ButtonImage';
import { useEffect, useLayoutEffect } from 'react';

export default function MyPropertyMobile() {
  const { content, setSubtitleHeaderMobile, setShowSidebar, user } = useAppContext();
  const { setLoadingInfo } = useLoading();
  const { getPermission } = usePermissions(content);
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();
  const canViewPlanta = getPermission('planta');
  const canViewAssembleia = getPermission('assembleiageral');
  const canViewDocumentos = getPermission('documentos');
  const canViewManual = getPermission('Manual do Proprietário');

  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Meu Imóvel');
    setShowSidebar(!isMobile);
    setLoadingInfo(true);
  }, [isMobile]);

  const conditions = [isMobile === null, content === null];

  useEffect(() => {
    if (conditions) setLoadingInfo(false);
  }, [isMobile, content]);

  if (!canBeRender({ conditions })) return null;
  if (user === null) return null;
  return (
    <>
      <div className='w-full'>
        <ButtonImage
          text='Planta do Imóvel'
          src='/images/planta.png'
          route={`/${user?.AccountId}/meu-imovel/planta`}
          permission={canViewPlanta}
        />
        <ButtonImage
          text='Assembleia Geral'
          src='/images/assembleia.png'
          route={`/${user?.AccountId}/meu-imovel/assembleia-geral`}
          permission={canViewAssembleia}
        />

        <ButtonImage
          text='Meus Documentos'
          src='/images/documentos.png'
          route={`/${user?.AccountId}/meu-imovel/documentos`}
          permission={canViewDocumentos}
        />

        <ButtonImage
          text='Manual do Proprietário'
          src='/images/manual.png'
          route={`/${user?.AccountId}/meu-imovel/manual-do-proprietario`}
          permission={canViewManual}
        />
      </div>
    </>
  );
}
