/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

// import { DocumentType, TYPE_GROUP_FILTER } from '@/@types/document';
import { DocumentType } from '@/@types/document';
import { useAppContext } from '@/contexts/AppContext';
import { usePermissions } from '@/hooks/usePermissions';
import { useMemo } from 'react';
import { DocumentSection } from '../components/DocumentSection';
import { TYPE_GROUP_FILTER } from './mock/filter';
import { PermissionsType } from './mock/PermissionsType';

const Divider = () => <hr className='border-t-1 border-white mt-7 mb-4 w-full' />;

export default function MyDocumentsContent() {
  const { user, content } = useAppContext();
  const { getPermission } = usePermissions(content);

  //user
  const userDocuments = useMemo(
    () => (Array.isArray(user?.documents) ? user?.documents : JSON.parse(user?.documents || '[]')),
    [user?.documents]
  );
  //proposal
  const proposalDocuments = useMemo(
    () =>
      Array.isArray(content?.documents)
        ? content?.documents
        : JSON.parse(content?.documents || '[]'),
    [content?.documents]
  );
  //contract
  const contractDocuments = useMemo(
    () =>
      Array.isArray(content?.Contract?.documents)
        ? content?.Contract?.documents
        : JSON.parse(content?.Contract?.documents || '[]'),
    [content?.Contract?.documents]
  );
  const myDocuments = useMemo(
    () => [...userDocuments, ...proposalDocuments, ...contractDocuments],
    [userDocuments, proposalDocuments, contractDocuments]
  );
  const empreendimentoDocuments = useMemo(
    () =>
      Array.isArray(content?.Empreendimento.documents)
        ? content?.Empreendimento.documents
        : JSON.parse(content?.Empreendimento.documents || '[]'),
    [content?.Empreendimento.documents]
  );
  const filteredEmpreendimentoDocuments = useMemo(
    () =>
      empreendimentoDocuments.filter(
        (doc: DocumentType) =>
          doc.Subgrupo__c !== TYPE_GROUP_FILTER &&
          PermissionsType[content?.type as string]?.[doc.Subgrupo__c]
      ),
    [empreendimentoDocuments, content?.type, PermissionsType]
  );

  const manualDocuments = useMemo(
    () =>
      empreendimentoDocuments?.filter(
        (doc: DocumentType) => doc.Subgrupo__c === TYPE_GROUP_FILTER && content?.type === 'morador'
      ),
    [empreendimentoDocuments, content?.type]
  );

  const canViewManual = getPermission(TYPE_GROUP_FILTER);
  const isMorador = content?.type === 'morador';

  return (
    <div className='max-md:mb-10'>
      {isMorador && (
        <>
          <DocumentSection
            title='Meus documentos'
            documents={myDocuments}
            EmpreendimentoId={content?.EmpreendimentoId}
            emptyMessage='Não existem documentos cadastrados.'
          />
          <Divider />
        </>
      )}

      <>
        <DocumentSection
          title='Documentos do empreendimento'
          documents={filteredEmpreendimentoDocuments}
          EmpreendimentoId={content?.EmpreendimentoId}
          emptyMessage='Não existem documentos do empreendimento cadastrados.'
        />
        <Divider />
      </>

      {canViewManual && isMorador && (
        <DocumentSection
          title='Manual do Proprietário'
          documents={manualDocuments}
          EmpreendimentoId={content?.EmpreendimentoId}
          emptyMessage='Não existem manuais do empreendimento cadastrados.'
        />
      )}
    </div>
  );
}
