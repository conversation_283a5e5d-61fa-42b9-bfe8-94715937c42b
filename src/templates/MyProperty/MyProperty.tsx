/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { useMediaQuery } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import MyPropertyDesk from './MyProperty/MyPropertyDesk';
import MyPropertyMobile from './MyProperty/MyPropertyMobile';

export default function MyPropertyScreen() {
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();
  const { content } = useAppContext();


  const conditions = [isMobile === null];
  if (!canBeRender({ conditions })) return null;

  return <>{isMobile ? <MyPropertyMobile /> : <MyPropertyDesk />}</>;
}
