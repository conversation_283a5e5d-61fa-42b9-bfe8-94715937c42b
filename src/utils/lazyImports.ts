import React from 'react';

// Commons Components
export const ServiceWorkerRegistration = React.lazy(
  () => import('@/templates/components/Commons/ServiceWorkerRegistration/index')
);

export const ModalFirstLogin = React.lazy(
  () => import('@/templates/components/Commons/ModalFirstLogin/index')
);

export const ModalCookies = React.lazy(
  () => import('@/templates/TalkToCury/components/ModalCookies')
);

export const ModalRelationshipCenter = React.lazy(
  () => import('@/templates/TalkToCury/components/ModalRelationshipCenter')
);

export const ModalPhotosEnterprise = React.lazy(
  () => import('@/templates/WorkInProgress/components/ModalPhotosEnterprise')
);

export const ModalVideosEnterprise = React.lazy(
  () => import('@/templates/WorkInProgress/components/ModalVideosEnterprise')
);

export const Modal = React.lazy(() => import('@/templates/components/Commons/Modal/index'));

export const CustomLoading = React.lazy(
  () => import('@/templates/components/Commons/Loading/CustomLoading')
);

export const ImpersonationBanner = React.lazy(
  () => import('@/templates/components/Commons/Impersonation/ImpersonationBanner')
);

export const SelectContent = React.lazy(
  () => import('@/templates/components/Commons/SelectContent/index')
);

export const DefaultNoContent = React.lazy(
  () => import('@/templates/components/Commons/DefaultNoContent/DefaultNoContent')
);

// Financial Components
export const IrScreen = React.lazy(
  () =>
    import(
      '@/templates/Financial/NegotiateYourInstallmentsScreen/components/NegotiatedConditions/IrScreen'
    )
);

// Home Components
export const BoletoAtoCard = React.lazy(
  () => import('@/templates/Home/components/ProposalCard/ProposalCard')
);

export const ImageSlider = React.lazy(
  () => import('@/templates/Home/components/ImageSlider/index')
);

export const MostAccessedMenu = React.lazy(
  () => import('@/templates/Home/components/MostAccessedMenu/MostAccessedMenu')
);

export const Sidebar = React.lazy(() => import('@/templates/Home/components/Sidebar/index'));
