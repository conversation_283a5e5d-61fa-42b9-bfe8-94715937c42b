/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export class ValidationUtils {
  static checkCPFvalid(cpfformated: string): boolean {
    if (!cpfformated) return false;
    const cpf = cpfformated.replace(/\D/g, '');
    if (cpf === '00000000000') return false;
    if (cpf.length !== 11) return false;

    let calcCPF = 0;
    let restCalc;

    for (let i = 1; i <= 9; i++) {
      calcCPF += parseInt(cpf.substring(i - 1, i), 10) * (11 - i);
    }

    restCalc = (calcCPF * 10) % 11;
    if (restCalc === 10 || restCalc === 11) restCalc = 0;

    if (restCalc !== parseInt(cpf.substring(9, 10), 10)) return false;

    calcCPF = 0;
    for (let i = 1; i <= 10; i++) {
      calcCPF += parseInt(cpf.substring(i - 1, i), 10) * (12 - i);
    }

    restCalc = (calcCPF * 10) % 11;
    if (restCalc === 10 || restCalc === 11) restCalc = 0;

    return !(restCalc !== parseInt(cpf.substring(10, 11), 10));
  }

  static checkCNPJvalid(cnpjFormatted: string): boolean {
    const cnpj = cnpjFormatted.replace(/\D/g, '');

    if (cnpj === '' || cnpj.length !== 14 || /^(\d)\1{13}$/.test(cnpj)) {
      return false;
    }

    let size = cnpj.length - 2;
    let numbers = cnpj.substring(0, size);
    const digits = cnpj.substring(size);
    let sum = 0;
    let pos = size - 7;

    for (let i = size; i >= 1; i--) {
      sum += parseInt(numbers.charAt(size - i), 10) * pos--;
      if (pos < 2) pos = 9;
    }

    let result = sum % 11 < 2 ? 0 : 11 - (sum % 11);
    if (result !== parseInt(digits.charAt(0), 10)) {
      return false;
    }

    size += 1;
    numbers = cnpj.substring(0, size);
    sum = 0;
    pos = size - 7;

    for (let i = size; i >= 1; i--) {
      sum += parseInt(numbers.charAt(size - i), 10) * pos--;
      if (pos < 2) pos = 9;
    }

    result = sum % 11 < 2 ? 0 : 11 - (sum % 11);

    return result === parseInt(digits.charAt(1), 10);
  }

  static validaShowBoletoAto(type: string, statusBoletoAto: string): boolean {
    return type === 'morador' && statusBoletoAto !== 'Recebimento';
  }

  static validaShowSideBar(type: string, statusBoletoAto: string): boolean {
    return (
      (type === 'morador' && statusBoletoAto === 'Recebimento') ||
      type === 'sindico' ||
      type === 'membro'
    );
  }
}
