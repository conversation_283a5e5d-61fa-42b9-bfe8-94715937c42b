/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export class StringUtils {
  static normalizeText(text: string): string {
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');
  }

  static maskEmail(email: string): string {
    if (!email) return '';
    const [user, domain] = email.split('@');
    const userMasked =
      user.length > 2 ? user.slice(0, 2) + '*'.repeat(user.length - 2) : user + '*';
    return userMasked + '@' + domain;
  }

  static formatString(str: string): string {
    const cleanedStr = str.replace(/[^a-zA-Z0-9_]/g, '');
    const lowercaseStr = cleanedStr.toLowerCase();
    return lowercaseStr.replace(/\s/g, '');
  }

  static cleanCPF(cpf: string): string {
    return cpf.replace(/\D/g, '');
  }

  static cleanData(data: string): string {
    return data.replace('_______', '').replace('_-____', '');
  }

  static FileName(fileName: string): string {
    if (!fileName) return '';

    const commonExtensions = [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'txt',
      'jpg',
      'jpeg',
      'png',
      'gif',
      'zip',
      'rar',
      'mp3',
      'mp4',
      'avi',
      'mov',
      'ppt',
      'pptx',
      'csv',
      'webp'
    ];

    const parts = fileName.split('.');

    if (parts.length > 1 && commonExtensions.includes(parts[parts.length - 1].toLowerCase())) {
      parts.pop();
    }

    return parts.join('.').replace(/_/g, ' ').toUpperCase();
  }

  static FormatPhoneNumber(phoneNumber: string): string | null {
    if (phoneNumber) {
      const cleanNumber = phoneNumber.replace(/[^0-9]/g, '');
      return '+55' + cleanNumber;
    }
    return null;
  }

  static ParselStringToArray(
    contentString: string | string[] | undefined,
    defaultString: string = ''
  ): string[] {
    try {
      if (!contentString) {
        return [defaultString];
      }

      if (Array.isArray(contentString)) {
        return contentString.length > 0 ? contentString : [defaultString];
      }

      const tryParse = (content: any): string[] => {
        if (Array.isArray(content) && content.length > 0) {
          return content;
        }

        if (typeof content !== 'string') {
          return [defaultString];
        }

        try {
          const parsed = JSON.parse(content);
          return tryParse(parsed);
        } catch (e) {
          return [content];
        }
      };

      return tryParse(contentString);
    } catch (error) {
      console.error('Erro ao analisar string:', error);
      return [defaultString];
    }
  }
}
