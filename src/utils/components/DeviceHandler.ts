/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { BrowserUtils } from '../BrowserUtils';

export interface DeviceHandler {
  triggerDownload(file: Blob | string | null, filename: string, finalFileMobile?: string | undefined): void;
  type: string;
}

export class MobileDeviceHandler implements DeviceHandler {
  type: string = 'mobile';
  triggerDownload(file: Blob | string | null, filename: string, finalFileMobile?: string | undefined): void {
    BrowserUtils.ActionReactNative({
      type: 'downloadFromAPI',
      data: { path: finalFileMobile, namefile: filename }
    });

  }
}


export class DesktopDeviceHandler implements DeviceHandler {
  type: string = 'desktop';
  triggerDownload(file: Blob | string, filename: string): void {
    const downloadUrl = file instanceof Blob ? window.URL.createObjectURL(file) : file;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    link.remove();
    if (downloadUrl) {
      window.URL.revokeObjectURL(downloadUrl);
    }

  }
}