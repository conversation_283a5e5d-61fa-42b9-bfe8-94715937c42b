/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { DownloadProgress } from '@/@types/download';

export class ProgressTracker {
  private progress: number = 0;
  private intervalId?: NodeJS.Timeout;

  constructor(
    private readonly updateUI: (progress: DownloadProgress) => void,
    private readonly maxProgress: number = 100,
    private readonly increment: number = 0.25,
    private readonly interval: number = 30
  ) {}

  start(): void {
    this.intervalId = setInterval(() => {
      if (this.progress < this.maxProgress) {
        this.progress += this.increment;
        this.updateProgress();
      }
    }, this.interval);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  updateProgress(customProgress?: number): void {
    const currentProgress = customProgress ?? this.progress;

    // Determina a mensagem baseada no progresso
    const message = this.getProgressMessage(currentProgress);

    this.updateUI({
      text: message,
      params: {
        progress: currentProgress,
        callback: () => {}
      }
    });

    if (currentProgress >= 99) {
      this.updateUI({
        text: this.getProgressMessage(currentProgress),
        params: {
          progress: 100,
          callback: () => {}
        }
      });
    }
  }

  private getProgressMessage(progress: number): string {
    if (progress < 20) {
      return `Iniciando download...<br>Progresso: ${progress.toFixed(1)}%`;
    } else if (progress < 40) {
      return `Verificando documento...<br>Progresso: ${progress.toFixed(1)}%`;
    } else if (progress < 50) {
      return `Preparando arquivo...<br>Progresso: ${progress.toFixed(1)}%`;
    } else if (progress < 85) {
      return `Validando arquivo...<br>Progresso: ${progress.toFixed(1)}%`;
    } else if (progress < 90) {
      return `Baixando arquivo...<br>Progresso: ${progress.toFixed(1)}%`;
    } else if (progress < 95) {
      return `Finalizando download...<br>Progresso: ${progress.toFixed(1)}%`;
    }
    return `Download concluído!`;
  }
}
