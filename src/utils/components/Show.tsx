import * as React from 'react';

interface ShowProps<T> {
  when: T | undefined | null | false;
  fallback?: React.ReactNode;
  children: React.ReactNode | ((item: T) => React.ReactNode);
}

const Show = <T,>({ when, fallback = null, children }: ShowProps<T>): React.ReactElement | null => {
  if (typeof children === 'function' && when) {
    return <>{children(when)}</>;
  }
  return <>{when ? children : fallback}</>;
};

interface AsyncShowProps<T> {
  when: Promise<T> | T;
  fallback?: React.ReactNode;
  children: React.ReactNode | ((item: T) => React.ReactNode);
}

const AsyncShow = <T,>({
  when,
  fallback = null,
  children
}: AsyncShowProps<T>): React.ReactElement | null => {
  const [isLoading, setIsLoading] = React.useState(true);
  const [data, setData] = React.useState<T | null>(null);

  React.useEffect(() => {
    Promise.resolve(when).then((result) => {
      setData(result);
      setIsLoading(false);
    });
  }, [when]);

  if (isLoading) return <>{fallback}</>;

  if (typeof children === 'function' && data) {
    return <>{children(data)}</>;
  }

  return <>{data ? children : null}</>;
};

export { AsyncShow, Show };
