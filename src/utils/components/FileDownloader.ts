/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 */

import type { ProgressTracker } from './ProgressTracker';

interface PDFValidationResult {
  isValid: boolean;
  reason?: string;
  fileType?: string;
  details?: {
    fileSize: number;
    detectedFormat: string;
    hasValidHeader: boolean;
    hasValidStructure: boolean;
    hasValidFooter: boolean;
  };
}

export class FileDownloader {
  constructor(private readonly progressTracker: ProgressTracker) { }

  async downloadBlob(url: string, filename: string): Promise<{ blob: Blob | null, fileValidated: boolean }> {
    console.log('downloadBlob', url, filename);
    if (!url) {
      return {
        blob: null,
        fileValidated: true
      };
    }
    const response = await fetch(
      `/services/download?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(filename)}`
    );

    if (!response.ok) throw new Error('Failed to download file');

    const totalBytes = Number.parseInt(response.headers.get('Content-Length') || '0', 10);
    const reader = response.body?.getReader();
    if (!reader) throw new Error('Unable to read response');

    const chunks: Uint8Array[] = [];
    let loadedBytes = 0;
    let loading = true;

    while (loading) {
      const { done, value } = await reader.read();
      if (done) {
        loading = false;
        break;
      }

      chunks.push(value);
      loadedBytes += value.length;

      const downloadProgress = totalBytes > 0
        ? Math.round((loadedBytes / totalBytes) * 100)
        : 0;

      const mappedProgress = 50 + (downloadProgress * 0.35);
      this.progressTracker.updateProgress(Math.min(85, mappedProgress));
    }

    const concatenatedChunks = new Uint8Array(loadedBytes);
    let offset = 0;
    for (const chunk of chunks) {
      concatenatedChunks.set(chunk, offset);
      offset += chunk.length;
    }

    const blob = new Blob([concatenatedChunks]);

    // Validação se é PDF (85% - 90%)
    if (this.isPDFFile(filename)) {
      this.progressTracker.updateProgress(85);
      await this.validateDownloadedFile(blob, filename);
      this.progressTracker.updateProgress(90);
    }

    return { blob, fileValidated: true };
  }

  private isPDFFile(filename: string): boolean {
    return filename.toLowerCase().endsWith('.pdf');
  }

  private async validateDownloadedFile(blob: Blob, filename: string): Promise<void> {
    console.log(`[VALIDATION] Iniciando validação para: ${filename} (Tamanho: ${blob.size} bytes)`);

    try {
      // Primeiro: detectar o tipo real do arquivo
      const fileType = await this.detectFileType(blob);
      console.log(`[VALIDATION] Tipo detectado: ${fileType.format} (Confiança: ${fileType.confidence})`);

      // Se esperamos PDF mas recebemos outro formato
      if (this.isPDFFile(filename) && fileType.format !== 'PDF') {
        throw new Error(
          `Arquivo baixado não é um PDF válido. ` +
          `Esperado: PDF, Recebido: ${fileType.format}. ` +
          `Isso pode indicar um erro no servidor ou arquivo corrompido.`
        );
      }

      // Se é PDF, fazer validação específica
      if (fileType.format === 'PDF') {
        const validation = await this.performPDFValidation(blob);

        if (!validation.isValid) {
          throw new Error(`PDF corrompido: ${validation.reason}`);
        }

        console.log(`✅ PDF válido: ${filename}`, validation.details);
      } else {
        console.log(`✅ Arquivo baixado: ${filename} (${fileType.format})`);
      }

    } catch (error) {
      console.error(`💥 Erro na validação: ${filename}`, error);
      throw new Error(`Falha na validação do arquivo: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  private async detectFileType(blob: Blob): Promise<{ format: string, confidence: string }> {
    try {
      console.log('[VALIDATION] Lendo header do arquivo...');
      // Ler os primeiros bytes para detectar o tipo
      const headerSlice = blob.slice(0, 20);
      console.log(`[VALIDATION] Slice do header criado (tamanho: ${headerSlice.size}). Convertendo para ArrayBuffer...`);
      const headerBuffer = await headerSlice.arrayBuffer();
      console.log(`[VALIDATION] ArrayBuffer do header criado (bytes: ${headerBuffer.byteLength}).`);
      const headerBytes = new Uint8Array(headerBuffer);

      // Converter para string preservando bytes
      const headerText = String.fromCharCode(...headerBytes);
      const headerHex = Array.from(headerBytes)
        .map(byte => byte.toString(16).padStart(2, '0'))
        .join(' ');

      console.log(`[VALIDATION] Header detectado: "${headerText.substring(0, 10)}"`);
      console.log(`[VALIDATION] Header hex: ${headerHex.substring(0, 30)}...`);

      // Detectar formatos comuns
      if (headerText.startsWith('%PDF-')) {
        return { format: 'PDF', confidence: 'alta' };
      }

      if (headerText.startsWith('RIFF')) {
        // RIFF pode ser WAV, AVI, WebP, etc.
        const riffType = headerText.substring(8, 12);
        return { format: `RIFF (${riffType})`, confidence: 'alta' };
      }

      if (headerBytes[0] === 0xFF && headerBytes[1] === 0xD8) {
        return { format: 'JPEG', confidence: 'alta' };
      }

      if (headerText.startsWith('\x89PNG')) {
        return { format: 'PNG', confidence: 'alta' };
      }

      if (headerText.startsWith('GIF87a') || headerText.startsWith('GIF89a')) {
        return { format: 'GIF', confidence: 'alta' };
      }

      if (headerBytes[0] === 0x50 && headerBytes[1] === 0x4B) {
        return { format: 'ZIP/Office', confidence: 'alta' };
      }

      // Verificar se parece com HTML/JSON (erro do servidor)
      const textSample = headerText.substring(0, 100).toLowerCase();
      if (textSample.includes('<html') || textSample.includes('<!doctype')) {
        return { format: 'HTML (erro do servidor)', confidence: 'alta' };
      }

      if (textSample.trim().startsWith('{') || textSample.trim().startsWith('[')) {
        return { format: 'JSON (erro do servidor)', confidence: 'alta' };
      }

      console.log('[VALIDATION] Nenhum tipo de arquivo conhecido detectado.');
      return { format: 'Desconhecido', confidence: 'baixa' };

    } catch (error) {
      console.error('[VALIDATION] Erro ao detectar tipo de arquivo:', error);
      return { format: 'Erro na detecção', confidence: 'baixa' };
    }
  }
  private async performPDFValidation(blob: Blob): Promise<PDFValidationResult> {
    const result: PDFValidationResult = {
      isValid: false,
      details: {
        fileSize: blob.size,
        detectedFormat: 'PDF',
        hasValidHeader: false,
        hasValidStructure: false,
        hasValidFooter: false
      }
    };

    try {
      // Verificar tamanho mínimo
      if (blob.size < 200) {
        return {
          isValid: false,
          reason: `Arquivo muito pequeno (${blob.size} bytes)`,
          details: result.details
        };
      }

      // Verificar header PDF
      const headerValidation = await this.validatePDFHeader(blob);
      result.details!.hasValidHeader = headerValidation.isValid;

      if (!headerValidation.isValid) {
        return {
          isValid: false,
          reason: headerValidation.reason,
          details: result.details
        };
      }

      // Verificar estrutura básica
      const structureValidation = await this.validatePDFStructure(blob);
      result.details!.hasValidStructure = structureValidation.isValid;

      // Verificar footer
      const footerValidation = await this.validatePDFFooter(blob);
      result.details!.hasValidFooter = footerValidation.isValid;

      // PDF válido se tem header correto e pelo menos alguma estrutura
      result.isValid = result.details!.hasValidHeader &&
        (result.details!.hasValidStructure || result.details!.hasValidFooter);
        (result.details!.hasValidStructure || result.details!.hasValidFooter);

      if (!result.isValid) {
        result.reason = 'PDF sem estrutura válida detectada';
      }

      return result;

    } catch (error) {
      return {
        isValid: false,
        reason: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
        details: result.details
      };
    }
  }

  private async validatePDFHeader(blob: Blob): Promise<{ isValid: boolean, reason?: string }> {
    try {
      const headerSlice = blob.slice(0, 20);
      const headerBuffer = await headerSlice.arrayBuffer();
      const headerBytes = new Uint8Array(headerBuffer);
      const headerText = String.fromCharCode(...headerBytes.slice(0, 8));

      if (!headerText.startsWith('%PDF-')) {
        return {
          isValid: false,
          reason: `Header inválido: esperado "%PDF-", encontrado "${headerText}"`
        };
      }

      return { isValid: true };

    } catch (error) {
      return {
        isValid: false,
        reason: `Erro ao ler header: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }

  private async validatePDFStructure(blob: Blob): Promise<{ isValid: boolean, reason?: string }> {
    try {
      const sampleSize = Math.min(2000, blob.size);
      const sampleSlice = blob.slice(0, sampleSize);
      const sampleBuffer = await sampleSlice.arrayBuffer();
      const sampleBytes = new Uint8Array(sampleBuffer);
      const sampleText = String.fromCharCode(...sampleBytes);

      const hasObj = sampleText.includes(' obj') || sampleText.includes('\nobj');
      const hasType = sampleText.includes('/Type');

      if (!hasObj && !hasType) {
        return {
          isValid: false,
          reason: 'Estruturas PDF não encontradas'
        };
      }

      return { isValid: true };

    } catch (error) {
      return {
        isValid: false,
        reason: `Erro ao verificar estrutura: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }

  private async validatePDFFooter(blob: Blob): Promise<{ isValid: boolean, reason?: string }> {
    try {
      const footerSize = Math.min(500, blob.size);
      const footerSlice = blob.slice(-footerSize);
      const footerBuffer = await footerSlice.arrayBuffer();
      const footerBytes = new Uint8Array(footerBuffer);
      const footerText = String.fromCharCode(...footerBytes);

      const hasEOF = footerText.includes('%%EOF');

      if (!hasEOF) {
        return {
          isValid: false,
          reason: 'Marcador %%EOF não encontrado'
        };
      }

      return { isValid: true };

    } catch (error) {
      return {
        isValid: false,
        reason: `Erro ao verificar footer: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }
}
