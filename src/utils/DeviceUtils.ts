/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import { parseCookies } from 'nookies';
import { UAParser } from 'ua-parser-js';

export class DeviceUtils {
  static isLoginAdmin(): string | null {
    const cookie = parseCookies();
    if (cookie === undefined) {
      return null;
    }
    if (!cookie['device_info']) {
      return null;
    }
    if (cookie['device_info'].startsWith('admin-')) {
      return JSON.parse(cookie['device_info']) as string;
      // return cookie['device_info'];
    }
    return null;
  }

  static deviceInfo(): string {
    const parser = new UAParser();
    const result = parser.getResult();

    const isMobileApp = result.device.type === 'mobile' || result.device.type === 'tablet';

    let deviceinfoReturn = this.isLoginAdmin();

    if (deviceinfoReturn) return deviceinfoReturn;

    if (isMobileApp) {
      deviceinfoReturn = `${result.os.name}-${result.device.type}-${result.device.vendor}-${result.device.model}-${result.ua}`;
    } else {
      deviceinfoReturn = `${result.browser.name}-desktop-${result.device.vendor}-${result.ua}`;
    }
    return deviceinfoReturn;
  }

  static isIphone(): boolean {
    const userAgent = navigator.userAgent.toLowerCase();
    return ['ipad', 'iPad', 'iphone', 'iPhone', 'Mac', 'mac', 'iPod', 'ipod'].some((device) =>
      userAgent.includes(device)
    );
  }

  static isPWA(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }
    return (
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone ||
      document.referrer.includes('android-app://')
    );
  }

  static isMobileApp(): boolean {
    const parser = new UAParser();
    const result = parser.getResult();
    const isMobileDevice = result.device.type === 'mobile' || result.device.type === 'tablet';
    return isMobileDevice;//&& this.isPWA();
  }
}
