/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export class MiscUtils {
  static defineTextUnidade(type: string, unidadeAtivoName: string): string {
    switch (type) {
    case 'morador':
      return `Unidade: ${unidadeAtivoName}`;
    case 'sindico':
      return 'Síndico';
    case 'membro':
      return 'Membro de patrimônio de afetação';
    default:
      return '';
    }
  }

  static calculateCode(codEmpreendimentoId: string): string {
    const dataNow = new Date();
    const mes = dataNow.getMonth() + 1;
    const dia = dataNow.getDate();
    // const empreendimento_id = parseInt(codEmpreendimentoId, 2);
    const empreendimento_id = Number(codEmpreendimentoId);
    const verific1 = 4 * empreendimento_id + 2 * mes + 4 * dia;
    const verific2 = Math.abs(1 * empreendimento_id + 5 * dia - 6 * mes);
    const verific3 = Math.abs(6 * empreendimento_id + 5 * mes - 4 * dia);
    const verific4 = Math.abs(2 * empreendimento_id - 7 * dia + 4 * mes);

    return '' + verific1 + mes + verific2 + verific3 + verific4 + dia;
  }

  static typeNegotiation(type: string, action: string): boolean {
    return (
      type === 'adimplente' || (type === 'inadimplente' && action === 'NegociacaoParcialSimulacao')
    );
  }

  static svgToBase64(svgString: string): Promise<string> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (ctx) ctx.drawImage(img, 0, 0);
        resolve(canvas.toDataURL('image/png'));
      };
      img.src = 'data:image/svg+xml;base64,' + btoa(svgString);
    });
  }

  static converterLinkYouTube(linkOriginal: string): string {
    const idVideo = linkOriginal;
    return `https://www.youtube.com/embed/${idVideo}`;
  }

  static getContractType(document: string): string {
    if (!document) return '';
    const contractTypes: { [key: string]: string } = {
      ADIA: 'Adiamento',
      BOL: 'Despesas',
      COM: 'Despesas',
      CT: 'Contrato',
      NFE: 'Despesas',
      REC: 'Despesas',
      AJ: 'Acordo Judicial',
      DE: 'Despesas',
      DP: 'Despesas',
      DR: 'Despesas',
      AR: 'Despesas',
      TX: 'Taxa',
      TO: 'Taxa de obra',
      COR: 'Comissao'
    };
    const contractTypeCode = document.split('.')[0];
    const contractName = contractTypes[contractTypeCode];
    return contractName || 'Tipo de contrato desconhecido';
  }
}
