/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
export class BrowserUtils {
  static copyToClipboard(text: string): void {
    if (this.isMobileApp() && window.ReactNativeWebView) {
      const postData = {
        type: 'copyText',
        data: { text }
      };

      this.ActionReactNative(postData);
    } else {
      navigator.clipboard
        .writeText(text)
        .then()
        .catch((err) => {
          console.error('Algo deu errado ao copiar', err);
        });
    }
  }

  static ActionReactNative(
    postData: Record<string, string | number | boolean | object> | null
  ): void {

    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(JSON.stringify(postData));
    } else {
      if ((postData?.type === 'openUrlBrowser' || postData?.type === 'downloadFromAPI') && postData.url) {
        window.open(postData.url as string, '_blank');
        return;
      }
      console.info('ReactNativeWebView não está disponível');
    }
  }

  static handleBodyScroll(show: boolean): void {
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    if (!show) {
      const { scrollY } = window;

      document.body.style.overflow = 'hidden';
      document.body.dataset.scrollY = String(scrollY);
      this.smoothScrollTo(0, 0);
    } else {
      document.body.style.overflow = '';
      const scrollY = parseInt(document.body.dataset.scrollY || '0', 10);
      this.smoothScrollTo(window.scrollX, scrollY);
    }
  }

  private static smoothScrollTo(x: number, y: number): void {
    if (typeof window === 'undefined') {
      return;
    }

    const startY = window.scrollY;
    const distanceY = y - startY;
    const startTime = performance.now();
    const duration = 300;

    function scrollStep(currentTime: number) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      window.scrollTo(x, startY + distanceY * BrowserUtils.easeInOutQuad(progress));

      if (progress < 1) {
        requestAnimationFrame(scrollStep);
      }
    }

    requestAnimationFrame(scrollStep);
  }

  private static easeInOutQuad(t: number): number {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  static isMobileApp(): boolean {
    const userAgent = navigator.userAgent || navigator.vendor || (window as Window).opera;
    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
      userAgent.toLowerCase()
    );
  }

  static getSizeWindow(): { maxWidth: number; maxHeight: number } {
    if (typeof window === 'undefined' || window === null)
      return {
        maxWidth: 0,
        maxHeight: 0
      };
    const { innerWidth, innerHeight } = window;

    return {
      maxWidth: innerWidth,
      maxHeight: innerHeight
    };
  }
}
