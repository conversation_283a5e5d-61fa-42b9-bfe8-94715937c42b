import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

export default function LazyImage({
  src,
  alt,
  width,
  height,
  className,
  style,
  quality = 75,
  ...props
}: {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  style?: React.CSSProperties;
  quality?: number;
  [key: string]: any;
}) {
  const imgRef = useRef<HTMLImageElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!imgRef.current) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = src;
          img.onload = () => setIsLoaded(true);
          observer.disconnect();
        }
      });
    });

    observer.observe(imgRef.current);

    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [src]);

  return (
    <div className={`image-container ${isLoaded ? 'loaded' : ''}`} style={{ width, height }}>
      <Image
        ref={imgRef}
        alt={alt}
        width={width}
        height={height}
        src={src}
        onLoad={() => setIsLoaded(true)}
        className={className}
        style={style}
        loading='lazy'
        quality={quality}
        {...props}
      />
    </div>
  );
}
