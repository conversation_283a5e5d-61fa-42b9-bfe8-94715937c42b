/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { InstallmentItem } from '@/@types/installments';
import { ChangeEvent } from 'react';

export class FinanceUtils {
  static MoneyFormat = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  });

  static onChangeValueMoney(
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string | number
  ): string {
    let value: string;

    if (typeof event === 'object' && 'target' in event) {
      value = event.target.value.replace(/\D/g, '');
    } else if (typeof event === 'string') {
      value = event.replace(/\D/g, '');
    } else if (typeof event === 'number') {
      value = event.toString();
    } else {
      value = '0';
    }

    const numericValue = Number(value) / 100;
    return this.MoneyFormat.format(numericValue);
  }

  static checkIfInstallmentsCanBeAntecipate(installment: InstallmentItem): boolean {
    return (
      installment.paymentTerms.descrition !== 'Desconto de Negociação' &&
      installment.paymentTerms.descrition !== 'Desconto de Antecipação' &&
      installment.paymentTerms.id !== 'DN' &&
      installment.receipts[0].days === 0 &&
      installment.receipts[0]?.type !== 'Adiantamento' &&
      installment.receipts[0]?.type !== 'Reparcelamento' &&
      installment.receipts[0]?.type !== 'Recebimento' &&
      installment.receipts[0]?.type !== 'Substituição' &&
      installment.paymentTerms?.id !== 'FN' &&
      installment.paymentTerms?.id !== 'FI' &&
      installment.installmentSituation !== '2'
    );
  }
}
