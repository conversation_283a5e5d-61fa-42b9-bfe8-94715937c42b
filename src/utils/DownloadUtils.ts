/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import type { DownloadProgress } from '@/@types/download';
import { DesktopDeviceHandler, MobileDeviceHandler } from './components/DeviceHandler';
import { FileDownloader } from './components/FileDownloader';
import { ProgressTracker } from './components/ProgressTracker';
import { DeviceUtils } from './DeviceUtils';
import { DownloadService } from './services/DownloadService';
export async function downloadFile(
  src: string,
  name: string,
  toggleModal: (progress: DownloadProgress) => void,
  objId?: string
): Promise<void> {
  const progressTracker = new ProgressTracker(toggleModal);
  const fileDownloader = new FileDownloader(progressTracker);
  const deviceHandler =
    DeviceUtils.isMobileApp() && window.ReactNativeWebView
      ? new MobileDeviceHandler()
      : new DesktopDeviceHandler();

  
  const downloadService = new DownloadService(
    fileDownloader,
    deviceHandler,
    progressTracker
  );
  
  await downloadService.download({ src, name, objId });
}
