/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

interface YearListType {
  id: number;
  value: number;
  name: string;
}
export class DateUtils {
  static formatDate(dateToFormat: string): string {
    if (!dateToFormat) return '';
    const parts = dateToFormat.split('-');
    const year = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1;
    const day = parseInt(parts[2], 10);

    const data = new Date(year, month, day);
    const dia = data.getDate().toString().padStart(2, '0');
    const mes = (data.getMonth() + 1).toString().padStart(2, '0');
    const ano = data.getFullYear();
    return `${dia}/${mes}/${ano}`;
  }

  static formatDateAndTime(startDate: string, endDate: string): string {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const day = start.getDate().toString().padStart(2, '0');
    const month = (start.getMonth() + 1).toString().padStart(2, '0');
    const year = start.getFullYear();

    const startHours = start.getHours().toString().padStart(2, '0');
    const startMinutes = start.getMinutes().toString().padStart(2, '0');
    const endHours = end.getHours().toString().padStart(2, '0');
    const endMinutes = end.getMinutes().toString().padStart(2, '0');

    return `${day}/${month}/${year} horário de ${startHours}h${startMinutes} a ${endHours}h${endMinutes}`;
  }

  static compareDates(date1: string, date2: string): boolean {
    if (date1 === null || date1 === undefined || date2 === null || date2 === undefined)
      return false;
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    return d1 > d2;
  }

  static getTodayDate() {
    const today = new Date();
    const day = today.getDate();
    const month = today.getMonth() + 1 < 10 ? `0${today.getMonth() + 1}` : today.getMonth() + 1;
    const year = today.getFullYear();

    return {
      day,
      month,
      year,
      todayDate: `${year}-${month}-${day}`,
      fullDate: today
    };
  }

  static createYearList = (createdDate: string): YearListType[] => {
    const startYear = new Date(createdDate).getFullYear();
    const currentYear = new Date().getFullYear();
    const yearList = [];
    let idYear = 0;

    for (let year = currentYear - 2; year >= startYear; year--) {
      yearList.push({
        id: idYear,
        value: year,
        name: year.toString()
      } as YearListType);
      idYear++;
    }

    return yearList;
  };

  static getMonthName(monthInput: number | string): string {
    const months = [
      'Janeiro',
      'Fevereiro',
      'Março',
      'Abril',
      'Maio',
      'Junho',
      'Julho',
      'Agosto',
      'Setembro',
      'Outubro',
      'Novembro',
      'Dezembro'
    ];

    const monthNumber = typeof monthInput === 'string' ? parseInt(monthInput, 10) : monthInput;
    if (monthNumber < 1 || monthNumber > 12) {
      return 'Número do mês inválido';
    }
    return months[monthNumber - 1];
  }

  static getDay(dateString: string) {
    if (!dateString) return '';
    const abbreviatedDaysOfWeek = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    const parts = dateString.split('-');
    const year = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1;
    const day = parseInt(parts[2], 10);
    const date = new Date(year, month, day);
    const dayOfWeek = date.getDay();
    const dayOfMonth = date.getDate().toString().padStart(2, '0');
    return {
      name: abbreviatedDaysOfWeek[dayOfWeek],
      day: dayOfMonth
    };
  }

  static formatDateToScheduling(dateString: string): string {
    if (!dateString || dateString === '') return '';

    const [dateStr, timeStr] = dateString.split(' - ');
    const [day, month, year] = dateStr.split('/');

    const date = new Date(
      Number(year),
      Number(month) - 1,
      Number(day),
      Number(timeStr.substring(0, 2)),
      Number(timeStr.substring(3, 5))
    );
    return (
      date
        .toLocaleString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
          // timeZone: 'America/Sao_Paulo'
          // timeZone: 'America'
        })
        .replace(/(\d+)\/(\d+)\/(\d+),/, '$3-$1-$2T') + '.000Z'
    ).replace(' ', '');
  }
}
