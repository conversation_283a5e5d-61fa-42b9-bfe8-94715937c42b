# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
.vscode/
.local/
# dependencies
/node_modules
/.pnp
.pnp.js
.local
# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
OLD_eslintrc.__js
.env.local
docs.md
BKP---.env
# Sentry Config File
.env.sentry-build-plugin

bkp-*
*/bkp-*
BKP-*
*/BKP-*
.qodo

/src/BuildId.tsx
# Million Lint
.million
src/BuildId.tsx
./src/BuildId.tsx

.scripts/update-front-3.sh
.scripts/update-front-2.sh
/.scripts/update-front-3.sh
/.scripts/update-front-2.sh

.local
.cursorignore