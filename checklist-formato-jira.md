* [open] Usuário com boleto ato pago
* [open] Usuário com boleto ato em aberto
* [open] Testar síndico
* [open] Testar usuário com muitos ativos
* [open] Testar usuários com situações diferentes (financiado, quitado, etc)
* [open] Testar desktop
* [open] Testar web mobile
* [open] Testar App
* [open] Verificar se o usuário possui os dados corretos no Sienge
* [open] /login - Verificar animações de entrada da página
* [open] /verifica-codigo - Testar animações de transição
* [open] /esqueci-minha-senha - Verificar fetch de recuperação
* [open] /nova-senha - Testar animações e submit
* [open] /primeiro-acesso - Verificar fluxo completo
* [open] /senha-definitiva - Testar animações de confirmação
* [open] Login com credenciais válidas
* [open] Login com credenciais inválidas
* [open] Recuperação de senha
* [open] Verificação de código
* [open] Definição de nova senha
* [open] Verificar se aparecerá boleto ato
* [open] Testar troca de ativos
* [open] Carregamento inicial com verificação de boleto ato
* [open] Verificar no Sienge quantas chamadas foram feitas
* [open] Verificar se a planta está aparecendo
* [open] Verificar se é possível baixar a planta
* [open] Verificar se é possível baixar um documento
* [open] Verificar se o painel de fotos está carregando
* [open] Verificar se o zoom está funcionando
* [open] Verificar se o vídeo está funcionando
* [open] Verificar data da última atualização
* [open] Verificar se a data atualiza quando existe alguma mudança no Salesforce
* [open] Verificar se os dados do andamento estão aparecendo
* [open] Verificar se os dados estão atualizando quando muda no Salesforce

## 5. FINANCEIRO

### 5.1 2ª Via de Boleto
* [open] Buscar boletos disponíveis
* [open] Gerar 2ª via de boleto
* [open] Download de PDF do boleto
* [open] Copiar código de barras

### 5.2 Relatório de Extrato
* [open] Verificar se carregou corretamente
* [open] Verificar se baixa o PDF
* [open] Verificar se envia por email
* [open] Verificar se abre a parcela com as informações corretas

### 5.3 Negocie Suas Parcelas
* [open] Testar negociação - Seleção no menu de opções
* [open] Verificar se os dados e cálculos do resumo estão corretos
* [open] Verificar se houve a negociação no Sienge
* [open] Antecipação - Seleção de parcelas
* [open] Valor com desconto
* [open] Verificar se houve antecipação no Sienge

### 5.4 Informes
* [open] Verificar no Sienge se o usuário tem informes disponíveis
* [open] Verificar se é possível baixar o Informe
* [open] Verificar se está enviado por email

### 5.5 Cury Chega Mais
* [open] Verificar se o usuário pode aderir ao programa
* [open] Verificar se as imagens estão corretas
* [open] Verificar se o usuário consegue aderir
* [open] Verificar que se o usuário já fizer parte, não aparecer a opção de aderir

## 6. DICAS
* [open] Verificar se o conteúdo está abrindo

## 7. DÚVIDAS FREQUENTES
* [open] Verificar se o conteúdo está abrindo

## 8. SOLICITAR ATENDIMENTO
* [open] Verificar se está abrindo um chamado e levando para a página de acompanhamento

## 9. ACOMPANHAR ATENDIMENTO
* [open] Verificar os chamados estão atualizados com o do Salesforce
* [open] Verificar que está abrindo a página de detalhes do chamado

## 10. MEUS DADOS
* [open] Testar atualização dos dados

## 11. ONDE ESTOU LOGADO
* [open] Testar Logout

---
**Total de itens:** 52
**Status:** Pendente
**Criado por:** Augment Code
**Data:** 2025-08-07
**Jira Issue:** CCM-364
