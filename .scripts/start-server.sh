!/bin/bash

# Mover o script para /usr/local/bin
# sudo cp /var/www/html/front/.scripts/start-server.sh /usr/local/bin/start-server
# sudo chmod +x /usr/local/bin/start-server
# start-server - para rodar o script

# Entrar no diretório do projeto
cd /var/www/html/front

# Executar o build
npm run build

# Matar o processo atual na porta 3000, se existir
sudo fuser -k 3000/tcp

# Verificar se o processo está rodando
if pm2 list | grep -q ".scripts/ecosystem.config.js"; then
    echo "Reiniciando o servidor com PM2..."
    pm2 restart .scripts/ecosystem.config.js
else
    echo "Iniciando o servidor com PM2..."
    pm2 start .scripts/ecosystem.config.js
fi

# Reiniciar o Nginx
sudo systemctl restart nginx

echo "Processo concluído."