#!/bin/bash

# Mover o script de atualização para /usr/local/bin
# sudo cp /var/www/html/front/.scripts/update-front.sh /usr/local/bin/update-front
# sudo chmod +x /usr/local/bin/update-front
# update-front - para rodar o script


# Entrar no diretório do projeto
cd /var/www/html/front

# Atualizar o repositório
# git stash
# git pull

git fetch
git reset --hard @{u}

# Reconstruir o projeto
rm -rf node_modules .next
npm install
npm run build
sudo fuser -k 3000/tcp
pm2 delete all
pm2 start .scripts/ecosystem.config.cjs