import fs from 'fs';
import path from 'path';

const headerContent = fs.readFileSync('./.scripts/fileheader.txt', 'utf-8');
const srcPath = './src';

function addHeaderToFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  if (!content.startsWith(headerContent)) {
    const updatedContent = headerContent + '\n' + content;
    fs.writeFileSync(filePath, updatedContent, 'utf-8');
    console.log(`Cabeçalho adicionado a: ${filePath}`);
  }
}

function traverseDirectory(directory) {
  fs.readdirSync(directory, { withFileTypes: true }).forEach((dirent) => {
    const resolvedPath = path.resolve(directory, dirent.name);
    if (dirent.isDirectory()) {
      traverseDirectory(resolvedPath);
    } else if (dirent.isFile() && (resolvedPath.endsWith('.ts') || resolvedPath.endsWith('.tsx'))) {
      addHeaderToFile(resolvedPath);
    }
  });
}

traverseDirectory(srcPath);
