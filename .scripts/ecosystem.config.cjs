/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 *
 * pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 5
pm2 set pm2-logrotate:compress true

 *
 */
module.exports = {
  apps: [
    {
      name: 'cury_cliente',
      script: 'node_modules/.bin/next',
      args: 'start',
      env: {
        NODE_ENV: process.env.ENV || 'homolog'
      },
      watch: false,
      exec_mode: 'cluster',
      instances: 'max',
      max_memory_restart: '1000M',
      log_date_format: 'YYYY-MM-DD HH:mm Z',
      error_file: 'logs/cury_cliente_error.log',
      out_file: 'logs/cury_cliente_out.log',
      max_size: '10M',
      max_files: 2,
      merge_logs: true,
      autorestart: true,
      restart_delay: 30000
    }
  ]
};
