#!/bin/bash

BR<PERSON>CH_NAME=$(echo $1 | sed 's/\//_/g')
DEPLOY_DIR="$ROOT_FOLDER/$BRANCH_NAME"

mkdir -p $DEPLOY_DIR
cd $DEPLOY_DIR
git init
git remote <NAME_EMAIL>:studiowox/cury_app_cliente_web.git
git pull origin $1

npm install
npm run build

sudo bash -c "cat > /etc/nginx/sites-available/$BRANCH_NAME <<EOL
server {
    listen 443 ssl;
    server_name ${BRANCH_NAME}.homolog.cliente.cury.net;

    location / {
        root $DEPLOY_DIR;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOL"

sudo ln -s /etc/nginx/sites-available/$BRANCH_NAME /etc/nginx/sites-enabled/
sudo systemctl reload nginx

pm2 start npm --name "$BRANCH_NAME" -- start

# ssh -i <(echo "$SSH_KEY" | sed 's/\\n/\n/g') $SSH_USER@$EC2_IP "
#     sudo bash -c 'cat > /etc/nginx/sites-available/$BRANCH_NAME.conf <<EOF
# server {
#     listen 80;
#     server_name $BRANCH_NAME.homolog.cliente.cury.net;
#     root /var/www/html/$BRANCH_NAME;
#     index index.html index.htm;

#     location / {
#         try_files \$uri \$uri/ /index.html;
#     }
# }
# EOF'
#     sudo ln -s /etc/nginx/sites-available/$BRANCH_NAME.conf /etc/nginx/sites-enabled/
#     sudo nginx -s reload
# "