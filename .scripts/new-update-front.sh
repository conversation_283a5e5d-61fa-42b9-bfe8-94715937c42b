#!/bin/bash

# Script de atualização para Next.js com Docker (Zero Downtime)
# Salve como: /usr/local/bin/update-nextjs
# Execute: chmod +x /usr/local/bin/update-nextjs

set -e  # Parar em caso de erro

echo "Iniciando atualização do aplicativo Next.js com zero downtime..."

# Configurações
APP_DIR="/var/www/html/front"
DOCKER_COMPOSE_FILE="$APP_DIR/docker-compose.yml"
BACKUP_DIR="$APP_DIR/backups/$(date +%Y%m%d%H%M%S)"

# Criar diretório de backup
mkdir -p $BACKUP_DIR

# Entrar no diretório do projeto
cd $APP_DIR
echo "Diretório de trabalho: $(pwd)"

# Fazer backup do estado atual
echo "Criando backup do estado atual..."
if [ -f "$DOCKER_COMPOSE_FILE" ]; then
  cp $DOCKER_COMPOSE_FILE $BACKUP_DIR/
fi
git rev-parse HEAD > $BACKUP_DIR/git-commit-hash.txt

# Atualizar o repositório
echo "Atualizando o código fonte..."
git stash
git pull

# Identificar o container atual para preparar a troca blue-green
CURRENT_CONTAINER=$(docker ps --filter "name=nextjs-app" --format "{{.Names}}")
echo "Container atual: $CURRENT_CONTAINER"

# Verificar qual nome usar para o novo container (blue-green approach)
if [[ "$CURRENT_CONTAINER" == *"blue"* ]]; then
  NEW_CONTAINER_NAME="nextjs-app-green"
  NEW_PORT=3001
else
  NEW_CONTAINER_NAME="nextjs-app-blue"
  NEW_PORT=3001
fi
echo "Novo container: $NEW_CONTAINER_NAME na porta $NEW_PORT"

# Construir uma nova imagem
echo "Construindo nova imagem Docker..."
docker build -t nextjs-app:latest .

# Iniciar novo container
echo "Iniciando novo container $NEW_CONTAINER_NAME na porta $NEW_PORT..."
docker run -d --name $NEW_CONTAINER_NAME -p $NEW_PORT:3000 --restart always nextjs-app:latest

# Verificar se o novo container está saudável
echo "Verificando saúde do novo container..."
sleep 5  # Dar tempo para o container iniciar
HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$NEW_PORT)

if [ "$HEALTH_CHECK" == "200" ]; then
  echo "Novo container está respondendo corretamente."

  # Atualizar configuração Nginx para apontar para o novo container
  echo "Atualizando configuração do Nginx..."
  NGINX_CONFIG="/etc/nginx/sites-available/default"

  # Fazer backup da configuração do Nginx
  cp $NGINX_CONFIG $BACKUP_DIR/nginx-config.bak

  # Atualizar o proxy_pass no Nginx
  sed -i "s/proxy_pass http:\/\/localhost:3000/proxy_pass http:\/\/localhost:$NEW_PORT/" $NGINX_CONFIG

  # Verificar sintaxe do Nginx
  nginx -t

  # Recarregar Nginx (sem reiniciar - zero downtime)
  echo "Recarregando Nginx..."
  systemctl reload nginx

  # Aguardar tráfego ser redirecionado
  sleep 2

  # Parar e remover o container antigo
  if [ ! -z "$CURRENT_CONTAINER" ]; then
    echo "Removendo container antigo $CURRENT_CONTAINER..."
    docker stop $CURRENT_CONTAINER
    docker rm $CURRENT_CONTAINER
  fi

  echo "Atualização concluída com sucesso! Sistema rodando no container $NEW_CONTAINER_NAME"
else
  echo "ERRO: Novo container não está respondendo corretamente (HTTP $HEALTH_CHECK)"
  echo "Abortando atualização e revertendo..."

  # Parar e remover o container novo que falhou
  docker stop $NEW_CONTAINER_NAME
  docker rm $NEW_CONTAINER_NAME

  echo "Reverted. O sistema continua rodando no container $CURRENT_CONTAINER"
  exit 1
fi