#!/bin/bash
echo "Script arguments: $@"
echo "Number of arguments: $#"

BRANCH_NAME=${1:-$BITBUCKET_BRANCH}
ROOT_FOLDER=${2:-$ROOT_FOLDER}
REPOSITORY_URL=${2:-$REPOSITORY_URL}

cd ${ROOT_FOLDER}
if [ ! -d \"${ROOT_FOLDER}/.git\" ]; then
    git clone ${REPOSITORY_URL} ${ROOT_FOLDER}
fi
git init
git fetch
git checkout ${BRANCH_NAME}
git pull --ff-only origin ${BRANCH_NAME}
# npm install
# npm run build
# pm2 restart .scripts/ecosystem.config.js
# sudo systemctl restart nginx