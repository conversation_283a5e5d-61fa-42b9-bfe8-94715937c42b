# ============================================
# DOCKERFILE OTIMIZADO PARA CURY CLIENTE - NEXT.JS 15
# ============================================

# Definir argumentos que podem ser passados durante o build
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_SITE_URL

# Base image com Node.js LTS (versão Debian para maior compatibilidade de build)
FROM node:20.12.2 AS base

# Instalar dumb-init para gerenciamento de processos
RUN apt-get update && apt-get install -y dumb-init

# Configurar variáveis de ambiente
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Criar usuário não-root para segurança
RUN groupadd --system --gid 1001 nodejs
RUN useradd --system --uid 1001 --gid nodejs nextjs

# Definir diretório de trabalho
WORKDIR /app

# ============================================
# STAGE 1: Instalação de dependências de produção
# ============================================
FROM base AS deps

# Copiar arquivos de configuração de dependências
COPY package.json package-lock.json* ./

# Instalar apenas dependências de produção
RUN npm ci --only=production --no-audit --no-fund --legacy-peer-deps

# ============================================
# STAGE 2: Build da aplicação
# ============================================
FROM base AS builder

# Copiar arquivos de configuração de dependências
COPY package.json package-lock.json* ./

# Instalar TODAS as dependências (incluindo dev) para o build
RUN npm ci --no-audit --no-fund --legacy-peer-deps

# Copiar código fonte
COPY . .

# Configurar variáveis de build
ENV NODE_ENV=production
# ENV NEXT_PUBLIC_API_URL=https://homolog.cliente.cury.net/api
ENV NEXT_PUBLIC_API_URL=http://api:82/api
ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_ENV_VALIDATION=1

# Gerar Build ID único
RUN echo "build-\$(date +%Y%m%d-%H%M)" > BUILD_ID

# Build da aplicação
RUN npm run build

# ============================================
# STAGE 3: Imagem de produção
# ============================================
FROM base AS runner

# Configurar variáveis de produção
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Criar estrutura de diretórios
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Copiar arquivos necessários do build
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Configurar permissões
RUN chown -R nextjs:nodejs /app
RUN chmod +x /app/server.js

# Mudar para usuário não-root
USER nextjs

# Criar healthcheck otimizado
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider --timeout=5 http://localhost:3000/api/health || exit 1

# Expor porta
EXPOSE 3000

# Comando de inicialização com dumb-init para gerenciamento correto de sinais
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]

# ============================================
# STAGE 4: Desenvolvimento (opcional)
# ============================================
FROM base AS development

# Instalar todas as dependências (incluindo dev)
COPY package.json package-lock.json* ./
RUN npm ci --no-audit --no-fund --legacy-peer-deps

# Copiar código fonte
COPY . .

# Configurar variáveis de desenvolvimento
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1

# Expor porta e volumes para desenvolvimento
EXPOSE 3000
VOLUME ["/app"]

# Comando para desenvolvimento
CMD ["npm", "run", "dev-server"]
