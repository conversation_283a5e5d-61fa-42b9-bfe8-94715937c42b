import { fixupConfigRules, fixupPluginRules } from '@eslint/compat';
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import typescriptEslint from '@typescript-eslint/eslint-plugin';
import react from 'eslint-plugin-react';
import { defineConfig } from 'eslint/config';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all
});

export default defineConfig([
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module'
    },

    extends: fixupConfigRules(
      compat.extends(
        'next/core-web-vitals'
      )
    ),

    plugins: {
      '@typescript-eslint': fixupPluginRules(typescriptEslint),
      'react': fixupPluginRules(react)
    },

    ignores: [
      'node_modules/',
      '.next/',
      'dist/',
      'build/',
      'coverage/',
      '*.config.js',
      '*.config.ts',
      'public/',
      '.vercel/',
      '*.d.ts',
      '*.test.*',
      '*.spec.*'
    ],

    rules: {
      // Desabilitar todas as regras restritivas
      'semi': 'off',
      'quotes': 'off',
      'indent': 'off',
      'no-unused-vars': 'off',
      'no-console': 'off',
      'no-undef': 'off',
      'no-empty': 'off',
      'no-unreachable': 'off',
      'no-mixed-spaces-and-tabs': 'off',
      'no-redeclare': 'off',
      'no-dupe-keys': 'off',
      'no-duplicate-case': 'off',
      'no-extra-semi': 'off',
      'no-func-assign': 'off',
      'no-irregular-whitespace': 'off',
      'no-sparse-arrays': 'off',
      'use-isnan': 'off',
      'valid-typeof': 'off',
      
      // React rules - desabilitar
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
      'react/no-unescaped-entities': 'off',
      'react/display-name': 'off',
      'react/jsx-key': 'off',
      'react/no-children-prop': 'off',
      'react/no-danger': 'off',
      'react/no-direct-mutation-state': 'off',
      'react/jsx-no-target-blank': 'off',
      'react/jsx-no-duplicate-props': 'off',
      'react/jsx-uses-vars': 'off',
      'react/jsx-uses-react': 'off',
      
      // React Hooks - desabilitar
      'react-hooks/rules-of-hooks': 'off',
      'react-hooks/exhaustive-deps': 'off',
      
      // TypeScript rules - desabilitar
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/ban-types': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-empty-interface': 'off',
      '@typescript-eslint/no-inferrable-types': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/prefer-as-const': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-this-alias': 'off',
      
      // Next.js rules - manter apenas o essencial
      '@next/next/no-img-element': 'off',
      '@next/next/no-html-link-for-pages': 'off',
      '@next/next/no-sync-scripts': 'off',
      '@next/next/no-css-tags': 'off',
      '@next/next/no-head-element': 'off',
      '@next/next/no-page-custom-font': 'off',
      '@next/next/no-styled-jsx-in-document': 'off',
      '@next/next/no-title-in-document-head': 'off',
      '@next/next/no-unwanted-polyfillio': 'off',
      
      // Segurança - manter apenas uma regra crítica
      'no-eval': 'off',
      'no-implied-eval': 'off',
      'no-new-func': 'off',
      'no-prototype-builtins': 'off',
      'no-return-await': 'off',
      'no-script-url': 'off',
      'no-var': 'off',
      'prefer-const': 'off',
      'eqeqeq': 'off',
      'no-async-promise-executor': 'off',
      'no-await-in-loop': 'off',
      'no-promise-executor-return': 'off'
    }
  }
]);
