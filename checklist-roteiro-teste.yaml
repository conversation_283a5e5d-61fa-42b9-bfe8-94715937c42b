# CHECKLIST PARA JIRA - CCM-364
# Co<PERSON> e cole os itens abaixo no checklist nativo do Jira

checklist_items:
    - name: "PRÉ-REQUISITOS"
      items:
        - text: "Usuário com boleto ato pago"
          checked: false
          status: "open"
        - text: "Usuário com boleto ato em aberto"
          checked: false
          status: "open"
        - text: "Testar síndico"
          checked: false
          status: "open"
        - text: "Testar usuário com muitos ativos"
          checked: false
          status: "open"
        - text: "Testar usuários com situações diferentes (financiado, quitado, etc)"
          checked: false
          status: "open"
        - text: "Testar desktop"
          checked: false
          status: "open"
        - text: "Testar web mobile"
          checked: false
          status: "open"
        - text: "Testar App"
          checked: false
          status: "open"
        - text: "Verificar se o usuário possui os dados corretos no Sienge"
          checked: false
          status: "open"
          priority: "high"

    - name: "1. AUTENTICAÇÃO E ACESSO"
      items:
        - text: "/login - Verificar animações de entrada da página"
          checked: false
          status: "open"
        - text: "/verifica-codigo - Testar animações de transição"
          checked: false
          status: "open"
        - text: "/esqueci-minha-senha - Verificar fetch de recuperação"
          checked: false
          status: "open"
        - text: "/nova-senha - Testar animações e submit"
          checked: false
          status: "open"
        - text: "/primeiro-acesso - Verificar fluxo completo"
          checked: false
          status: "open"
        - text: "/senha-definitiva - Testar animações de confirmação"
          checked: false
          status: "open"
        - text: "Login com credenciais válidas"
          checked: false
          status: "open"
          priority: "high"
        - text: "Login com credenciais inválidas"
          checked: false
          status: "open"
        - text: "Recuperação de senha"
          checked: false
          status: "open"
        - text: "Verificação de código"
          checked: false
          status: "open"
        - text: "Definição de nova senha"
          checked: false
          status: "open"

    - name: "2. PÁGINA HOME"
      items:
        - text: "Verificar se aparecerá boleto ato"
          checked: false
          status: "open"
        - text: "Testar troca de ativos"
          checked: false
          status: "open"
        - text: "Carregamento inicial com verificação de boleto ato"
          checked: false
          status: "open"
        - text: "Verificar no Sienge quantas chamadas foram feitas"
          checked: false
          status: "open"

    - name: "3. MEU IMÓVEL"
      items:
        - text: "Verificar se a planta está aparecendo"
          checked: false
          status: "open"
        - text: "Verificar se é possível baixar a planta"
          checked: false
          status: "open"
        - text: "Verificar se é possível baixar um documento"
          checked: false
          status: "open"

    - name: "4. ANDAMENTO DA OBRA"
      items:
        - text: "Verificar se os dados do andamento estão aparecendo"
          checked: false
          status: "open"
        - text: "Verificar se os dados estão atualizando quando muda no Salesforce"
          checked: false
          status: "open"

    - name: "3.1 MEU IMÓVEL - FOTOS E VÍDEOS"
      items:
        - text: "Verificar se o painel de fotos está carregando"
          checked: false
          status: "open"
        - text: "Verificar se o zoom está funcionando"
          checked: false
          status: "open"
        - text: "Verificar se o vídeo está funcionando"
          checked: false
          status: "open"
        - text: "Verificar data da última atualização"
          checked: false
          status: "open"
        - text: "Verificar se a data atualiza quando existe alguma mudança no Salesforce"
          checked: false
          status: "open"

    - name: "4. FINANCEIRO"
      subsections:
        - name: "4.1 2ª Via de Boleto"
          items:
            - text: "Buscar boletos disponíveis"
              checked: false
              status: "open"
            - text: "Gerar 2ª via de boleto"
              checked: false
              status: "open"
            - text: "Download de PDF do boleto"
              checked: false
              status: "open"
            - text: "Copiar código de barras"
              checked: false
              status: "open"

        - name: "4.2 Relatório de Extrato"
          items:
            - text: "Verificar se carregou corretamente"
              checked: false
              status: "open"
            - text: "Verificar se baixa o PDF"
              checked: false
              status: "open"
            - text: "Verificar se envia por email"
              checked: false
              status: "open"
            - text: "Verificar se abre a parcela com as informações corretas"
              checked: false
              status: "open"

        - name: "4.3 Negocie Suas Parcelas"
          items:
            - text: "Testar negociação - Seleção no menu de opções"
              checked: false
              status: "open"
            - text: "Verificar se os dados e cálculos do resumo estão corretos"
              checked: false
              status: "open"
            - text: "Verificar se houve a negociação no Sienge"
              checked: false
              status: "open"
            - text: "Antecipação - Seleção de parcelas"
              checked: false
              status: "open"
            - text: "Valor com desconto"
              checked: false
              status: "open"
            - text: "Verificar se houve antecipação no Sienge"
              checked: false
              status: "open"

        - name: "4.4 Informes"
          items:
            - text: "Verificar no Sienge se o usuário tem informes disponíveis"
              checked: false
              status: "open"
            - text: "Verificar se é possível baixar o Informe"
              checked: false
              status: "open"
            - text: "Verificar se está enviado por email"
              checked: false
              status: "open"

        - name: "4.5 Cury Chega Mais"
          items:
            - text: "Verificar se o usuário pode aderir ao programa"
              checked: false
              status: "open"
            - text: "Verificar se as imagens estão corretas"
              checked: false
              status: "open"
            - text: "Verificar se o usuário consegue aderir"
              checked: false
              status: "open"
            - text: "Verificar que se o usuário já fizer parte, não aparecer a opção de aderir"
              checked: false
              status: "open"

    - name: "5. DICAS"
      items:
        - text: "Verificar se o conteúdo está abrindo"
          checked: false
          status: "open"

    - name: "6. DÚVIDAS FREQUENTES"
      items:
        - text: "Verificar se o conteúdo está abrindo"
          checked: false
          status: "open"

    - name: "7. SOLICITAR ATENDIMENTO"
      items:
        - text: "Verificar se está abrindo um chamado e levando para a página de acompanhamento"
          checked: false
          status: "open"

    - name: "8. ACOMPANHAR ATENDIMENTO"
      items:
        - text: "Verificar os chamados estão atualizados com o do Salesforce"
          checked: false
          status: "open"
        - text: "Verificar que está abrindo a página de detalhes do chamado"
          checked: false
          status: "open"

    - name: "6.1 MEUS DADOS"
      items:
        - text: "Testar atualização dos dados"
          checked: false
          status: "open"

    - name: "6.2 ONDE ESTOU LOGADO"
      items:
        - text: "Testar Logout"
          checked: false
          status: "open"

metadata:
  created_by: "Augment Code"
  created_date: "2025-08-07"
  jira_issue: "CCM-364"
  total_items: 52
