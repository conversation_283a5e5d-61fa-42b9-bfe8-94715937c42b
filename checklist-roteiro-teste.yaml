# CHECKLIST PARA JIRA - CCM-364
# Co<PERSON> e cole os itens abaixo no checklist nativo do Jira

checklist_items:
    - name: "PRÉ-REQUISITOS"
      items:
        - text: "Usuário com boleto ato pago"
          status: "open"
        - text: "Usuário com boleto ato em aberto"
          status: "open"
        - text: "Testar síndico"
          status: "open"
        - text: "Testar usuário com muitos ativos"
          status: "open"
        - text: "Testar usuários com situações diferentes (financiado, quitado, etc)"
          status: "open"
        - text: "Testar desktop"
          status: "open"
        - text: "Testar web mobile"
          status: "open"
        - text: "Testar App"
          status: "open"
        - text: "Verificar se o usuário possui os dados corretos no Sienge"
          status: "open"
          priority: "high"

    - name: "1. AUTENTICAÇÃO E ACESSO"
      items:
        - text: "/login - Verificar animações de entrada da página"
          status: "open"
        - text: "/verifica-codigo - Testar animações de transição"
          status: "open"
        - text: "/esqueci-minha-senha - Verificar fetch de recuperação"
          status: "open"
        - text: "/nova-senha - Testar animações e submit"
          status: "open"
        - text: "/primeiro-acesso - Verificar fluxo completo"
          status: "open"
        - text: "/senha-definitiva - Testar animações de confirmação"
          status: "open"
        - text: "Login com credenciais válidas"
          status: "open"
          priority: "high"
        - text: "Login com credenciais inválidas"
          status: "open"
        - text: "Recuperação de senha"
          status: "open"
        - text: "Verificação de código"
          status: "open"
        - text: "Definição de nova senha"
          status: "open"

    - name: "2. PÁGINA HOME"
      items:
        - text: "Verificar se aparecerá boleto ato"
          status: "open"
        - text: "Testar troca de ativos"
          status: "open"
        - text: "Carregamento inicial com verificação de boleto ato"
          status: "open"
        - text: "Verificar no Sienge quantas chamadas foram feitas"
          status: "open"

    - name: "3. MEU IMÓVEL"
      items:
        - text: "Verificar se a planta está aparecendo"
          status: "open"
        - text: "Verificar se é possível baixar a planta"
          status: "open"
        - text: "Verificar se é possível baixar um documento"
          status: "open"

    - name: "4. ANDAMENTO DA OBRA"
      items:
        - text: "Verificar se os dados do andamento estão aparecendo"
          status: "open"
        - text: "Verificar se os dados estão atualizando quando muda no Salesforce"
          status: "open"

    - name: "3.1 MEU IMÓVEL - FOTOS E VÍDEOS"
      items:
        - text: "Verificar se o painel de fotos está carregando"
          status: "open"
        - text: "Verificar se o zoom está funcionando"
          status: "open"
        - text: "Verificar se o vídeo está funcionando"
          status: "open"
        - text: "Verificar data da última atualização"
          status: "open"
        - text: "Verificar se a data atualiza quando existe alguma mudança no Salesforce"
          status: "open"

    - name: "4. FINANCEIRO"
      subsections:
        - name: "4.1 2ª Via de Boleto"
          items:
            - text: "Buscar boletos disponíveis"
              status: "open"
            - text: "Gerar 2ª via de boleto"
              status: "open"
            - text: "Download de PDF do boleto"
              status: "open"
            - text: "Copiar código de barras"
              status: "open"

        - name: "4.2 Relatório de Extrato"
          items:
            - text: "Verificar se carregou corretamente"
              status: "open"
            - text: "Verificar se baixa o PDF"
              status: "open"
            - text: "Verificar se envia por email"
              status: "open"
            - text: "Verificar se abre a parcela com as informações corretas"
              status: "open"

        - name: "4.3 Negocie Suas Parcelas"
          items:
            - text: "Testar negociação - Seleção no menu de opções"
              status: "open"
            - text: "Verificar se os dados e cálculos do resumo estão corretos"
              status: "open"
            - text: "Verificar se houve a negociação no Sienge"
              status: "open"
            - text: "Antecipação - Seleção de parcelas"
              status: "open"
            - text: "Valor com desconto"
              status: "open"
            - text: "Verificar se houve antecipação no Sienge"
              status: "open"

        - name: "4.4 Informes"
          items:
            - text: "Verificar no Sienge se o usuário tem informes disponíveis"
              status: "open"
            - text: "Verificar se é possível baixar o Informe"
              status: "open"
            - text: "Verificar se está enviado por email"
              status: "open"

        - name: "4.5 Cury Chega Mais"
          items:
            - text: "Verificar se o usuário pode aderir ao programa"
              status: "open"
            - text: "Verificar se as imagens estão corretas"
              status: "open"
            - text: "Verificar se o usuário consegue aderir"
              status: "open"
            - text: "Verificar que se o usuário já fizer parte, não aparecer a opção de aderir"
              status: "open"

    - name: "5. DICAS"
      items:
        - text: "Verificar se o conteúdo está abrindo"
          status: "open"

    - name: "6. DÚVIDAS FREQUENTES"
      items:
        - text: "Verificar se o conteúdo está abrindo"
          status: "open"

    - name: "7. SOLICITAR ATENDIMENTO"
      items:
        - text: "Verificar se está abrindo um chamado e levando para a página de acompanhamento"
          status: "open"

    - name: "8. ACOMPANHAR ATENDIMENTO"
      items:
        - text: "Verificar os chamados estão atualizados com o do Salesforce"
          status: "open"
        - text: "Verificar que está abrindo a página de detalhes do chamado"
          status: "open"

    - name: "6.1 MEUS DADOS"
      items:
        - text: "Testar atualização dos dados"
          status: "open"

    - name: "6.2 ONDE ESTOU LOGADO"
      items:
        - text: "Testar Logout"
          status: "open"

metadata:
  created_by: "Augment Code"
  created_date: "2025-08-07"
  jira_issue: "CCM-364"
  total_items: 52
