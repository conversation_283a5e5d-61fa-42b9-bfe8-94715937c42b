services:
  nextjs-blue:
    container_name: nextjs-app-blue
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - HOSTNAME=0.0.0.0
      - PORT=3000
    restart: always
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/']
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  nextjs-green:
    container_name: nextjs-app-green
    # Inicialmente desabilitado - será ativado durante atualizações
    profiles: ['manual']
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '3001:3000'
    environment:
      - NODE_ENV=production
      - HOSTNAME=0.0.0.0
      - PORT=3000
    restart: always
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/']
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
