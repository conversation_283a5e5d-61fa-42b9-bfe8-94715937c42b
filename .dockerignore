# ============================================
# .dockerignore para Cury Cliente - Next.js
# ============================================

# Dependências
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn
.pnpm-debug.log*

# Build outputs
.next/
out/
dist/
build/

# Cache directories
.cache/
.parcel-cache/
.nuxt/
.vuepress/dist

# Logs
logs
*.log
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment files
.env*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript cache
*.tsbuildinfo
.tsbuildinfo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
bitbucket-pipelines.yml
.circleci/

# Testing
coverage/
.coverage
.coveragerc
.pytest_cache/
test-results/
jest.config.js

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Documentation
*.md
docs/
README*
CHANGELOG*
LICENSE*

# Storybook
.storybook/
storybook-static/

# Linting and formatting
.eslintrc*
.prettierrc*
.stylelintrc*

# Package manager files
# package-lock.json - REMOVIDO para permitir npm ci
pnpm-lock.yaml

# Local development
.next/cache/
.swc/