# definitions:
#   steps:
#     deploy-staging: &deploy-staging
#       name: Deploying to Staging
#       deployment: Staging
#       script:
#         - echo "Start Staging deploy..."
#         - export ENVIRONMENT="homolog"
#         - export ROOT_FOLDER="/var/www/html/front"
#         - export REPOSITORY_URL="*****************:studiowox/cury_app_cliente_web.git"
#         - pipe: atlassian/ssh-run:0.4.1
#           variables:
#             SSH_USER: $SSH_USER
#             SERVER: $HOMOLOG_EC2_IP
#             SSH_KEY: $SSH_PRIVATE_KEY_HOMOLOG
#             MODE: 'command'
#             COMMAND: 'bash ${ROOT_FOLDER}/.scripts/deploy-homolog.sh ${BITBUCKET_BRANCH} ${ROOT_FOLDER} ${REPOSITORY_URL}'
#         - echo "Done Staging deploy."

#     deploy-master: &deploy-master
#       name: Deploying to Production
#       deployment: Production
#       script:
#         - echo "Start Production deploy..."
#         - export ENVIRONMENT="production"
#         - export ROOT_FOLDER="/var/www/html/front"
#         - export REPOSITORY_URL="*****************:studiowox/cury_app_cliente_web.git"
#         - pipe: atlassian/ssh-run:0.4.1
#           variables:
#             SSH_USER: $SSH_USER
#             SERVER: $PRODUCTION_EC2_IP
#             SSH_KEY: $SSH_KEY_PROD
#             ROOT_FOLDER: '/var/www/html/front'
#             MODE: 'command'
#             COMMAND: 'bash ${ROOT_FOLDER}/.scripts/deploy-production.sh ${BITBUCKET_BRANCH} ${ROOT_FOLDER} ${REPOSITORY_URL}'
#         - echo "Done Production deploy."

# pipelines:
#   branches:
#     homolog:
#       - step:
#           <<: *deploy-staging
#     master:
#       - step:
#           <<: *deploy-master
