# Projeto Frontend - Cury Cliente

Bem-vindo ao projeto Frontend do Cury Cliente!

## Instalação

### 1. Instalar e Configurar o NVM, Node.js e npm

Instale o NVM (Node Version Manager):

```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash
```

Carregue o NVM:

```bash
export NVM_DIR="$([ -z "${XDG_CONFIG_HOME-}" ] && printf %s "${HOME}/.nvm" || printf %s "${XDG_CONFIG_HOME}/nvm")"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
```

Instale e use a versão especificada do Node.js e npm:

```bash
nvm install 20.12.2
nvm use 20.12.2
npm install -g npm@10.5.0
```

### 2. Clonar o Repositório

Clone o repositório no diretório que você deseja usar.

# Caso não tenha a chave associada no bitbucket será necessário fazer a configuracao primeiro.

```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_rsa
cat ~/.ssh/id_rsa.pub
```

Copie o conteúdo exibido. Vá para o Bitbucket, navegue até as configurações da sua conta (User settings) e em seguida em "SSH keys". Adicione uma nova chave SSH e cole o conteúdo da sua chave pública.

Verifique a conexão SSH com o Bitbucket:
Teste a conexão SSH com o Bitbucket para garantir que a chave está configurada corretamente:

```bash
ssh -T *****************
```

```bash
sudo mkdir -p /var/www/html/front
sudo chown $USER:$USER /var/www/html/front
<NAME_EMAIL>:studiowox/cury_app_cliente_web.git /var/www/html/front
```

### 3. Configurar Variáveis de Ambiente

Crie um arquivo `.env.local`, `.env` seguindo o padrão do arquivo `.env` do repositório.

```env
NEXT_PUBLIC_APP_ENV=
NEXT_PUBLIC_API_URL=
NEXT_PUBLIC_SITE_URL=
NEXT_PUBLIC_SITE_URL=
NEXT_PUBLIC_FILES_URL=
NEXT_PUBLIC_WHATS_NUMBER=
SENTRY_AUTH_TOKEN=

# SIENGE PROD
SIENGE_BASE_URL=
SIENGE_USERNAME=
SIENGE_PASSWORD=

# SIENGE HOMOLOG
# SIENGE_BASE_URL=
# SIENGE_USERNAME=
# SIENGE_PASSWORD=

```

### 4. Build do Projeto

Instale as dependências e construa o projeto:

````bash
npm install
npm run dev

Reinicie o Nginx para aplicar as configurações:

```bash
sudo systemctl restart nginx
````
