// /*
//  * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
//  * Copyright (C) 2024 StudioWox
//  *
//  * Desenvolvido exclusivamente para Cury por StudioWox.
//  *
//  * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
//  * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
//  *
//  * Para mais informações, entre em contato com:
//  * Thalles Freitas - <EMAIL>
//  * StudioWox: https://studiowox.com.br
//  * Cury: https://cury.net
//  *
//  * Todos os direitos reservados.
//  */

// module.exports = {
//   root: true,
//   env: {
//     browser: true,
//     es2024: true,
//     node: true
//   },
//   extends: [
//     'next/core-web-vitals',
//     'eslint:recommended',
//     'plugin:@typescript-eslint/recommended',
//     'plugin:@typescript-eslint/recommended-requiring-type-checking',
//     'plugin:react/recommended',
//     'plugin:react/jsx-runtime',
//     'plugin:import/errors',
//     'plugin:import/warnings',
//     'plugin:import/typescript',
//     'plugin:@tanstack/eslint-plugin-query/recommended',
//     'prettier'
//   ],
//   parser: '@typescript-eslint/parser',
//   parserOptions: {
//     projectService: true,
//     ecmaVersion: 'latest',
//     sourceType: 'module',
//     project: ['./tsconfig.json'],
//     ecmaFeatures: {
//       jsx: true
//     },
//     tsconfigRootDir: __dirname
//   },
//   plugins: [
//     'import',
//     'react',
//     '@typescript-eslint',
//     'prettier',
//     'unused-imports',
//     '@tanstack/query'
//   ],
//   settings: {
//     react: {
//       version: 'detect'
//     }
//     // 'import/resolver': {
//     //   typescript: {
//     //     alwaysTryTypes: true,
//     //     project: './tsconfig.json'
//     //   },
//     //   node: true
//     // }
//   },
//   rules: {
//     'jsx-a11y/aria-props': 'off',
//     'prettier/prettier': [
//       'warn',
//       {
//         singleQuote: true,
//         jsxSingleQuote: true,
//         trailingComma: 'none',
//         useTabs: false,
//         tabWidth: 2,
//         quoteProps: 'preserve',
//         endOfLine: 'auto'
//       }
//     ],
//     'quotes': ['error', 'single', { avoidEscape: true, allowTemplateLiterals: true }],
//     '@typescript-eslint/no-unused-vars': [
//       'warn',
//       {
//         argsIgnorePattern: '^_',
//         varsIgnorePattern: '^_',
//         caughtErrorsIgnorePattern: '^_'
//       }
//     ],
//     '@typescript-eslint/no-explicit-any': 'warn',
//     '@typescript-eslint/explicit-module-boundary-types': 'off',
//     'react/react-in-jsx-scope': 'off',
//     'react/jsx-uses-react': 'off',
//     'react/prop-types': 'off',
//     'quote-props': ['warn', 'consistent'],

//     // Regras de boas práticas
//     'for-direction': 'error',
//     'no-prototype-builtins': 'error',
//     'no-template-curly-in-string': 'error',
//     'no-unsafe-negation': 'error',
//     'array-callback-return': 'error',
//     'block-scoped-var': 'error',
//     'complexity': ['error', 17],
//     'consistent-return': 'error',
//     'eqeqeq': ['error', 'smart'],
//     'guard-for-in': 'error',
//     'no-alert': 'error',
//     'no-caller': 'error',
//     'no-div-regex': 'error',
//     'no-eval': 'error',
//     'no-extend-native': 'error',
//     'no-extra-bind': 'error',
//     'no-extra-label': 'error',
//     'no-floating-decimal': 'error',
//     'no-implied-eval': 'error',
//     'no-iterator': 'error',
//     'no-labels': 'error',
//     'no-lone-blocks': 'error',
//     'no-loop-func': 'error',
//     'no-new': 'error',
//     'no-new-func': 'error',
//     'no-new-wrappers': 'error',
//     'no-proto': 'error',
//     'no-restricted-properties': 'error',
//     'no-return-assign': 'error',
//     // 'no-return-await': 'error',
//     'no-self-compare': 'error',
//     'no-sequences': 'error',
//     'no-throw-literal': 'error',
//     'no-unmodified-loop-condition': 'error',
//     'no-unused-expressions': 'error',
//     'no-useless-call': 'error',
//     'no-useless-concat': 'error',
//     'no-useless-escape': 'error',
//     'no-useless-return': 'error',
//     'unused-imports/no-unused-imports': 'error',
//     'unused-imports/no-unused-vars': [
//       'warn',
//       {
//         vars: 'all',
//         varsIgnorePattern: '^_',
//         args: 'after-used',
//         argsIgnorePattern: '^_'
//       }
//     ],
//     'no-void': 'error',
//     'no-with': 'error',
//     'radix': 'error',
//     'require-await': 'error',
//     'wrap-iife': 'error',
//     'yoda': 'error',

//     // Estilo
//     'camelcase': 'off',
//     'consistent-this': ['warn', 'that'],
//     'func-name-matching': 'error',
//     'func-style': ['warn', 'declaration', { allowArrowFunctions: true }],
//     'lines-between-class-members': ['error', 'always', { exceptAfterSingleLine: true }],
//     'max-depth': ['warn', 4],
//     'max-lines': ['warn', 1000],
//     'max-params': ['warn', 4],
//     'no-array-constructor': 'warn',
//     'no-bitwise': 'warn',
//     'no-lonely-if': 'error',
//     'no-multi-assign': 'warn',
//     'no-nested-ternary': 'warn',
//     'no-new-object': 'warn',
//     'no-underscore-dangle': 'warn',
//     'no-unneeded-ternary': 'warn',
//     'one-var': ['warn', 'never'],
//     'operator-assignment': 'warn',

//     // ES6+
//     'no-duplicate-imports': 'error',
//     'no-useless-computed-key': 'error',
//     'no-useless-rename': 'error',
//     'no-var': 'error',
//     'object-shorthand': 'error',
//     'prefer-arrow-callback': 'error',
//     'prefer-const': 'error',
//     'prefer-destructuring': ['warn', { object: true, array: false }],
//     'prefer-numeric-literals': 'warn',
//     'prefer-rest-params': 'warn',
//     'prefer-spread': 'warn',

//     // Import
//     'import/extensions': ['error', 'never', { css: 'never', svg: 'always', json: 'always' }],
//     'import/first': 'error',
//     'import/newline-after-import': 'error',
//     'import/no-absolute-path': 'error',
//     'import/no-amd': 'error',
//     'import/no-deprecated': 'warn',
//     'import/no-duplicates': 'error',
//     'import/no-mutable-exports': 'error',
//     'import/no-named-as-default': 'warn',
//     'import/no-named-as-default-member': 'warn',
//     'import/no-named-default': 'error',
//     'import/no-unresolved': 'off',

//     // React Hooks
//     'react-hooks/rules-of-hooks': 'error',
//     'react-hooks/exhaustive-deps': 'off',

//     // Notice section removed
//     // Tanstack Query
//     '@tanstack/query/exhaustive-deps': 'error',
//     '@tanstack/query/no-unstable-deps': 'error',
//     '@tanstack/query/no-rest-destructuring': 'error',
//     '@tanstack/query/stable-query-client': 'error',

//     // REVER
//     '@typescript-eslint/no-base-to-string': 'off',
//     '@typescript-eslint/no-misused-promises': 'off',
//     '@typescript-eslint/no-floating-promises': 'off',
//     '@typescript-eslint/no-unsafe-assignment': 'off',
//     '@typescript-eslint/no-unsafe-argument': 'off',
//     '@typescript-eslint/no-unsafe-return': 'off',
//     '@typescript-eslint/no-unsafe-call': 'off',
//     '@typescript-eslint/no-unsafe-member-access': 'off',
//     'no-return-await': 'off',
//     '@typescript-eslint/await-thenable': 'off',
//     '@typescript-eslint/require-await': 'off',
//     'import/namespace': 'off'
//   }
// };
