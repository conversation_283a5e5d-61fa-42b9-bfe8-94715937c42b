CHECKLIST PARA JIRA - CCM-364
Copie e cole os itens abaixo no checklist nativo do Jira

=== PRÉ-REQUISITOS ===
Usuário com boleto ato pago
Usuário com boleto ato em aberto
Testar síndico
Testar usuário com muitos ativos
Testar usuários com situações diferentes (financiado, quitado, etc)
Testar desktop
Testar web mobile
Testar App
Verificar se o usuário possui os dados corretos no Sienge

=== 1. AUTENTICAÇÃO E ACESSO ===
/login - Verificar animações de entrada da página
/verifica-codigo - Testar animações de transição
/esqueci-minha-senha - Verificar fetch de recuperação
/nova-senha - Testar animações e submit
/primeiro-acesso - Verificar fluxo completo
/senha-definitiva - Testar animações de confirmação
Login com credenciais válidas
Login com credenciais inválidas
Recuperação de senha
Verificação de código
Definição de nova senha

=== 2. PÁGINA HOME ===
Verificar se aparecerá boleto ato
Testar troca de ativos
Carregamento inicial com verificação de boleto ato
Verificar no Sienge quantas chamadas foram feitas

=== 3. MEU IMÓVEL ===
Verificar se a planta está aparecendo
Verificar se é possível baixar a planta
Verificar se é possível baixar um documento
Verificar se o painel de fotos está carregando
Verificar se o zoom está funcionando
Verificar se o vídeo está funcionando
Verificar data da última atualização
Verificar se a data atualiza quando existe alguma mudança no Salesforce

=== 4. ANDAMENTO DA OBRA ===
Verificar se os dados do andamento estão aparecendo
Verificar se os dados estão atualizando quando muda no Salesforce

=== 5. FINANCEIRO ===
Buscar boletos disponíveis
Gerar 2ª via de boleto
Download de PDF do boleto
Copiar código de barras
Verificar se carregou corretamente (extrato)
Verificar se baixa o PDF (extrato)
Verificar se envia por email (extrato)
Verificar se abre a parcela com as informações corretas
Testar negociação - Seleção no menu de opções
Verificar se os dados e cálculos do resumo estão corretos
Verificar se houve a negociação no Sienge
Antecipação - Seleção de parcelas
Valor com desconto
Verificar se houve antecipação no Sienge
Verificar no Sienge se o usuário tem informes disponíveis
Verificar se é possível baixar o Informe
Verificar se está enviado por email (informe)
Verificar se o usuário pode aderir ao programa Chega Mais
Verificar se as imagens estão corretas (Chega Mais)
Verificar se o usuário consegue aderir (Chega Mais)
Verificar que se o usuário já fizer parte, não aparecer a opção de aderir

=== 6. OUTRAS SEÇÕES ===
Verificar se o conteúdo está abrindo (Dicas)
Verificar se o conteúdo está abrindo (Dúvidas Frequentes)
Verificar se está abrindo um chamado e levando para a página de acompanhamento
Verificar os chamados estão atualizados com o do Salesforce
Verificar que está abrindo a página de detalhes do chamado
Testar atualização dos dados (Meus Dados)
Testar Logout (Onde Estou Logado)

TOTAL: 52 ITENS
