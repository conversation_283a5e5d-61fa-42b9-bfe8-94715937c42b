# syntax=docker.io/docker/dockerfile:1

FROM node:20-alpine

# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

# Set environment for development
ENV NODE_ENV=development
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Set Sentry warning suppression
ENV SENTRY_SUPPRESS_TURBOPACK_WARNING=1

WORKDIR /app

# Copiar package.json e instalar dependências
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* .npmrc* ./

# Instalar dependências - em modo development para obter todas as dependências
RUN \
  if [ -f yarn.lock ]; then yarn install --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm install --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Copiar o restante dos arquivos
COPY . .

# Configurar workaround para o problema de process.env
RUN echo "try { if (process.env && Object.getOwnPropertyDescriptor(process, 'env')) { const originalEnvDescriptor = Object.getOwnPropertyDescriptor(process, 'env'); if (originalEnvDescriptor && originalEnvDescriptor.configurable) { const envCopy = { ...process.env }; Object.defineProperty(process, 'env', { get() { return envCopy; }, set() { return true; }, configurable: true, enumerable: true }); } } } catch (e) { console.warn('Failed to protect process.env:', e); }" > process-env-protect.js

# Expor a porta
EXPOSE 3000

# Comando para iniciar o aplicativo em desenvolvimento
CMD ["sh", "-c", "node process-env-protect.js && npm run dev-server-turbo"]
