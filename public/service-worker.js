/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
// const CACHE_NAME = 'image-cache-v1';

// self.addEventListener('install', (event) => {
//   event.waitUntil(caches.open(CACHE_NAME));
// });

// self.addEventListener('fetch', (event) => {
//   // console.log('self.addEventListener')
//   if (event.request.url.includes('/api/getimg')) {
//     event.respondWith(
//       caches.open(CACHE_NAME).then((cache) => {
//         return cache.match(event.request).then((response) => {
//           if (response) {
//             return response;
//           }

//           return fetch(event.request).then((networkResponse) => {
//             cache.put(event.request, networkResponse.clone());
//             return networkResponse;
//           });
//         });
//       })
//     );
//   }
// });

// Importar o BUILD_ID gerado automaticamente
import { BUILD_ID } from '../src/BuildId';

// Usar o BUILD_ID como parte do nome do cache
const CACHE_NAME = `image-cache-${BUILD_ID}`;

self.addEventListener('install', (event) => {
  console.log(`Service Worker instalando versão: ${BUILD_ID}`);
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      // Força a ativação imediata do service worker
      return self.skipWaiting();
    })
  );
});

// Adiciona evento de ativação para limpar caches antigos
self.addEventListener('activate', (event) => {
  console.log(`Service Worker ativando versão: ${BUILD_ID}`);
  event.waitUntil(
    // Limpa caches antigos quando uma nova versão é ativada
    caches
      .keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames
            .filter((name) => {
              // Remove todos os caches de imagem antigos
              return name.startsWith('image-cache-') && name !== CACHE_NAME;
            })
            .map((name) => {
              console.log(`Deletando cache antigo: ${name}`);
              return caches.delete(name);
            })
        );
      })
      .then(() => {
        // Toma controle de todos os clientes sem precisar recarregar a página
        return self.clients.claim();
      })
  );
});

self.addEventListener('fetch', (event) => {
  if (event.request.url.includes('/api/getimg')) {
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response) {
            return response;
          }

          return fetch(event.request).then((networkResponse) => {
            cache.put(event.request, networkResponse.clone());
            return networkResponse;
          });
        });
      })
    );
  }
});

// Adiciona listener de mensagem para informar clientes sobre a versão atual
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'GET_VERSION') {
    // Envia a versão atual (BUILD_ID) para o cliente
    event.source.postMessage({
      type: 'VERSION_INFO',
      version: BUILD_ID
    });
  }
});
