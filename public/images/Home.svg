<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g filter="url(#filter0_i_3744_14184)">
<rect width="25" height="25" fill="url(#pattern0_3744_14184)"/>
</g>
<defs>
<filter id="filter0_i_3744_14184" x="0" y="0" width="25" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="100" operator="erode" in="SourceAlpha" result="effect1_innerShadow_3744_14184"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="50"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.101961 0 0 0 0 0.215686 0 0 0 0 0.301961 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3744_14184"/>
</filter>
<pattern id="pattern0_3744_14184" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_3744_14184" transform="scale(0.00195312)"/>
</pattern>
<image id="image0_3744_14184" width="512" height="512" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAMAAADDpiTIAAAAA3NCSVQICAjb4U/gAAAACXBIWXMAAHdgAAB3YAH/HeIpAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAhlQTFRF////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHZoYKgAAALJ0Uk5TAAECAwQFBgcICQoLDA4PEBESExUWGBkaHB8gISIjJCUnKSssLS4yMzQ3ODk6Ozw9QUNERUZHSEtMT1BSU1RVVldYWV9gYmNkZWZnaGtsb3BxdHV2d3h6fn+Cg4SGh4iJiouMkpOUlZiZmpucnaCipaeoqaqrrK2ys7S1t7i5uru+wMHCxcbHyMnKy8zOz9DR0tPU1tna29zd3uDj5OXm5+rr7e7v8PLz9PX29/j5+vz9/u9jXOEAAAtBSURBVHja7dz3mx1VHcfxswECSQSiJtFAUJpIUUCKAgIWkKbSNhYQN4ASilKDhRJQCKhISaQlIgkkgKEsbOYvVARhd3Nntty5954z39fn15lNnjnzep7s7ns3KcXehg3J4m7J7VV1+xLnEHVLN1X/3aalTiLmVmyu/rfNK5xFxK18ovp4T650GvG2Zmv1ybatcR7Rtm57NW3b1zmRWDtuZzVjO493JpF28p5q1vac4lTi7Ky91X7be5ZzibILJqsem7zQycTYZVNVz01d5mwibLyq3Xqn0/1NVA2Thrq+D/NP06Shbu+j/NO0+6WhDu//+adp0lB392n+aZo01NVNzz9Nk4a6uZn5p2nSUBc3O/80TRrq3vbPP02Thrq2XvmnadJQt9Y7/zRNGurSLp2qFjxpqDsbrxY1aagjm6gWOWmoC5sr/zTtDmmo+M2df6ShLm8++Uca6u7ml3+koa5uvvlHGurm5p9/pKEubiH5Rxrq3haWf6Shru3MvVVrk4bK28LzjzTUpS0m/0hD3dl41fqkoYI2UQ1g0lAp6yf/SEPlr7/8Iw2Vvn7zT9MekYayX//5RxoqeW3kH2mo3LWTf6ShUtdW/pGGylx7+UcaKnFt5h9pqLy1m3+kodLWdv6RhsraeDXUSUOZbaIa8qShnDao/CMNlbHB5Z9+09ABq1vY573h5g0y//SZho5o4+/5p1fcuMHmn/7SEACD3+qt1cg2ZxoCYOAbfP7pJw0BMOgNI/80bdfxAIxyw8k/i09DAAx2w8o/i05DAAx0w8s/i01DAAxyw8w/jWnocgBGsfEqm60HYPibqDLaBgCGnX9uq7JaTRoCoFP5Z+FpCIDBbPnmKrv1TEMAdCz/LDQNAdC1/LPANATAAHbk9irT7VgHQPfzz4LSEAAdzD8LSUMAdDH/NO3tswHofv6ZfxoCoJv5Z95pCIBWd21VxNYDECD/NO1GAALkn3mlIQA6nH/mk4YAaC3/PFwVtY/TEACdzj9zpyEAup1/5kxDALSTf16uCtyOowDofv6ZIw0B0Pn805yGAOh+/mlMQ1cA0P3807R9AATIP4NeZADXev2hAUx4+5EBlJR/ABhA/rnPu48MoLT8A0D0/ANA9PwDQPT8A0D0/ANAS/lnt7ceGUDJ+QeA6PkHgL7zzwdeeWQA8k9sAL/0viMDkH9iA5B/YgOQf2IDkH9iA5B/YgOQf2IDkH9iA5B/YgOQf2IDOF/+CQ3gEvknNAD5JzYA+Sc0APknNoCD5J/QAOSf2ABWbvF2IwOQf2IDkH9iAzjuVa82MoCT5J/QAOSf2ADkn9gA5J/YAOSf2ADkn9AA5J/YAOSf2ADkn9gADpd/QgNY/ZzXGRmA/BMbwLHyT2gA8k9sAGfIP6EByD+xAcg/sQHIP7EByD+hAcg/sQHIP7EByD+xAcg/sQHIP7EByD+xAcg/sQHIP7EByD/tbPKb8k/svf9d+Sf2pn5Y3vu/xmtrcz+Vf4Lv5rLyz63eWNv77QHyT+z9/mD5J/Ye/Yz8E3t/+az8E3v/+IL8E3s7jpJ/Ym/XV+Sf2Hvja/JP7L2dcRqSf4axfNOQ/DOcTV0h/wTfT+Sf4LtJ/gm+32SWhuSfYe93S+Wf2Ht0hfwTe/mkIflnNNuWSRqSf0a1PNKQ/DO67Tpe/om9PafIP7H39tnyT+xNfkf+ib2py+Wf4Fs/qvd/g7PPYzfKP8F3x5IR5J97nXs+u3/oaWj5Q049pz0y5DQk/+S2J1fKP7G3bY38E3vb18k/sbdzSGlI/sl1w0lD8k++23uW/BN7kxfKP7E3dZn8E3zj8k/wbZB/gu/2AaUh+aeUbRpIGpJ/ytnmAaQh+aekPdF6GpJ/ytrWltOQ/FPa2k1D8k9523mc/BN7e05uLf/822mWuL1ntvP+vy3/FLrJC9p4/z+Qf4rd1KUDzD/vOt98Vvsyrh1Y/nn8Sseez658vO7KxIDyzwPLLnbs+eziZQ/UXbptySDyz90HJgByApAOvLvu2n1L288/t4wlAPICkMZuqbv48PK28891H14GIC8AKV1Xd3Vxaag2/+y7KgGQI4B01b6ay1tXt5h/PrgoAZAngHRR3fdsXj6ytfzz3rkJgFwBpHPfq7nh1WNbyj9vnZ4AyBdAOv2tmjt2n9RK/nn9xARAzgDSia/X3LL3jBbyzyvHJADyBpCOeaXmnsnz+84/L6xNAOQOIK19oe7T90v6zD9Pr0oA5A8grXq67rZr+ss/hyUASgCQDqtNQzf0lX8SAGUASPVp6NYlfeQfAEoB0JCG7j1o8fkHgGIANKShh5YvOv8AUA6AhjS05fDF5h8ASgJQn4aeq0lDR7xU9/XjRQmA8gAsNA3NnX8AKAvAwtLQCXPnHwAKA9CQhk6YfeuhO2punZZ/ACgNQH0a2nHorDvrvm6cnn8AKA5AfRq6e+Z959Tc9uLaBEDJANLaF2s+5JwZt23sfdMzqxIAZQNIq57p/SEbZ9zV+zsAs/IPACUCqEtDz834FvBUr1seXJYAKB9AWvZgrw+Zmv4t4bF3en2a0JgNACgGQDqo16f474zN8U/AxrEEQDcApLGNc/wTkDbtd/36uX5yAIByAKR0/X4fsmnmjwHPurrv6gRAlwCkq2enoVk/JHzPjIsfzP0HAlAWgHTxzDR0z6zLa96YdvG98xIAXQOQzpueht7Y7z+S/PJTn1x88dQEQPcApFM/7f1PfqnH1wobPhLy7s8OTgB0EUA65Bcf/cbP+z8/oOf1g78+/qsfn7lqnr8+AkBxAFL63Deu/vWPTjqklf8+EIACAbQ5AAAwAAwAA8AAMAAMAAPAADAADAADwAAwAAwAA8AAMAAMAAPAADAADAADwAAwAAwAA8AAMAAMAAPAADAADAADwAAwAAwAA8AAaN6bzz/254HsseffBCBzAC/dfNryQT708lNvegGAbAE8dfowHvy0vwKQJYDXvj82nCcf+95rAOQH4Nm1w3v2tc8CkBuAP64Y5sOv+AMAeQH4+7LhPv2yvwGQ0wHs+uKwH3/NvwDIaN8a/vOfAUA++9MInj89AkAu2/fVUQA4YR8AmWxLGsm2AJDJxkcDYByATHb0aAAcDUAe251GtN0AZLFtowKwDYAstnlUADYDkMXuGhWAuwDIYneOCsCdAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwUAD/AR6utT3M2/kZAAAAAElFTkSuQmCC"/>
</defs>
</svg>
