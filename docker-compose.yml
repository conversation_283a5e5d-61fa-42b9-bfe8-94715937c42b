# ============================================
# Docker Compose para Cury Cliente - Next.js
# ============================================
services:
  # Serviço principal da aplicação
  cury-cliente-prod:
    container_name: cury-cliente-prod
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
      args:
        - NODE_ENV=production
        - NEXT_PUBLIC_API_URL=https://homolog.cliente.cury.net/api
      # cache_from:
      #   - cury-cliente-prod:latest
    ports:
      - '3001:3000'
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://homolog.cliente.cury.net/api
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3001
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - backend_cury-cliente
    profiles:
      - production
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  # Serviço para desenvolvimento (opcional)
  cury-cliente-dev:
    container_name: cury-cliente-dev
    build:
      context: .
      dockerfile: Dockerfile
      target: development
      args:
        - NODE_ENV=development
    ports:
      - '3000:3000'
      - '9230:9230' # Debug port alterado para evitar conflito
    volumes:
      - ./src:/app/src:cached
      - ./public:/app/public:cached
      - ./package.json:/app/package.json
      - ./next.config.js:/app/next.config.js:ro
      - ./tailwind.config.ts:/app/tailwind.config.ts:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
    env_file:
      - .env.local
    command: npm run dev-server
    restart: unless-stopped
    networks:
      - backend_cury-cliente
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

networks:
  backend_cury-cliente:
    external: true
# Para usar apenas produção: docker-compose --profile production up
# Para usar desenvolvimento: docker-compose --profile development up
